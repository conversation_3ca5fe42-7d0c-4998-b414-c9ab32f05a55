<!doctype html>
<html lang="en">
   <head>
      @@include("partials/head/meta.html")
      <link rel="stylesheet" href="@@webRoot/node_modules/swiper/swiper-bundle.min.css" />
      @@include("partials/head/head-links.html")
      <title>Landing AI Studio - Responsive Website Template | Block</title>
   </head>
   <body data-bs-theme="dark">
      @@include("partials/navbar.html",{ "classList": "navbar-light w-100" })
      <main>
         <!--Hero section start-->
         <section
            id=""
            class="particals py-md-10 py-5"
            style="background: url(./assets/images/ai-studio/ai-hero-glow.png) no-repeat; background-size: cover; background-position: center"
            data-cue="fadeIn">
            <canvas id="starCanvas"></canvas>
            <div class="container py-xl-10">
               <div class="row py-xl-4">
                  <div class="col-xxl-8 offset-xxl-2 col-xl-8 offset-xl-2 col-lg-10 offset-lg-1 col-12">
                     <div class="text-center d-flex flex-column gap-6" data-cue="zoomIn">
                        <div class="d-flex flex-column gap-3">
                           <h1 class="display-4 mb-0"><span class="gradient-text">Unleash the Power of AI</span></h1>
                           <p class="mb-0 lead px-xxl-8">Create stunning content, generate ideas, and automate tasks with our suite of AI-powered tools designed for creators and businesses.</p>
                        </div>
                        <div class="d-flex flex-row gap-3 justify-content-center">
                           <a href="./signup.html" class="btn btn-primary">Sign up for Free</a>
                           <a href="./index.html" class="btn btn-dark">See demo</a>
                        </div>
                        <div class="d-flex flex-row align-items-center justify-content-center gap-3 mt-2">
                           <div class="avatar-group">
                              <span class="avatar avatar-md">
                                 <img alt="avatar " src="./assets/images/avatar/avatar-1.jpg" class="rounded-circle" />
                              </span>
                              <span class="avatar avatar-md">
                                 <img alt=" avatar" src="./assets/images/avatar/avatar-2.jpg" class="rounded-circle" />
                              </span>
                              <span class="avatar avatar-md">
                                 <img alt=" avatar" src="./assets/images/avatar/avatar-4.jpg" class="rounded-circle" />
                              </span>
                              <span class="avatar avatar-md">
                                 <img alt=" avatar" src="./assets/images/avatar/avatar-3.jpg" class="rounded-circle" />
                              </span>
                           </div>
                           <small class="fw-medium">Join 10,000+ users creating with AI</small>
                        </div>
                     </div>
                  </div>
               </div>
            </div>
         </section>
         <!--Hero section end-->
         <!--Ai product section start-->
         <section class="py-xl-9 pb-lg-9 pt-5 pb-6" data-cue="fadeIn">
            <div class="container">
               <div class="row">
                  <div class="col-12">
                     <div class="text-center mb-xl-7 mb-5 d-flex flex-column gap-2">
                        <h2 class="mb-0"><span class="gradient-text">Our AI Products</span></h2>
                        <p class="mb-0 lead">Powerful AI tools to enhance your creativity and productivity</p>
                     </div>
                  </div>
               </div>
               <div class="row g-5 mb-5">
                  <div class="col-lg-4 col-md-6 col-12" data-cue="fadeIn" data-cue-delay="100">
                     <a href="#!">
                        <div class="card border-gradient bg-transparent">
                           <div class="card-body d-flex flex-column gap-6 p-5">
                              <div class="bg-pink-gradient icon-shape icon-xl rounded-3">
                                 <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <g clip-path="url(#9ed2b4d4)">
                                       <path
                                          d="M14 3V7C14 7.26522 14.1054 7.51957 14.2929 7.70711C14.4804 7.89464 14.7348 8 15 8H19"
                                          stroke="white"
                                          stroke-width="1.5"
                                          stroke-linecap="round"
                                          stroke-linejoin="round"></path>
                                       <path
                                          d="M10 21H7C6.46957 21 5.96086 20.7893 5.58579 20.4142C5.21071 20.0391 5 19.5304 5 19V5C5 4.46957 5.21071 3.96086 5.58579 3.58579C5.96086 3.21071 6.46957 3 7 3H14L19 8V12"
                                          stroke="white"
                                          stroke-width="1.5"
                                          stroke-linecap="round"
                                          stroke-linejoin="round"></path>
                                       <path
                                          d="M14 21V17C14 16.4696 14.2107 15.9609 14.5858 15.5858C14.9609 15.2107 15.4696 15 16 15C16.5304 15 17.0391 15.2107 17.4142 15.5858C17.7893 15.9609 18 16.4696 18 17V21"
                                          stroke="white"
                                          stroke-width="1.5"
                                          stroke-linecap="round"
                                          stroke-linejoin="round"></path>
                                       <path d="M14 19H18" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                       <path d="M21 15V21" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                    </g>
                                    <defs>
                                       <clipPath>
                                          <rect width="24" height="24" fill="white"></rect>
                                       </clipPath>
                                    </defs>
                                 </svg>
                              </div>
                              <div class="d-flex flex-column gap-2">
                                 <h3 class="mb-0 fs-4">AI Writer</h3>
                                 <p class="mb-0">Generate blog posts, marketing copy, and creative stories with ease.</p>
                              </div>
                           </div>
                        </div>
                     </a>
                  </div>
                  <div class="col-lg-4 col-md-6 col-12" data-cue="fadeIn" data-cue-delay="200">
                     <a href="#!">
                        <div class="card border-gradient bg-transparent">
                           <div class="card-body d-flex flex-column gap-6 p-5">
                              <div class="bg-info-gradient icon-shape icon-xl rounded-3">
                                 <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <g clip-path="url(#82b990e2)">
                                       <path d="M15 8H15.01" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                       <path
                                          d="M10 21H6C5.20435 21 4.44129 20.6839 3.87868 20.1213C3.31607 19.5587 3 18.7956 3 18V6C3 5.20435 3.31607 4.44129 3.87868 3.87868C4.44129 3.31607 5.20435 3 6 3H18C18.7956 3 19.5587 3.31607 20.1213 3.87868C20.6839 4.44129 21 5.20435 21 6V11"
                                          stroke="white"
                                          stroke-width="1.5"
                                          stroke-linecap="round"
                                          stroke-linejoin="round"></path>
                                       <path d="M3 16L8 11C8.928 10.107 10.072 10.107 11 11L12 12" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                       <path
                                          d="M14 21V17C14 16.4696 14.2107 15.9609 14.5858 15.5858C14.9609 15.2107 15.4696 15 16 15C16.5304 15 17.0391 15.2107 17.4142 15.5858C17.7893 15.9609 18 16.4696 18 17V21"
                                          stroke="white"
                                          stroke-width="1.5"
                                          stroke-linecap="round"
                                          stroke-linejoin="round"></path>
                                       <path d="M14 19H18" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                       <path d="M21 15V21" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                    </g>
                                    <defs>
                                       <clipPath>
                                          <rect width="24" height="24" fill="white"></rect>
                                       </clipPath>
                                    </defs>
                                 </svg>
                              </div>
                              <div class="d-flex flex-column gap-2">
                                 <h3 class="mb-0 fs-4">AI Image Generator</h3>
                                 <p class="mb-0">Create stunning visuals, art, and designs from text descriptions.</p>
                              </div>
                           </div>
                        </div>
                     </a>
                  </div>
                  <div class="col-lg-4 col-md-6 col-12" data-cue="fadeIn" data-cue-delay="300">
                     <a href="#!">
                        <div class="card border-gradient bg-transparent">
                           <div class="card-body d-flex flex-column gap-6 p-5">
                              <div class="bg-success-gradient icon-shape icon-xl rounded-3">
                                 <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <g clip-path="url(#71372fdc)">
                                       <path
                                          d="M6 5H18C18.5304 5 19.0391 5.21071 19.4142 5.58579C19.7893 5.96086 20 6.46957 20 7V19C20 19.5304 19.7893 20.0391 19.4142 20.4142C19.0391 20.7893 18.5304 21 18 21H6C5.46957 21 4.96086 20.7893 4.58579 20.4142C4.21071 20.0391 4 19.5304 4 19V7C4 6.46957 4.21071 5.96086 4.58579 5.58579C4.96086 5.21071 5.46957 5 6 5Z"
                                          stroke="white"
                                          stroke-width="1.5"
                                          stroke-linecap="round"
                                          stroke-linejoin="round"></path>
                                       <path d="M9 16C10 16.667 11 17 12 17C13 17 14 16.667 15 16" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                       <path d="M9 7L8 3" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                       <path d="M15 7L16 3" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                       <path d="M9 12V11" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                       <path d="M15 12V11" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                    </g>
                                    <defs>
                                       <clipPath>
                                          <rect width="24" height="24" fill="white"></rect>
                                       </clipPath>
                                    </defs>
                                 </svg>
                              </div>
                              <div class="d-flex flex-column gap-2">
                                 <h3 class="mb-0 fs-4">AI Chatbot</h3>
                                 <p class="mb-0">Build conversational AI assistants for customer support and engagement.</p>
                              </div>
                           </div>
                        </div>
                     </a>
                  </div>
               </div>
               <div class="row gy-5">
                  <div class="col-lg-8" data-cue="fadeIn" data-cue-delay="400">
                     <div class="row g-5">
                        <div class="col-lg-12 col-md-6 col-12">
                           <a href="#!">
                              <div class="card border-gradient bg-transparent">
                                 <div class="card-body d-flex flex-column gap-6 p-5">
                                    <div class="bg-orange-gradient icon-shape icon-xl rounded-3">
                                       <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                          <g clip-path="url(#94d14284)">
                                             <path
                                                d="M3 17C3 17.7956 3.31607 18.5587 3.87868 19.1213C4.44129 19.6839 5.20435 20 6 20C6.79565 20 7.55871 19.6839 8.12132 19.1213C8.68393 18.5587 9 17.7956 9 17C9 16.2044 8.68393 15.4413 8.12132 14.8787C7.55871 14.3161 6.79565 14 6 14C5.20435 14 4.44129 14.3161 3.87868 14.8787C3.31607 15.4413 3 16.2044 3 17Z"
                                                stroke="white"
                                                stroke-width="1.5"
                                                stroke-linecap="round"
                                                stroke-linejoin="round"></path>
                                             <path d="M9 17V4H19V12" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                             <path d="M9 8H19" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                             <path d="M19 16L17 19H21L19 22" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                          </g>
                                          <defs>
                                             <clipPath>
                                                <rect width="24" height="24" fill="white"></rect>
                                             </clipPath>
                                          </defs>
                                       </svg>
                                    </div>
                                    <div class="d-flex flex-column gap-2">
                                       <h3 class="mb-0 fs-4">AI Video Generator</h3>
                                       <p class="mb-0">Transform text into engaging video content with customizable styles.</p>
                                    </div>
                                 </div>
                              </div>
                           </a>
                        </div>
                        <div class="col-lg-6 col-md-6 col-12">
                           <a href="#!">
                              <div class="card border-gradient bg-transparent">
                                 <div class="card-body d-flex flex-column gap-6 p-5">
                                    <div class="bg-blue-gradient icon-shape icon-xl rounded-3">
                                       <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                          <g clip-path="url(#6e6bd9ef)">
                                             <path
                                                d="M14 3V7C14 7.26522 14.1054 7.51957 14.2929 7.70711C14.4804 7.89464 14.7348 8 15 8H19"
                                                stroke="white"
                                                stroke-width="1.5"
                                                stroke-linecap="round"
                                                stroke-linejoin="round"></path>
                                             <path
                                                d="M12 21H7C6.46957 21 5.96086 20.7893 5.58579 20.4142C5.21071 20.0391 5 19.5304 5 19V5C5 4.46957 5.21071 3.96086 5.58579 3.58579C5.96086 3.21071 6.46957 3 7 3H14L19 8V11.5"
                                                stroke="white"
                                                stroke-width="1.5"
                                                stroke-linecap="round"
                                                stroke-linejoin="round"></path>
                                             <path d="M9 9H10" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                             <path d="M9 13H15" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                             <path d="M9 17H12" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                             <path
                                                d="M19 22.5C19.2053 21.6513 19.6406 20.8755 20.2581 20.2581C20.8755 19.6406 21.6513 19.2053 22.5 19C21.6513 18.7947 20.8755 18.3594 20.2581 17.7419C19.6406 17.1245 19.2053 16.3487 19 15.5C18.7947 16.3487 18.3594 17.1245 17.7419 17.7419C17.1245 18.3594 16.3487 18.7947 15.5 19C16.3487 19.2053 17.1245 19.6406 17.7419 20.2581C18.3594 20.8755 18.7947 21.6513 19 22.5Z"
                                                stroke="white"
                                                stroke-width="1.5"
                                                stroke-linecap="round"
                                                stroke-linejoin="round"></path>
                                          </g>
                                          <defs>
                                             <clipPath>
                                                <rect width="24" height="24" fill="white"></rect>
                                             </clipPath>
                                          </defs>
                                       </svg>
                                    </div>
                                    <div class="d-flex flex-column gap-2">
                                       <h3 class="mb-0 fs-4">AI Speech-to-Text</h3>
                                       <p class="mb-0">Convert audio recordings into accurate text transcriptions.</p>
                                    </div>
                                 </div>
                              </div>
                           </a>
                        </div>
                        <div class="col-lg-6 col-md-6 col-12">
                           <a href="#!">
                              <div class="card border-gradient bg-transparent">
                                 <div class="card-body d-flex flex-column gap-6 p-5">
                                    <div class="bg-pinks-gradient icon-shape icon-xl rounded-3">
                                       <svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                          <g clip-path="url(#ad35a9a2)">
                                             <path
                                                d="M6.5 6C6.5 5.46957 6.71071 4.96086 7.08579 4.58579C7.46086 4.21071 7.96957 4 8.5 4H16.5C17.0304 4 17.5391 4.21071 17.9142 4.58579C18.2893 4.96086 18.5 5.46957 18.5 6V10C18.5 10.5304 18.2893 11.0391 17.9142 11.4142C17.5391 11.7893 17.0304 12 16.5 12H8.5C7.96957 12 7.46086 11.7893 7.08579 11.4142C6.71071 11.0391 6.5 10.5304 6.5 10V6Z"
                                                stroke="white"
                                                stroke-width="1.5"
                                                stroke-linecap="round"
                                                stroke-linejoin="round"></path>
                                             <path d="M12.5 2V4" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                             <path d="M9.5 12V21" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                             <path d="M15.5 12V21" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                             <path d="M5.5 16L9.5 14" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                             <path d="M15.5 14L19.5 16" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                             <path d="M9.5 18H15.5" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                             <path d="M10.5 8V8.01" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                             <path d="M14.5 8V8.01" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                          </g>
                                          <defs>
                                             <clipPath>
                                                <rect width="24" height="24" fill="white" transform="translate(0.5)"></rect>
                                             </clipPath>
                                          </defs>
                                       </svg>
                                    </div>
                                    <div class="d-flex flex-column gap-2">
                                       <h3 class="mb-0 fs-4">AI Code Generator</h3>
                                       <p class="mb-0">Write, debug, and optimize code across multiple programming languages.</p>
                                    </div>
                                 </div>
                              </div>
                           </a>
                        </div>
                     </div>
                  </div>
                  <div class="col-lg-4 col-md-6" data-cue="fadeIn" data-cue-delay="500">
                     <a href="#!">
                        <div class="card h-100 border-gradient bg-transparent">
                           <div class="card-body d-flex flex-column gap-6 p-5">
                              <div class="bg-purle-gradient icon-shape icon-xl rounded-3">
                                 <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <g clip-path="url(#a0f45168)">
                                       <path
                                          d="M9 15H6C5.20435 15 4.44129 14.6839 3.87868 14.1213C3.31607 13.5587 3 12.7956 3 12V6C3 5.20435 3.31607 4.44129 3.87868 3.87868C4.44129 3.31607 5.20435 3 6 3H12C12.7956 3 13.5587 3.31607 14.1213 3.87868C14.6839 4.44129 15 5.20435 15 6V9"
                                          stroke="white"
                                          stroke-width="1.5"
                                          stroke-linecap="round"
                                          stroke-linejoin="round"></path>
                                       <path
                                          d="M9 12C9 11.2044 9.31607 10.4413 9.87868 9.87868C10.4413 9.31607 11.2044 9 12 9H18C18.7956 9 19.5587 9.31607 20.1213 9.87868C20.6839 10.4413 21 11.2044 21 12V18C21 18.7956 20.6839 19.5587 20.1213 20.1213C19.5587 20.6839 18.7956 21 18 21H12C11.2044 21 10.4413 20.6839 9.87868 20.1213C9.31607 19.5587 9 18.7956 9 18V12Z"
                                          stroke="white"
                                          stroke-width="1.5"
                                          stroke-linecap="round"
                                          stroke-linejoin="round"></path>
                                       <path
                                          d="M3 12L5.296 9.70404C5.74795 9.25215 6.36089 8.99829 7 8.99829C7.63911 8.99829 8.25205 9.25215 8.704 9.70404L9 10"
                                          stroke="white"
                                          stroke-width="1.5"
                                          stroke-linecap="round"
                                          stroke-linejoin="round"></path>
                                       <path d="M14 13.5V16.5L16.5 15L14 13.5Z" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                       <path d="M7 6V6.01" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                    </g>
                                    <defs>
                                       <clipPath>
                                          <rect width="24" height="24" fill="white"></rect>
                                       </clipPath>
                                    </defs>
                                 </svg>
                              </div>
                              <div class="d-flex flex-column gap-2">
                                 <h3 class="mb-0 fs-4">AI Video Generator</h3>
                                 <p class="mb-0">Transform text into engaging video content with customizable styles.</p>
                              </div>
                           </div>
                        </div>
                     </a>
                  </div>
               </div>
            </div>
         </section>
         <!--Ai product section end-->
         <!--How it work start-->
         <section class="py-xl-9 py-lg-7 py-5" data-cue="fadeIn">
            <div class="container">
               <div class="row">
                  <div class="col-12">
                     <div class="text-center mb-xl-7 mb-5 d-flex flex-column gap-2">
                        <h2 class="mb-0"><span class="gradient-text">How It Works</span></h2>
                        <p class="mb-0 lead">Get started with our AI tools in just a few simple steps</p>
                     </div>
                  </div>
               </div>
               <div class="row gy-5 gy-md-0 process-step gx-6">
                  <div class="col-lg-4 col-md-6 col-12" data-cue="fadeIn" data-duration="1000">
                     <div class="d-flex flex-column gap-6 p-xxl-6 p-md-4 text-center">
                        <div class="line">
                           <div class="icon-shape icon-md text-dark fw-semibold fs-4 mx-auto border-gradient-mix-color">1</div>
                        </div>
                        <div class="d-flex flex-column gap-2">
                           <h3 class="fs-4 mb-0">Sign Up &amp; Choose a Plan</h3>
                           <p class="mb-0">Get started in minutes by selecting the plan that fits your needs.</p>
                        </div>
                     </div>
                  </div>
                  <div class="col-lg-4 col-md-6 col-12" data-cue="fadeIn" data-duration="1500">
                     <div class="d-flex flex-column gap-6 p-xxl-6 p-md-4 text-center">
                        <div class="line">
                           <div class="icon-shape icon-md text-dark fw-semibold fs-4 mx-auto border-gradient-mix-color">2</div>
                        </div>
                        <div class="d-flex flex-column gap-2">
                           <h3 class="fs-4 mb-0">Access AI Tools &amp; Customize</h3>
                           <p class="mb-0">Use our AI-driven tools to create content, analyze data, and more.</p>
                        </div>
                     </div>
                  </div>
                  <div class="col-lg-4 col-md-6 col-12" data-cue="fadeIn" data-duration="2000">
                     <div class="d-flex flex-column gap-6 p-xxl-6 p-md-4 text-center">
                        <div class="line">
                           <div class="icon-shape icon-md text-dark fw-semibold fs-4 mx-auto border-gradient-mix-color">3</div>
                        </div>
                        <div class="d-flex flex-column gap-2">
                           <h3 class="fs-4 mb-0">Generate &amp; Download Results</h3>
                           <p class="mb-0">Download high-quality outputs for your projects effortlessly.</p>
                        </div>
                     </div>
                  </div>
               </div>
            </div>
         </section>
         <!--How it work end-->
         <!--Testimonial start-->
         <section class="py-xl-9 py-lg-7 py-5" data-cue="fadeIn">
            <div class="container-fluid">
               <div class="row">
                  <div class="col-12">
                     <div class="text-center mb-xl-7 mb-5 d-flex flex-column gap-2">
                        <h2 class="mb-0"><span class="gradient-text">What Our Clients Say</span></h2>
                        <p class="mb-0 lead">Trusted by creators and businesses worldwide</p>
                     </div>
                  </div>
               </div>
               <div class="embla" data-cue="zoomIn">
                  <div class="row">
                     <div class="embla__viewport mb-4" style="width: 100%">
                        <div class="embla__container d-flex flex-row">
                           <div class="embla__slide ms-4">
                              <div class="d-flex flex-column gap-4">
                                 <a href="#!">
                                    <div class="card card-gradient bg-gray-950">
                                       <div class="card-body d-flex flex-column gap-5">
                                          <p class="mb-0">The AI image generator has been a game-changer for our design team. We can quickly iterate on concepts that used to take days.</p>
                                          <div class="d-flex flex-row gap-3 align-content-center">
                                             <img src="./assets/images/avatar/avatar-2.jpg" alt="avatar 1" class="avatar avatar-md rounded-circle" />
                                             <div class="">
                                                <h3 class="fs-5 mb-0">Michael Chen</h3>
                                                <small>Marketing Director,TechGrowth</small>
                                             </div>
                                          </div>
                                       </div>
                                    </div>
                                 </a>
                                 <a href="#!">
                                    <div class="card card-gradient bg-gray-950">
                                       <div class="card-body d-flex flex-column gap-5">
                                          <p class="mb-0">As a solo entrepreneur, these AI tools have given me capabilities that would normally require an entire team. Incredible value.</p>
                                          <div class="d-flex flex-row gap-3 align-content-center">
                                             <img src="./assets/images/avatar/avatar-5.jpg" alt="avatar 1" class="avatar avatar-md rounded-circle" />
                                             <div class="">
                                                <h3 class="fs-5 mb-0">Priya Patel</h3>
                                                <small>Founder,NexGen Solutions</small>
                                             </div>
                                          </div>
                                       </div>
                                    </div>
                                 </a>
                              </div>
                           </div>
                           <div class="embla__slide">
                              <div class="d-flex flex-column gap-4">
                                 <a href="#!">
                                    <div class="card card-gradient bg-gray-950">
                                       <div class="card-body d-flex flex-column gap-5">
                                          <p class="mb-0">The AI image generator has been a game-changer for our design team. We can quickly iterate on concepts that used to take days.</p>
                                          <div class="d-flex flex-row gap-3 align-content-center">
                                             <img src="./assets/images/avatar/avatar-2.jpg" alt="avatar 1" class="avatar avatar-md rounded-circle" />
                                             <div class="">
                                                <h3 class="fs-5 mb-0">Michael Chen</h3>
                                                <small>Marketing Director,TechGrowth</small>
                                             </div>
                                          </div>
                                       </div>
                                    </div>
                                 </a>
                                 <a href="#!">
                                    <div class="card card-gradient bg-gray-950">
                                       <div class="card-body d-flex flex-column gap-5">
                                          <p class="mb-0">As a solo entrepreneur, these AI tools have given me capabilities that would normally require an entire team. Incredible value.</p>
                                          <div class="d-flex flex-row gap-3 align-content-center">
                                             <img src="./assets/images/avatar/avatar-5.jpg" alt="avatar 1" class="avatar avatar-md rounded-circle" />
                                             <div class="">
                                                <h3 class="fs-5 mb-0">Priya Patel</h3>
                                                <small>Founder,NexGen Solutions</small>
                                             </div>
                                          </div>
                                       </div>
                                    </div>
                                 </a>
                              </div>
                           </div>
                           <div class="embla__slide">
                              <div class="d-flex flex-column gap-4">
                                 <a href="#!">
                                    <div class="card card-gradient bg-gray-950">
                                       <div class="card-body d-flex flex-column gap-5">
                                          <p class="mb-0">
                                             Lorem ipsum dolor sit amet consectetur adipisicing elit. Amet dolore aspernatur architecto omnis ipsam minima nulla quae. Provident repudiandae quaerat
                                             ducimus possimus amet. Ducimus, neque?
                                          </p>
                                          <div class="d-flex flex-row gap-3 align-content-center">
                                             <img src="./assets/images/avatar/avatar-3.jpg" alt="avatar 1" class="avatar avatar-md rounded-circle" />
                                             <div class="">
                                                <h3 class="fs-5 mb-0">Ryan Michael</h3>
                                                <small>Director</small>
                                             </div>
                                          </div>
                                       </div>
                                    </div>
                                 </a>
                                 <a href="#!">
                                    <div class="card card-gradient bg-gray-950">
                                       <div class="card-body d-flex flex-column gap-5">
                                          <p class="mb-0">The AI image generator has been a game-changer for our design team. We can quickly iterate on concepts that used to take days.</p>
                                          <div class="d-flex flex-row gap-3 align-content-center">
                                             <img src="./assets/images/avatar/avatar-2.jpg" alt="avatar 1" class="avatar avatar-md rounded-circle" />
                                             <div class="">
                                                <h3 class="fs-5 mb-0">Michael Chen</h3>
                                                <small>Marketing Director,TechGrowth</small>
                                             </div>
                                          </div>
                                       </div>
                                    </div>
                                 </a>
                              </div>
                           </div>
                           <div class="embla__slide">
                              <div class="d-flex flex-column gap-4">
                                 <a href="#!">
                                    <div class="card card-gradient bg-gray-950">
                                       <div class="card-body d-flex flex-column gap-5">
                                          <p class="mb-0">
                                             The predictive analytics feature has allowed us to make data-driven decisions with confidence, leading to a 40% increase in our campaign ROI.
                                          </p>
                                          <div class="d-flex flex-row gap-3 align-content-center">
                                             <img src="./assets/images/avatar/avatar-8.jpg" alt="avatar 1" class="avatar avatar-md rounded-circle" />
                                             <div class="">
                                                <h3 class="fs-5 mb-0">James Rodriguez</h3>
                                                <small>Data Analyst,Insight Innovations</small>
                                             </div>
                                          </div>
                                       </div>
                                    </div>
                                 </a>
                                 <a href="#!">
                                    <div class="card card-gradient bg-gray-950">
                                       <div class="card-body d-flex flex-column gap-5">
                                          <p class="mb-0">
                                             Lorem ipsum dolor sit amet consectetur adipisicing elit. Amet dolore aspernatur architecto omnis ipsam minima nulla quae. Provident repudiandae quaerat
                                             ducimus possimus amet. Ducimus, neque?
                                          </p>
                                          <div class="d-flex flex-row gap-3 align-content-center">
                                             <img src="./assets/images/avatar/avatar-3.jpg" alt="avatar 1" class="avatar avatar-md rounded-circle" />
                                             <div class="">
                                                <h3 class="fs-5 mb-0">Ryan Michael</h3>
                                                <small>Director</small>
                                             </div>
                                          </div>
                                       </div>
                                    </div>
                                 </a>
                              </div>
                           </div>
                           <div class="embla__slide">
                              <div class="d-flex flex-column gap-4">
                                 <a href="#!">
                                    <div class="card card-gradient bg-gray-950">
                                       <div class="card-body d-flex flex-column gap-5">
                                          <p class="mb-0">The AI image generator has been a game-changer for our design team. We can quickly iterate on concepts that used to take days.</p>
                                          <div class="d-flex flex-row gap-3 align-content-center">
                                             <img src="./assets/images/avatar/avatar-2.jpg" alt="avatar 1" class="avatar avatar-md rounded-circle" />
                                             <div class="">
                                                <h3 class="fs-5 mb-0">Michael Chen</h3>
                                                <small>Marketing Director,TechGrowth</small>
                                             </div>
                                          </div>
                                       </div>
                                    </div>
                                 </a>
                                 <a href="#!">
                                    <div class="card card-gradient bg-gray-950">
                                       <div class="card-body d-flex flex-column gap-5">
                                          <p class="mb-0">Lorem ipsum, dolor sit amet consectetur adipisicing elit. Et vel, maxime odio totam temporibus laborum aliquid quis nam!</p>
                                          <div class="d-flex flex-row gap-3 align-content-center">
                                             <img src="./assets/images/avatar/avatar-1.jpg" alt="avatar 1" class="avatar avatar-md rounded-circle" />
                                             <div class="">
                                                <h3 class="fs-5 mb-0">John Deo</h3>
                                                <small>CTO,TechGrowth</small>
                                             </div>
                                          </div>
                                       </div>
                                    </div>
                                 </a>
                              </div>
                           </div>
                           <div class="embla__slide">
                              <div class="d-flex flex-column gap-4">
                                 <a href="#!">
                                    <div class="card card-gradient bg-gray-950">
                                       <div class="card-body d-flex flex-column gap-5">
                                          <p class="mb-0">Lorem ipsum, dolor sit amet consectetur adipisicing elit. Et vel, maxime odio totam temporibus laborum aliquid quis nam!</p>
                                          <div class="d-flex flex-row gap-3 align-content-center">
                                             <img src="./assets/images/avatar/avatar-1.jpg" alt="avatar 1" class="avatar avatar-md rounded-circle" />
                                             <div class="">
                                                <h3 class="fs-5 mb-0">John Deo</h3>
                                                <small>CTO,TechGrowth</small>
                                             </div>
                                          </div>
                                       </div>
                                    </div>
                                 </a>
                                 <a href="#!">
                                    <div class="card card-gradient bg-gray-950">
                                       <div class="card-body d-flex flex-column gap-5">
                                          <p class="mb-0">
                                             Lorem ipsum dolor sit amet consectetur adipisicing elit. Nobis rerum dolorum quae vel, doloribus itaque in, sequi quibusdam animi ipsa, laborum sit?
                                          </p>
                                          <div class="d-flex flex-row gap-3 align-content-center">
                                             <img src="./assets/images/avatar/avatar-6.jpg" alt="avatar 1" class="avatar avatar-md rounded-circle" />
                                             <div class="">
                                                <h3 class="fs-5 mb-0">Misty Roy</h3>
                                                <small>Manager,AI Studio</small>
                                             </div>
                                          </div>
                                       </div>
                                    </div>
                                 </a>
                              </div>
                           </div>
                           <div class="embla__slide">
                              <div class="d-flex flex-column gap-4">
                                 <a href="#!">
                                    <div class="card card-gradient bg-gray-950">
                                       <div class="card-body d-flex flex-column gap-5">
                                          <p class="mb-0">
                                             Lorem ipsum dolor sit amet consectetur adipisicing elit. Nobis rerum dolorum quae vel, doloribus itaque in, sequi quibusdam animi ipsa, laborum sit?
                                          </p>
                                          <div class="d-flex flex-row gap-3 align-content-center">
                                             <img src="./assets/images/avatar/avatar-6.jpg" alt="avatar 1" class="avatar avatar-md rounded-circle" />
                                             <div class="">
                                                <h3 class="fs-5 mb-0">Misty Roy</h3>
                                                <small>Manager,AI Studio</small>
                                             </div>
                                          </div>
                                       </div>
                                    </div>
                                 </a>
                                 <a href="#!">
                                    <div class="card card-gradient bg-gray-950">
                                       <div class="card-body d-flex flex-column gap-5">
                                          <p class="mb-0">
                                             Lorem ipsum dolor sit amet consectetur adipisicing elit. Iusto, debitis itaque impedit, officiis atque aut delectus deleniti dolorum explicabo sequi dicta
                                             neque nihil voluptate similique repellat quidem quas quo molestiae!
                                          </p>
                                          <div class="d-flex flex-row gap-3 align-content-center">
                                             <img src="./assets/images/avatar/avatar-3.jpg" alt="avatar 1" class="avatar avatar-md rounded-circle" />
                                             <div class="">
                                                <h3 class="fs-5 mb-0">Sherleen Khoise</h3>
                                                <small>PO,AI Studio</small>
                                             </div>
                                          </div>
                                       </div>
                                    </div>
                                 </a>
                              </div>
                           </div>
                           <div class="embla__slide">
                              <div class="d-flex flex-column gap-4">
                                 <a href="#!">
                                    <div class="card card-gradient bg-gray-950">
                                       <div class="card-body d-flex flex-column gap-5">
                                          <p class="mb-0">
                                             Lorem ipsum dolor sit amet consectetur adipisicing elit. Iusto, debitis itaque impedit, officiis atque aut delectus deleniti dolorum explicabo sequi dicta
                                             neque nihil voluptate similique repellat quidem quas quo molestiae!
                                          </p>
                                          <div class="d-flex flex-row gap-3 align-content-center">
                                             <img src="./assets/images/avatar/avatar-3.jpg" alt="avatar 1" class="avatar avatar-md rounded-circle" />
                                             <div class="">
                                                <h3 class="fs-5 mb-0">Sherleen Khoise</h3>
                                                <small>PO,AI Studio</small>
                                             </div>
                                          </div>
                                       </div>
                                    </div>
                                 </a>
                                 <a href="#!">
                                    <div class="card card-gradient bg-gray-950">
                                       <div class="card-body d-flex flex-column gap-5">
                                          <p class="mb-0">
                                             Lorem ipsum dolor sit amet consectetur adipisicing elit. Nobis rerum dolorum quae vel, doloribus itaque in, sequi quibusdam animi ipsa, laborum sit?
                                          </p>
                                          <div class="d-flex flex-row gap-3 align-content-center">
                                             <img src="./assets/images/avatar/avatar-6.jpg" alt="avatar 1" class="avatar avatar-md rounded-circle" />
                                             <div class="">
                                                <h3 class="fs-5 mb-0">Misty Roy</h3>
                                                <small>Manager,AI Studio</small>
                                             </div>
                                          </div>
                                       </div>
                                    </div>
                                 </a>
                              </div>
                           </div>
                        </div>
                     </div>
                     <div class="embla__dots"></div>
                  </div>
               </div>
            </div>
         </section>
         <!--Testimonial end-->
         <!--Pricing section start-->
         <section class="py-xl-9 py-lg-7 py-5" data-cue="fadeIn">
            <div class="container pb-xl-5">
               <div class="row">
                  <div class="col-12">
                     <div class="text-center mb-xl-7 mb-5 d-flex flex-column gap-2">
                        <h2 class="mb-0"><span class="gradient-text">Pricing Plans</span></h2>
                        <p class="mb-0 lead">Choose the perfect plan for your needs</p>
                     </div>
                  </div>
               </div>
               <div class="row gy-5 gy-xl-0">
                  <div class="col-xl-4 col-md-6 col-12" data-cue="slideInLeft">
                     <div class="card bg-gray-950">
                        <div class="card-body d-flex flex-column gap-6 p-5">
                           <div class="d-flex flex-column gap-3">
                              <div class="d-flex flex-column gap-1">
                                 <h3 class="mb-0">Free</h3>
                                 <p class="mb-0">Basic access to essential AI tools</p>
                              </div>
                              <div class="d-flex flex-row align-items-center gap-2">
                                 <h3 class="fs-1 fw-bold mb-0">$0</h3>
                                 <span>forever</span>
                              </div>
                              <ul class="list-unstyled mb-0 d-flex flex-column gap-2">
                                 <li class="d-flex flex-row gap-2 align-items-center">
                                    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                       <g clip-path="url(#4997e58d)">
                                          <path d="M5.83333 9.99992L9.99999 14.1666L18.3333 5.83325" stroke="#6FDB93" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                          <path
                                             d="M1.66667 9.99992L5.83334 14.1666M10 9.99992L14.1667 5.83325"
                                             stroke="#6FDB93"
                                             stroke-width="1.5"
                                             stroke-linecap="round"
                                             stroke-linejoin="round"></path>
                                       </g>
                                       <defs>
                                          <clipPath>
                                             <rect width="20" height="20" fill="white"></rect>
                                          </clipPath>
                                       </defs>
                                    </svg>
                                    <span>5 AI generations per day</span>
                                 </li>
                                 <li class="d-flex flex-row gap-2 align-items-center">
                                    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                       <g clip-path="url(#4997e58d)">
                                          <path d="M5.83333 9.99992L9.99999 14.1666L18.3333 5.83325" stroke="#6FDB93" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                          <path
                                             d="M1.66667 9.99992L5.83334 14.1666M10 9.99992L14.1667 5.83325"
                                             stroke="#6FDB93"
                                             stroke-width="1.5"
                                             stroke-linecap="round"
                                             stroke-linejoin="round"></path>
                                       </g>
                                       <defs>
                                          <clipPath>
                                             <rect width="20" height="20" fill="white"></rect>
                                          </clipPath>
                                       </defs>
                                    </svg>
                                    <span>Basic text generation</span>
                                 </li>
                                 <li class="d-flex flex-row gap-2 align-items-center">
                                    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                       <g clip-path="url(#4997e58d)">
                                          <path d="M5.83333 9.99992L9.99999 14.1666L18.3333 5.83325" stroke="#6FDB93" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                          <path
                                             d="M1.66667 9.99992L5.83334 14.1666M10 9.99992L14.1667 5.83325"
                                             stroke="#6FDB93"
                                             stroke-width="1.5"
                                             stroke-linecap="round"
                                             stroke-linejoin="round"></path>
                                       </g>
                                       <defs>
                                          <clipPath>
                                             <rect width="20" height="20" fill="white"></rect>
                                          </clipPath>
                                       </defs>
                                    </svg>
                                    <span>Simple image creation</span>
                                 </li>
                                 <li class="d-flex flex-row gap-2 align-items-center">
                                    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                       <g clip-path="url(#4997e58d)">
                                          <path d="M5.83333 9.99992L9.99999 14.1666L18.3333 5.83325" stroke="#6FDB93" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                          <path
                                             d="M1.66667 9.99992L5.83334 14.1666M10 9.99992L14.1667 5.83325"
                                             stroke="#6FDB93"
                                             stroke-width="1.5"
                                             stroke-linecap="round"
                                             stroke-linejoin="round"></path>
                                       </g>
                                       <defs>
                                          <clipPath>
                                             <rect width="20" height="20" fill="white"></rect>
                                          </clipPath>
                                       </defs>
                                    </svg>
                                    <span>Standard response time</span>
                                 </li>
                                 <li class="d-flex flex-row gap-2 align-items-center">
                                    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                       <g clip-path="url(#4997e58d)">
                                          <path d="M5.83333 9.99992L9.99999 14.1666L18.3333 5.83325" stroke="#6FDB93" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                          <path
                                             d="M1.66667 9.99992L5.83334 14.1666M10 9.99992L14.1667 5.83325"
                                             stroke="#6FDB93"
                                             stroke-width="1.5"
                                             stroke-linecap="round"
                                             stroke-linejoin="round"></path>
                                       </g>
                                       <defs>
                                          <clipPath>
                                             <rect width="20" height="20" fill="white"></rect>
                                          </clipPath>
                                       </defs>
                                    </svg>
                                    <span>Community support</span>
                                 </li>
                              </ul>
                           </div>
                           <div>
                              <a href="#!" class="btn btn-dark">Get Started</a>
                           </div>
                        </div>
                     </div>
                  </div>
                  <div class="col-xl-4 col-md-6 col-12" data-cue="zoomOut">
                     <div class="card position-relative bg-gray-950 border-gradient-mix-color">
                        <div class="card-body d-flex flex-column gap-6 p-5 z-2">
                           <div class="d-flex flex-column gap-3">
                              <div class="position-absolute top-0 end-0 translate-middle">
                                 <span class="badge bg-primary rounded-pill fw-medium border border-dark border-2">Most Popular</span>
                              </div>
                              <div class="d-flex flex-column gap-1">
                                 <h3 class="mb-0">Pro</h3>
                                 <p class="mb-0">Advanced features for professionals</p>
                              </div>
                              <div class="d-flex flex-row align-items-center gap-2">
                                 <h3 class="fs-1 fw-bold mb-0">$29</h3>
                                 <span>per month</span>
                              </div>
                              <ul class="list-unstyled mb-0 d-flex flex-column gap-2">
                                 <li class="d-flex flex-row gap-2 align-items-center">
                                    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                       <g clip-path="url(#4997e58d)">
                                          <path d="M5.83333 9.99992L9.99999 14.1666L18.3333 5.83325" stroke="#6FDB93" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                          <path
                                             d="M1.66667 9.99992L5.83334 14.1666M10 9.99992L14.1667 5.83325"
                                             stroke="#6FDB93"
                                             stroke-width="1.5"
                                             stroke-linecap="round"
                                             stroke-linejoin="round"></path>
                                       </g>
                                       <defs>
                                          <clipPath>
                                             <rect width="20" height="20" fill="white"></rect>
                                          </clipPath>
                                       </defs>
                                    </svg>
                                    <span>Unlimited AI generations</span>
                                 </li>
                                 <li class="d-flex flex-row gap-2 align-items-center">
                                    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                       <g clip-path="url(#4997e58d)">
                                          <path d="M5.83333 9.99992L9.99999 14.1666L18.3333 5.83325" stroke="#6FDB93" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                          <path
                                             d="M1.66667 9.99992L5.83334 14.1666M10 9.99992L14.1667 5.83325"
                                             stroke="#6FDB93"
                                             stroke-width="1.5"
                                             stroke-linecap="round"
                                             stroke-linejoin="round"></path>
                                       </g>
                                       <defs>
                                          <clipPath>
                                             <rect width="20" height="20" fill="white"></rect>
                                          </clipPath>
                                       </defs>
                                    </svg>
                                    <span>Advanced text &amp; image tools</span>
                                 </li>
                                 <li class="d-flex flex-row gap-2 align-items-center">
                                    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                       <g clip-path="url(#4997e58d)">
                                          <path d="M5.83333 9.99992L9.99999 14.1666L18.3333 5.83325" stroke="#6FDB93" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                          <path
                                             d="M1.66667 9.99992L5.83334 14.1666M10 9.99992L14.1667 5.83325"
                                             stroke="#6FDB93"
                                             stroke-width="1.5"
                                             stroke-linecap="round"
                                             stroke-linejoin="round"></path>
                                       </g>
                                       <defs>
                                          <clipPath>
                                             <rect width="20" height="20" fill="white"></rect>
                                          </clipPath>
                                       </defs>
                                    </svg>
                                    <span>Audio &amp; video generation</span>
                                 </li>
                                 <li class="d-flex flex-row gap-2 align-items-center">
                                    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                       <g clip-path="url(#4997e58d)">
                                          <path d="M5.83333 9.99992L9.99999 14.1666L18.3333 5.83325" stroke="#6FDB93" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                          <path
                                             d="M1.66667 9.99992L5.83334 14.1666M10 9.99992L14.1667 5.83325"
                                             stroke="#6FDB93"
                                             stroke-width="1.5"
                                             stroke-linecap="round"
                                             stroke-linejoin="round"></path>
                                       </g>
                                       <defs>
                                          <clipPath>
                                             <rect width="20" height="20" fill="white"></rect>
                                          </clipPath>
                                       </defs>
                                    </svg>
                                    <span>Priority processing</span>
                                 </li>
                                 <li class="d-flex flex-row gap-2 align-items-center">
                                    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                       <g clip-path="url(#4997e58d)">
                                          <path d="M5.83333 9.99992L9.99999 14.1666L18.3333 5.83325" stroke="#6FDB93" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                          <path
                                             d="M1.66667 9.99992L5.83334 14.1666M10 9.99992L14.1667 5.83325"
                                             stroke="#6FDB93"
                                             stroke-width="1.5"
                                             stroke-linecap="round"
                                             stroke-linejoin="round"></path>
                                       </g>
                                       <defs>
                                          <clipPath>
                                             <rect width="20" height="20" fill="white"></rect>
                                          </clipPath>
                                       </defs>
                                    </svg>
                                    <span>Email support</span>
                                 </li>
                                 <li class="d-flex flex-row gap-2 align-items-center">
                                    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                       <g clip-path="url(#4997e58d)">
                                          <path d="M5.83333 9.99992L9.99999 14.1666L18.3333 5.83325" stroke="#6FDB93" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                          <path
                                             d="M1.66667 9.99992L5.83334 14.1666M10 9.99992L14.1667 5.83325"
                                             stroke="#6FDB93"
                                             stroke-width="1.5"
                                             stroke-linecap="round"
                                             stroke-linejoin="round"></path>
                                       </g>
                                       <defs>
                                          <clipPath>
                                             <rect width="20" height="20" fill="white"></rect>
                                          </clipPath>
                                       </defs>
                                    </svg>
                                    <span>API access</span>
                                 </li>
                              </ul>
                           </div>
                           <div>
                              <a href="#!" class="btn btn-primary">Start Free Trial</a>
                           </div>
                        </div>
                     </div>
                  </div>
                  <div class="col-xl-4 col-md-6 col-12" data-cue="slideInRight">
                     <div class="card bg-gray-950">
                        <div class="card-body d-flex flex-column gap-6 p-5">
                           <div class="d-flex flex-column gap-3">
                              <div class="d-flex flex-column gap-1">
                                 <h3 class="mb-0">Enterprise</h3>
                                 <p class="mb-0">Custom solutions for teams</p>
                              </div>
                              <div class="d-flex flex-row gap-2">
                                 <h3 class="fs-1 fw-bold mb-0">Custom</h3>
                                 <span class="mt-auto">pricing</span>
                              </div>
                              <ul class="list-unstyled mb-0 d-flex flex-column gap-2">
                                 <li class="d-flex flex-row gap-2 align-items-center">
                                    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                       <g clip-path="url(#4997e58d)">
                                          <path d="M5.83333 9.99992L9.99999 14.1666L18.3333 5.83325" stroke="#6FDB93" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                          <path
                                             d="M1.66667 9.99992L5.83334 14.1666M10 9.99992L14.1667 5.83325"
                                             stroke="#6FDB93"
                                             stroke-width="1.5"
                                             stroke-linecap="round"
                                             stroke-linejoin="round"></path>
                                       </g>
                                       <defs>
                                          <clipPath>
                                             <rect width="20" height="20" fill="white"></rect>
                                          </clipPath>
                                       </defs>
                                    </svg>
                                    <span>Unlimited AI generations</span>
                                 </li>
                                 <li class="d-flex flex-row gap-2 align-items-center">
                                    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                       <g clip-path="url(#4997e58d)">
                                          <path d="M5.83333 9.99992L9.99999 14.1666L18.3333 5.83325" stroke="#6FDB93" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                          <path
                                             d="M1.66667 9.99992L5.83334 14.1666M10 9.99992L14.1667 5.83325"
                                             stroke="#6FDB93"
                                             stroke-width="1.5"
                                             stroke-linecap="round"
                                             stroke-linejoin="round"></path>
                                       </g>
                                       <defs>
                                          <clipPath>
                                             <rect width="20" height="20" fill="white"></rect>
                                          </clipPath>
                                       </defs>
                                    </svg>
                                    <span>All Pro features</span>
                                 </li>
                                 <li class="d-flex flex-row gap-2 align-items-center">
                                    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                       <g clip-path="url(#4997e58d)">
                                          <path d="M5.83333 9.99992L9.99999 14.1666L18.3333 5.83325" stroke="#6FDB93" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                          <path
                                             d="M1.66667 9.99992L5.83334 14.1666M10 9.99992L14.1667 5.83325"
                                             stroke="#6FDB93"
                                             stroke-width="1.5"
                                             stroke-linecap="round"
                                             stroke-linejoin="round"></path>
                                       </g>
                                       <defs>
                                          <clipPath>
                                             <rect width="20" height="20" fill="white"></rect>
                                          </clipPath>
                                       </defs>
                                    </svg>
                                    <span>Custom model training</span>
                                 </li>
                                 <li class="d-flex flex-row gap-2 align-items-center">
                                    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                       <g clip-path="url(#4997e58d)">
                                          <path d="M5.83333 9.99992L9.99999 14.1666L18.3333 5.83325" stroke="#6FDB93" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                          <path
                                             d="M1.66667 9.99992L5.83334 14.1666M10 9.99992L14.1667 5.83325"
                                             stroke="#6FDB93"
                                             stroke-width="1.5"
                                             stroke-linecap="round"
                                             stroke-linejoin="round"></path>
                                       </g>
                                       <defs>
                                          <clipPath>
                                             <rect width="20" height="20" fill="white"></rect>
                                          </clipPath>
                                       </defs>
                                    </svg>
                                    <span>Dedicated account manager</span>
                                 </li>
                                 <li class="d-flex flex-row gap-2 align-items-center">
                                    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                       <g clip-path="url(#4997e58d)">
                                          <path d="M5.83333 9.99992L9.99999 14.1666L18.3333 5.83325" stroke="#6FDB93" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                          <path
                                             d="M1.66667 9.99992L5.83334 14.1666M10 9.99992L14.1667 5.83325"
                                             stroke="#6FDB93"
                                             stroke-width="1.5"
                                             stroke-linecap="round"
                                             stroke-linejoin="round"></path>
                                       </g>
                                       <defs>
                                          <clipPath>
                                             <rect width="20" height="20" fill="white"></rect>
                                          </clipPath>
                                       </defs>
                                    </svg>
                                    <span>24/7 priority support</span>
                                 </li>
                                 <li class="d-flex flex-row gap-2 align-items-center">
                                    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                       <g clip-path="url(#4997e58d)">
                                          <path d="M5.83333 9.99992L9.99999 14.1666L18.3333 5.83325" stroke="#6FDB93" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                          <path
                                             d="M1.66667 9.99992L5.83334 14.1666M10 9.99992L14.1667 5.83325"
                                             stroke="#6FDB93"
                                             stroke-width="1.5"
                                             stroke-linecap="round"
                                             stroke-linejoin="round"></path>
                                       </g>
                                       <defs>
                                          <clipPath>
                                             <rect width="20" height="20" fill="white"></rect>
                                          </clipPath>
                                       </defs>
                                    </svg>
                                    <span>Advanced security &amp; compliance</span>
                                 </li>
                                 <li class="d-flex flex-row gap-2 align-items-center">
                                    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                       <g clip-path="url(#4997e58d)">
                                          <path d="M5.83333 9.99992L9.99999 14.1666L18.3333 5.83325" stroke="#6FDB93" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                          <path
                                             d="M1.66667 9.99992L5.83334 14.1666M10 9.99992L14.1667 5.83325"
                                             stroke="#6FDB93"
                                             stroke-width="1.5"
                                             stroke-linecap="round"
                                             stroke-linejoin="round"></path>
                                       </g>
                                       <defs>
                                          <clipPath>
                                             <rect width="20" height="20" fill="white"></rect>
                                          </clipPath>
                                       </defs>
                                    </svg>
                                    <span>Custom integrations</span>
                                 </li>
                              </ul>
                           </div>
                           <div>
                              <a href="#!" class="btn btn-dark">Get Started</a>
                           </div>
                        </div>
                     </div>
                  </div>
               </div>
            </div>
         </section>
         <!--Pricing section end-->
         <!--Faq start-->
         <section class="py-xl-9 pb-md-8 pt-lg-8 pb-lg-10 py-5" data-cue="fadeIn">
            <div class="container">
               <div class="row">
                  <div class="col-lg-5 col-12" data-cue="zoomIn">
                     <div class="mb-7 mb-md-0 me-lg-7 text-md-center text-lg-start">
                        <div class="mb-4">
                           <h2 class="mb-3"><span class="gradient-text">Frequently asked questions</span></h2>
                           <p class="mb-0 lead">
                              Can’t find any answer for your question?
                              <br />
                              Ask our
                              <a href="./contact-1.html" class="text-primary">customer support</a>
                           </p>
                        </div>
                     </div>
                  </div>
                  <div class="col-lg-7 col-12" data-cue="zoomIn">
                     <div class="accordion" id="accordionExample">
                        <div class="border mb-2 rounded-3 p-3">
                           <h2 class="h5 mb-0">
                              <a
                                 href="#"
                                 class="text-reset d-flex justify-content-between align-items-center"
                                 data-bs-toggle="collapse"
                                 data-bs-target="#collapseOne"
                                 aria-expanded="false"
                                 aria-controls="collapseOne">
                                 Can I trial block before paying?
                                 <span class="chevron-arrow"><i class="bi bi-chevron-down"></i></span>
                              </a>
                           </h2>
                           <div id="collapseOne" class="accordion-collapse collapse" data-bs-parent="#accordionExample">
                              <div class="mt-3">Lorem ipsum dolor sit amet, consectetur adipisicing elit. Quae harum adipisci possimus et. Iusto pariatur iste nam incidunt ratione modi.</div>
                           </div>
                        </div>

                        <div class="border mb-2 rounded-3 p-3">
                           <h2 class="h5 mb-0">
                              <a
                                 href="#"
                                 class="text-reset d-flex justify-content-between align-items-center"
                                 data-bs-toggle="collapse"
                                 data-bs-target="#collapseTwo"
                                 aria-expanded="true"
                                 aria-controls="collapseTwo">
                                 How are additional plan billed?
                                 <span class="chevron-arrow"><i class="bi bi-chevron-down"></i></span>
                              </a>
                           </h2>
                           <div id="collapseTwo" class="accordion-collapse collapse show" data-bs-parent="#accordionExample">
                              <div class="mt-3">
                                 Sed urna felis, dapibus quis leo nec, luctus auctor augue. Nam gravida placerat sem vitae rutrum. Integer accumsan, enim et facilisis eleifend, ante ligula ornare
                                 nulla, sed pharetra tortor diam eget magna.
                              </div>
                           </div>
                        </div>
                        <div class="border mb-2 rounded-3 p-3">
                           <h2 class="h5 mb-0">
                              <a
                                 href="#"
                                 class="text-reset d-flex justify-content-between align-items-center"
                                 data-bs-toggle="collapse"
                                 data-bs-target="#collapseThree"
                                 aria-expanded="false"
                                 aria-controls="collapseThree">
                                 When should I change my plan?
                                 <span class="chevron-arrow"><i class="bi bi-chevron-down"></i></span>
                              </a>
                           </h2>
                           <div id="collapseThree" class="accordion-collapse collapse" data-bs-parent="#accordionExample">
                              <div class="mt-3">
                                 Lorem ipsum dolor sit, amet consectetur adipisicing elit. Inventore tenetur cum doloremque iusto molestiae. Minus beatae quam cumque modi quidem asperiores aliquam
                                 pariatur in iste.
                              </div>
                           </div>
                        </div>
                        <div class="border mb-2 rounded-3 p-3">
                           <h2 class="h5 mb-0">
                              <a
                                 href="#"
                                 class="text-reset d-flex justify-content-between align-items-center"
                                 data-bs-toggle="collapse"
                                 data-bs-target="#collapseFour"
                                 aria-expanded="false"
                                 aria-controls="collapseFour">
                                 What payment methods do you offer?
                                 <span class="chevron-arrow"><i class="bi bi-chevron-down"></i></span>
                              </a>
                           </h2>
                           <div id="collapseFour" class="accordion-collapse collapse" data-bs-parent="#accordionExample">
                              <div class="mt-3">
                                 Lorem ipsum dolor sit, amet consectetur adipisicing elit. Inventore tenetur cum doloremque iusto molestiae. Minus beatae quam cumque modi quidem asperiores aliquam
                                 pariatur in iste.
                              </div>
                           </div>
                        </div>
                        <div class="border mb-2 rounded-3 p-3">
                           <h2 class="h5 mb-0">
                              <a
                                 href="#"
                                 class="text-reset d-flex justify-content-between align-items-center"
                                 data-bs-toggle="collapse"
                                 data-bs-target="#collapseFive"
                                 aria-expanded="false"
                                 aria-controls="collapseFive">
                                 What is your refund policy?
                                 <span class="chevron-arrow"><i class="bi bi-chevron-down"></i></span>
                              </a>
                           </h2>
                           <div id="collapseFive" class="accordion-collapse collapse" data-bs-parent="#accordionExample">
                              <div class="mt-3">
                                 Lorem ipsum dolor sit, amet consectetur adipisicing elit. Inventore tenetur cum doloremque iusto molestiae. Minus beatae quam cumque modi quidem asperiores aliquam
                                 pariatur in iste.
                              </div>
                           </div>
                        </div>
                        <div class="border mb-2 rounded-3 p-3">
                           <h2 class="h5 mb-0">
                              <a
                                 href="#"
                                 class="text-reset d-flex justify-content-between align-items-center"
                                 data-bs-toggle="collapse"
                                 data-bs-target="#collapseSix"
                                 aria-expanded="false"
                                 aria-controls="collapseSix">
                                 How are paid plans billed when moving other plan?
                                 <span class="chevron-arrow"><i class="bi bi-chevron-down"></i></span>
                              </a>
                           </h2>
                           <div id="collapseSix" class="accordion-collapse collapse" data-bs-parent="#accordionExample">
                              <div class="mt-3">
                                 Lorem ipsum dolor sit, amet consectetur adipisicing elit. Inventore tenetur cum doloremque iusto molestiae. Minus beatae quam cumque modi quidem asperiores aliquam
                                 pariatur in iste.
                              </div>
                           </div>
                        </div>
                     </div>
                  </div>
               </div>
            </div>
         </section>
         <!--Faq end-->
         <!--Call to action start-->
         <section data-cue="fadeIn" class="py-lg-9 py-md-8 py-5" style="background: url(./assets/images/ai-studio/cta-glows.png) no-repeat; background-size: cover; background-position: center">
            <div class="container">
               <div class="row">
                  <div class="col-xxl-6 offset-xxl-3 col-12">
                     <div class="d-flex flex-column gap-6">
                        <div class="text-center d-flex flex-column gap-2" data-cue="zoomOut">
                           <h2 class="mb-0 display-6">Ready to Transform Your Creative Process?</h2>

                           <p class="mb-0 px-xl-5 lead">Join thousands of creators and businesses using our AI to work smarter, faster, and more creatively.</p>
                        </div>
                        <div class="d-flex flex-row gap-3 align-content-center justify-content-center">
                           <a href="#!" class="btn btn-primary">Get Started Free</a>
                           <a href="./index.html" class="btn btn-dark">See demo</a>
                        </div>
                        <div class="d-flex justify-content-center">
                           <small class="fw-medium">No credit card required. Start with a free account.</small>
                        </div>
                     </div>
                  </div>
               </div>
            </div>
         </section>
         <!--Call to action end-->
      </main>
      <!-- Modal -->

      @@include("partials/footer-ai-studio.html") @@include("partials/btn-scroll-top.html") @@include("partials/scripts.html")
      <script src="@@webRoot/assets/js/vendors/partical.js"></script>
      <script src="@@webRoot/node_modules/embla-carousel/embla-carousel.umd.js"></script>
      <script src="@@webRoot/node_modules/embla-carousel-auto-scroll/embla-carousel-auto-scroll.umd.js"></script>
      <script src="@@webRoot/assets/js/vendors/embla.js"></script>
      <script src="@@webRoot/node_modules/scrollcue/scrollCue.min.js"></script>
      <script src="@@webRoot/assets/js/vendors/scrollcue.js"></script>
      <script src="@@webRoot/assets/js/vendors/partical.js"></script>
   </body>
</html>
