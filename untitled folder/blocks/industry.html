<!doctype html>
<html lang="en">
   <head>
      <!-- Required meta tags -->
<meta charset="utf-8" />
<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />

      <link href="../assets/libs/prismjs/themes/prism-okaidia.min.css" rel="stylesheet" />
      <!-- Favicon icon-->
<link rel="apple-touch-icon" sizes="180x180" href="../assets/images/favicon/apple-touch-icon.png" />
<link rel="icon" type="image/png" sizes="32x32" href="../assets/images/favicon/favicon-32x32.png" />
<link rel="icon" type="image/png" sizes="16x16" href="../assets/images/favicon/favicon-16x16.png" />
<link rel="manifest" href="../assets/images/favicon/site.webmanifest" />
<link rel="mask-icon" href="../assets/images/favicon/block-safari-pinned-tab.svg" color="#8b3dff" />
<link rel="shortcut icon" href="../assets/images/favicon/favicon.ico" />
<meta name="msapplication-TileColor" content="#8b3dff" />
<meta name="msapplication-config" content="../assets/images/favicon/tile.xml" />

<!-- Color modes -->
<script src="../assets/js/vendors/color-modes.js"></script>

<!-- Libs CSS -->
<link href="../assets/libs/simplebar/dist/simplebar.min.css" rel="stylesheet" />
<link href="../assets/libs/bootstrap-icons/font/bootstrap-icons.min.css" rel="stylesheet" />

<!-- Scroll Cue -->
<link rel="stylesheet" href="../assets/libs/scrollcue/scrollCue.css" />

<!-- Box icons -->
<link rel="stylesheet" href="../assets/fonts/css/boxicons.min.css" />

<!-- Theme CSS -->
<link rel="stylesheet" href="../assets/css/theme.min.css">

      <title>Industry Snippet - Responsive Website Template | Block</title>
   </head>

   <body>
      <!-- Navbar -->
<header>
   <nav class="navbar navbar-expand-lg  navbar-light w-100">
      <div class="container px-3">
         <a class="navbar-brand" href="../index.html"><img src="../assets/images/logo/logo.svg" alt /></a>
         <button class="navbar-toggler offcanvas-nav-btn" type="button">
            <i class="bi bi-list"></i>
         </button>
         <div class="offcanvas offcanvas-start offcanvas-nav" style="width: 20rem">
            <div class="offcanvas-header">
               <a href="../index.html" class="text-inverse"><img src="../assets/images/logo/logo.svg" alt /></a>
               <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close"></button>
            </div>
            <div class="offcanvas-body pt-0 align-items-center">
               <ul class="navbar-nav mx-auto align-items-lg-center">
                  <li class="nav-item">
                     <a class="nav-link" href="../home.html">Home</a>
                  </li>
                  <li class="nav-item">
                     <a class="nav-link" href="../scan-pay.html">Scan & Pay</a>
                  </li>
                  <li class="nav-item">
                     <a class="nav-link" href="../service.html">Services</a>
                  </li>
                  <li class="nav-item">
                     <a class="nav-link" href="../contact.html">Contact</a>
                  </li>
                  <li class="nav-item">
                     <a class="nav-link" href="../become-an-agent.html">Become An Agent</a>
                  </li>
               </ul>
               <div class="mt-3 mt-lg-0 d-flex align-items-center">
                  <a href="../signin.html" class="btn btn-light mx-2">Login</a>
                  <a href="https://play.google.com/store/apps/details?id=com.qsoft.aidapay&hl=en&pli=1" class="btn btn-primary">Create account</a>
               </div>
            </div>
         </div>
      </div>
   </nav>
</header>

      <main>
         <div class="pattern-square"></div>
         <section class="bg-light py-5 py-lg-8 bg-opacity-50">
            <div class="container">
               <div class="row">
                  <div class="col-12 col-md-6">
                     <div>
                        <h1 class="mb-0">Industry Sections</h1>
                     </div>
                  </div>
                  <!-- Navbar Filter tabs -->
<div class="mt-6 col-12 d-flex flex-wrap gap-2">
   <a href="../blocks/about.html" class="filter-badge ">About</a>
   <a href="../blocks/award.html" class="filter-badge ">Award</a>
   <a href="../blocks/blog.html" class="filter-badge ">Blog</a>
   <a href="../blocks/cta.html" class="filter-badge ">Call to Action</a>
   <a href="../blocks/clients.html" class="filter-badge ">Clients</a>
   <a href="../blocks/contact.html" class="filter-badge ">Contact</a>
   <a href="../blocks/carousel.html" class="filter-badge ">Carousel</a>
   <a href="../blocks/case-study.html" class="filter-badge ">Case Study</a>
   <a href="../blocks/facts.html" class="filter-badge ">Facts</a>
   <a href="../blocks/faq.html" class="filter-badge ">FAQ</a>
   <a href="../blocks/features.html" class="filter-badge ">Features</a>
   <a href="../blocks/form.html" class="filter-badge ">Form</a>
   <a href="../blocks/footer.html" class="filter-badge ">Footer</a>
   <a href="../blocks/hero.html" class="filter-badge ">Hero</a>
   <a href="../blocks/integration.html" class="filter-badge ">Integration</a>
   <a href="../blocks/industry.html" class="filter-badge  active ">Industry</a>
   <a href="../blocks/location.html" class="filter-badge ">Location</a>
   <a href="../blocks/navbar.html" class="filter-badge ">Navbar</a>
   <a href="../blocks/portfolio.html" class="filter-badge ">Portfolio</a>
   <a href="../blocks/pricing.html" class="filter-badge ">Pricing</a>
   <a href="../blocks/process.html" class="filter-badge ">Process</a>
   <a href="../blocks/services.html" class="filter-badge ">Services</a>
   <a href="../blocks/team.html" class="filter-badge ">Team</a>
   <a href="../blocks/testimonails.html" class="filter-badge ">Testimonials</a>
</div>

               </div>
            </div>
         </section>
         <section class="py-lg-8 py-5">
            <div class="container">
               <div class="mb-lg-7 mb-5">
                  <div class="row align-items-center">
                     <div class="col-lg-8 col-xl-9 col-7">
                        <div>
                           <h2 class="text-truncate h5 mb-0">Industry #1</h2>
                        </div>
                     </div>
                     <div class="col-lg-4 col-xl-3 col-5 d-flex justify-content-end">
                        <ul class="nav nav-pills nav-custom-pill" id="pills-tabTwo" role="tablist">
                           <li class="nav-item">
                              <a
                                 class="nav-link active"
                                 id="pills-example-preview-tab"
                                 data-bs-toggle="pill"
                                 href="#pills-example-preview"
                                 role="tab"
                                 aria-controls="pills-example-preview"
                                 aria-selected="true">
                                 <span class="lh-1"><i class="bi bi-eye"></i></span>
                                 <span class="ms-2 d-none d-lg-block">Preview</span>
                              </a>
                           </li>
                           <li class="nav-item">
                              <a class="nav-link" id="pills-example-code-tab" data-bs-toggle="pill" href="#pills-example-code" role="tab" aria-controls="pills-example-code" aria-selected="false">
                                 <span class="lh-1"><i class="bi bi-code"></i></span>
                                 <span class="ms-2 d-none d-lg-block">Code</span>
                              </a>
                           </li>
                        </ul>
                     </div>
                  </div>
                  <div class="row">
                     <div class="col-md-12">
                        <div class="tab-content border mt-3 p-3 rounded-2" id="pills-tabTwoContent">
                           <div class="tab-pane tab-example-preview fade show active" id="pills-example-preview" role="tabpanel" aria-labelledby="pills-example-preview-tab">
                              <!--Industry we serve start-->
                              <section class="py-xl-9 py-5 bg-light">
                                 <div class="container">
                                    <div class="row">
                                       <div class="col-lg-8 offset-lg-2 col-12">
                                          <div class="text-center mb-xl-7 mb-5">
                                             <small class="text-uppercase ls-md fw-semibold text-primary">Industry We Serve</small>
                                             <h2 class="my-3">Our industry expertise and solutions</h2>
                                             <p class="mb-0 text-body">
                                                At [Your Company Name], we specialize in delivering innovative IT solutions tailored for a diverse range of industries. Our expertise extends across:
                                             </p>
                                          </div>
                                       </div>
                                    </div>
                                    <div class="row g-4">
                                       <div class="col-lg-2 col-md-4 col-6">
                                          <a href="#!" class="card-hover bg-white card card-lift text-center p-4">
                                             <span class="border rounded-circle icon-shape icon-xxl mb-4">
                                                <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                   <path
                                                      opacity="0.2"
                                                      d="M27 14.1338V20.7863C27.0006 21.0307 26.9116 21.2668 26.75 21.45C25.3375 23.0238 21.8875 26 16 26C10.1125 26 6.665 23.0238 5.25 21.45C5.08837 21.2668 4.99944 21.0307 5 20.7863V14.1338L16 20L27 14.1338Z" />
                                                   <path
                                                      d="M31.4697 11.1176L16.4697 3.11758C16.325 3.04054 16.1636 3.00024 15.9997 3.00024C15.8358 3.00024 15.6744 3.04054 15.5297 3.11758L0.529688 11.1176C0.369693 11.2028 0.235888 11.33 0.142587 11.4854C0.0492867 11.6409 0 11.8188 0 12.0001C0 12.1814 0.0492867 12.3593 0.142587 12.5147C0.235888 12.6702 0.369693 12.7973 0.529688 12.8826L3.99969 14.7338V20.7863C3.99865 21.2775 4.17942 21.7517 4.50719 22.1176C6.14469 23.9413 9.81344 27.0001 15.9997 27.0001C18.0509 27.017 20.0867 26.6455 21.9997 25.9051V30.0001C21.9997 30.2653 22.105 30.5196 22.2926 30.7072C22.4801 30.8947 22.7345 31.0001 22.9997 31.0001C23.2649 31.0001 23.5193 30.8947 23.7068 30.7072C23.8943 30.5196 23.9997 30.2653 23.9997 30.0001V24.9388C25.3037 24.1861 26.4821 23.2342 27.4922 22.1176C27.82 21.7517 28.0007 21.2775 27.9997 20.7863V14.7338L31.4697 12.8826C31.6297 12.7973 31.7635 12.6702 31.8568 12.5147C31.9501 12.3593 31.9994 12.1814 31.9994 12.0001C31.9994 11.8188 31.9501 11.6409 31.8568 11.4854C31.7635 11.33 31.6297 11.2028 31.4697 11.1176ZM15.9997 25.0001C10.5909 25.0001 7.40969 22.3576 5.99969 20.7863V15.8001L15.5297 20.8826C15.6744 20.9596 15.8358 20.9999 15.9997 20.9999C16.1636 20.9999 16.325 20.9596 16.4697 20.8826L21.9997 17.9338V23.7263C20.4247 24.4613 18.4397 25.0001 15.9997 25.0001ZM25.9997 20.7813C25.4002 21.4465 24.7296 22.0438 23.9997 22.5626V16.8663L25.9997 15.8001V20.7813ZM23.4997 14.8676L23.4722 14.8513L16.4722 11.1176C16.2386 10.9983 15.9676 10.9755 15.7174 11.0542C15.4672 11.1328 15.258 11.3066 15.1347 11.5381C15.0114 11.7695 14.984 12.0402 15.0583 12.2917C15.1327 12.5431 15.3029 12.7554 15.5322 12.8826L21.3747 16.0001L15.9997 18.8663L3.12469 12.0001L15.9997 5.13383L28.8747 12.0001L23.4997 14.8676Z" />
                                                </svg>
                                             </span>
                                             <h4 class="mb-0 card-text fs-5">Education</h4>
                                          </a>
                                       </div>
                                       <div class="col-lg-2 col-md-4 col-6">
                                          <a href="#!" class="card-hover bg-white card card-lift text-center p-4">
                                             <span class="border rounded-circle icon-shape icon-xxl mb-4">
                                                <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                   <path
                                                      opacity="0.2"
                                                      d="M28.0009 9L24.4359 20.5888C24.3099 20.9976 24.0562 21.3553 23.712 21.6093C23.3678 21.8633 22.9512 22.0002 22.5234 22H10.5096C10.0749 22.0002 9.65204 21.8588 9.30493 21.5971C8.95782 21.3355 8.70539 20.9679 8.58586 20.55L5.28711 9H28.0009Z"
                                                      fill="" />
                                                   <path
                                                      d="M12 27C12 27.3956 11.8827 27.7822 11.6629 28.1111C11.4432 28.44 11.1308 28.6964 10.7654 28.8478C10.3999 28.9991 9.99778 29.0387 9.60982 28.9616C9.22186 28.8844 8.86549 28.6939 8.58579 28.4142C8.30608 28.1345 8.1156 27.7781 8.03843 27.3902C7.96126 27.0022 8.00087 26.6001 8.15224 26.2346C8.30362 25.8692 8.55996 25.5568 8.88886 25.3371C9.21776 25.1173 9.60444 25 10 25C10.5304 25 11.0391 25.2107 11.4142 25.5858C11.7893 25.9609 12 26.4696 12 27ZM23 25C22.6044 25 22.2178 25.1173 21.8889 25.3371C21.56 25.5568 21.3036 25.8692 21.1522 26.2346C21.0009 26.6001 20.9613 27.0022 21.0384 27.3902C21.1156 27.7781 21.3061 28.1345 21.5858 28.4142C21.8655 28.6939 22.2219 28.8844 22.6098 28.9616C22.9978 29.0387 23.3999 28.9991 23.7654 28.8478C24.1308 28.6964 24.4432 28.44 24.6629 28.1111C24.8827 27.7822 25 27.3956 25 27C25 26.4696 24.7893 25.9609 24.4142 25.5858C24.0391 25.2107 23.5304 25 23 25ZM28.9562 9.29375L25.39 20.8825C25.2032 21.4968 24.8234 22.0346 24.3068 22.416C23.7903 22.7975 23.1646 23.0022 22.5225 23H10.5087C9.85747 22.9976 9.2245 22.7842 8.70463 22.3919C8.18477 21.9996 7.80598 21.4494 7.625 20.8237L3.1025 5H1C0.734784 5 0.48043 4.89464 0.292893 4.70711C0.105357 4.51957 0 4.26522 0 4C0 3.73478 0.105357 3.48043 0.292893 3.29289C0.48043 3.10536 0.734784 3 1 3H3.1025C3.53676 3.00144 3.95888 3.14349 4.30567 3.40487C4.65246 3.66626 4.90526 4.03293 5.02625 4.45L6.04 8H28C28.1565 7.99994 28.3108 8.03661 28.4505 8.10706C28.5903 8.17751 28.7115 8.27976 28.8045 8.40561C28.8976 8.53145 28.9597 8.67737 28.9861 8.83163C29.0124 8.98588 29.0022 9.14417 28.9562 9.29375ZM26.6462 10H6.61125L9.5475 20.275C9.60724 20.4839 9.73336 20.6676 9.90679 20.7984C10.0802 20.9292 10.2915 21 10.5087 21H22.5225C22.7365 21.0001 22.9448 20.9315 23.117 20.8044C23.2891 20.6773 23.4159 20.4983 23.4788 20.2938L26.6462 10Z"
                                                      fill="" />
                                                </svg>
                                             </span>
                                             <h4 class="mb-0 card-text fs-5">E-Commerce</h4>
                                          </a>
                                       </div>
                                       <div class="col-lg-2 col-md-4 col-6">
                                          <a href="#!" class="card-hover bg-white card card-lift text-center p-4">
                                             <span class="border rounded-circle icon-shape icon-xxl mb-4">
                                                <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                   <path
                                                      opacity="0.2"
                                                      d="M29 11.75C29 20 16 27 16 27C16 27 3 20 3 11.75C3 9.95979 3.71116 8.2429 4.97703 6.97703C6.2429 5.71116 7.95979 5 9.75 5C12.5738 5 14.9925 6.53875 16 9C17.0075 6.53875 19.4262 5 22.25 5C24.0402 5 25.7571 5.71116 27.023 6.97703C28.2888 8.2429 29 9.95979 29 11.75Z"
                                                      fill="" />
                                                   <path
                                                      d="M9 17H4C3.73478 17 3.48043 16.8946 3.29289 16.7071C3.10536 16.5196 3 16.2652 3 16C3 15.7348 3.10536 15.4804 3.29289 15.2929C3.48043 15.1054 3.73478 15 4 15H8.465L10.1675 12.445C10.2588 12.3078 10.3825 12.1953 10.5278 12.1175C10.673 12.0397 10.8352 11.999 11 11.999C11.1648 11.999 11.327 12.0397 11.4722 12.1175C11.6175 12.1953 11.7412 12.3078 11.8325 12.445L15 17.195L16.1675 15.445C16.2589 15.308 16.3827 15.1957 16.5279 15.1181C16.6732 15.0405 16.8353 14.9999 17 15H20C20.2652 15 20.5196 15.1054 20.7071 15.2929C20.8946 15.4804 21 15.7348 21 16C21 16.2652 20.8946 16.5196 20.7071 16.7071C20.5196 16.8946 20.2652 17 20 17H17.535L15.8325 19.555C15.7412 19.6922 15.6175 19.8047 15.4722 19.8825C15.327 19.9603 15.1648 20.001 15 20.001C14.8352 20.001 14.673 19.9603 14.5278 19.8825C14.3825 19.8047 14.2588 19.6922 14.1675 19.555L11 14.8025L9.8325 16.5525C9.74139 16.6899 9.6177 16.8027 9.47246 16.8808C9.32721 16.9589 9.1649 16.9998 9 17ZM22.25 4C19.6688 4 17.4088 5.11 16 6.98625C14.5912 5.11 12.3312 4 9.75 4C7.69528 4.00232 5.72539 4.81958 4.27248 6.27248C2.81958 7.72539 2.00232 9.69528 2 11.75C2 11.8438 2 11.9375 2 12.0312C2.00829 12.2965 2.12159 12.5475 2.31499 12.7292C2.50839 12.9109 2.76603 13.0083 3.03125 13C3.29647 12.9917 3.54753 12.8784 3.7292 12.685C3.91088 12.4916 4.00829 12.234 4 11.9688C4 11.8963 4 11.8225 4 11.75C4.00198 10.2256 4.60842 8.76423 5.68633 7.68633C6.76423 6.60842 8.22561 6.00198 9.75 6C12.1813 6 14.2225 7.295 15.075 9.375C15.1503 9.55841 15.2785 9.71528 15.4432 9.82569C15.6079 9.93609 15.8017 9.99503 16 9.99503C16.1983 9.99503 16.3921 9.93609 16.5568 9.82569C16.7215 9.71528 16.8497 9.55841 16.925 9.375C17.7775 7.29125 19.8187 6 22.25 6C23.7744 6.00198 25.2358 6.60842 26.3137 7.68633C27.3916 8.76423 27.998 10.2256 28 11.75C28 18.4513 18.28 24.5188 16 25.85C14.6463 25.0613 10.6712 22.6 7.665 19.3237C7.48565 19.1282 7.23594 19.0118 6.97081 19.0003C6.70568 18.9888 6.44685 19.0831 6.25125 19.2625C6.05565 19.4419 5.93931 19.6916 5.92783 19.9567C5.91634 20.2218 6.01065 20.4807 6.19 20.6763C10.0863 24.9263 15.3062 27.7612 15.5262 27.88C15.6719 27.9583 15.8346 27.9993 16 27.9993C16.1654 27.9993 16.3281 27.9583 16.4737 27.88C17.0262 27.5825 30 20.5 30 11.75C29.9977 9.69528 29.1804 7.72539 27.7275 6.27248C26.2746 4.81958 24.3047 4.00232 22.25 4Z"
                                                      fill="" />
                                                </svg>
                                             </span>
                                             <h4 class="mb-0 card-text fs-5">Healthcare</h4>
                                          </a>
                                       </div>
                                       <div class="col-lg-2 col-md-4 col-6">
                                          <a href="#!" class="card-hover bg-white card card-lift text-center p-4">
                                             <span class="border rounded-circle icon-shape icon-xxl mb-4">
                                                <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                   <path opacity="0.2" d="M29 12H3L16 4L29 12Z" fill="" />
                                                   <path
                                                      d="M3 13.0001H6V21.0001H4C3.73478 21.0001 3.48043 21.1055 3.29289 21.293C3.10536 21.4805 3 21.7349 3 22.0001C3 22.2653 3.10536 22.5197 3.29289 22.7072C3.48043 22.8948 3.73478 23.0001 4 23.0001H28C28.2652 23.0001 28.5196 22.8948 28.7071 22.7072C28.8946 22.5197 29 22.2653 29 22.0001C29 21.7349 28.8946 21.4805 28.7071 21.293C28.5196 21.1055 28.2652 21.0001 28 21.0001H26V13.0001H29C29.2176 12.9999 29.4292 12.9287 29.6026 12.7974C29.7761 12.666 29.902 12.4817 29.9612 12.2723C30.0205 12.063 30.0098 11.84 29.9308 11.6373C29.8519 11.4345 29.709 11.263 29.5238 11.1489L16.5238 3.14886C16.3662 3.05201 16.1849 3.00073 16 3.00073C15.8151 3.00073 15.6338 3.05201 15.4762 3.14886L2.47625 11.1489C2.29103 11.263 2.14811 11.4345 2.06916 11.6373C1.99021 11.84 1.97955 12.063 2.03878 12.2723C2.09801 12.4817 2.22391 12.666 2.39738 12.7974C2.57085 12.9287 2.78242 12.9999 3 13.0001ZM8 13.0001H12V21.0001H8V13.0001ZM18 13.0001V21.0001H14V13.0001H18ZM24 21.0001H20V13.0001H24V21.0001ZM16 5.17386L25.4675 11.0001H6.5325L16 5.17386ZM31 26.0001C31 26.2653 30.8946 26.5197 30.7071 26.7072C30.5196 26.8948 30.2652 27.0001 30 27.0001H2C1.73478 27.0001 1.48043 26.8948 1.29289 26.7072C1.10536 26.5197 1 26.2653 1 26.0001C1 25.7349 1.10536 25.4805 1.29289 25.293C1.48043 25.1055 1.73478 25.0001 2 25.0001H30C30.2652 25.0001 30.5196 25.1055 30.7071 25.293C30.8946 25.4805 31 25.7349 31 26.0001Z"
                                                      fill="" />
                                                </svg>
                                             </span>
                                             <h4 class="mb-0 card-text fs-5">Finance</h4>
                                          </a>
                                       </div>
                                       <div class="col-lg-2 col-md-4 col-6">
                                          <a href="#!" class="card-hover bg-white card card-lift text-center p-4">
                                             <span class="border rounded-circle icon-shape icon-xxl mb-4">
                                                <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                   <path
                                                      opacity="0.2"
                                                      d="M23 23H28V26C28 26.2652 27.8946 26.5196 27.7071 26.7071C27.5196 26.8946 27.2652 27 27 27H24C23.7348 27 23.4804 26.8946 23.2929 26.7071C23.1054 26.5196 23 26.2652 23 26V23ZM4 26C4 26.2652 4.10536 26.5196 4.29289 26.7071C4.48043 26.8946 4.73478 27 5 27H8C8.26522 27 8.51957 26.8946 8.70711 26.7071C8.89464 26.5196 9 26.2652 9 26V23H4V26ZM24.2638 6.59375C24.1852 6.41702 24.057 6.26686 23.8949 6.16148C23.7327 6.05609 23.5434 6 23.35 6H8.65C8.45659 6 8.26733 6.05609 8.10514 6.16148C7.94296 6.26686 7.81482 6.41702 7.73625 6.59375L4 15H28L24.2638 6.59375Z"
                                                      fill="" />
                                                   <path
                                                      d="M30 14H28.65L25.1775 6.1875C25.0204 5.83403 24.7641 5.53372 24.4397 5.32296C24.1153 5.11219 23.7368 5 23.35 5H8.65C8.26317 5 7.88465 5.11219 7.56029 5.32296C7.23593 5.53372 6.97965 5.83403 6.8225 6.1875L3.35 14H2C1.73478 14 1.48043 14.1054 1.29289 14.2929C1.10536 14.4804 1 14.7348 1 15C1 15.2652 1.10536 15.5196 1.29289 15.7071C1.48043 15.8946 1.73478 16 2 16H3V26C3 26.5304 3.21071 27.0391 3.58579 27.4142C3.96086 27.7893 4.46957 28 5 28H8C8.53043 28 9.03914 27.7893 9.41421 27.4142C9.78929 27.0391 10 26.5304 10 26V24H22V26C22 26.5304 22.2107 27.0391 22.5858 27.4142C22.9609 27.7893 23.4696 28 24 28H27C27.5304 28 28.0391 27.7893 28.4142 27.4142C28.7893 27.0391 29 26.5304 29 26V16H30C30.2652 16 30.5196 15.8946 30.7071 15.7071C30.8946 15.5196 31 15.2652 31 15C31 14.7348 30.8946 14.4804 30.7071 14.2929C30.5196 14.1054 30.2652 14 30 14ZM8.65 7H23.35L26.4613 14H5.53875L8.65 7ZM8 26H5V24H8V26ZM24 26V24H27V26H24ZM27 22H5V16H27V22ZM7 19C7 18.7348 7.10536 18.4804 7.29289 18.2929C7.48043 18.1054 7.73478 18 8 18H10C10.2652 18 10.5196 18.1054 10.7071 18.2929C10.8946 18.4804 11 18.7348 11 19C11 19.2652 10.8946 19.5196 10.7071 19.7071C10.5196 19.8946 10.2652 20 10 20H8C7.73478 20 7.48043 19.8946 7.29289 19.7071C7.10536 19.5196 7 19.2652 7 19ZM21 19C21 18.7348 21.1054 18.4804 21.2929 18.2929C21.4804 18.1054 21.7348 18 22 18H24C24.2652 18 24.5196 18.1054 24.7071 18.2929C24.8946 18.4804 25 18.7348 25 19C25 19.2652 24.8946 19.5196 24.7071 19.7071C24.5196 19.8946 24.2652 20 24 20H22C21.7348 20 21.4804 19.8946 21.2929 19.7071C21.1054 19.5196 21 19.2652 21 19Z"
                                                      fill="" />
                                                </svg>
                                             </span>
                                             <h4 class="mb-0 card-text fs-5">Automotive</h4>
                                          </a>
                                       </div>
                                       <div class="col-lg-2 col-md-4 col-6">
                                          <a href="#!" class="card-hover bg-white card card-lift text-center p-4">
                                             <span class="border rounded-circle icon-shape icon-xxl mb-4">
                                                <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                   <path
                                                      opacity="0.2"
                                                      d="M27 9V22H5V9C5 8.46957 5.21071 7.96086 5.58579 7.58579C5.96086 7.21071 6.46957 7 7 7H25C25.5304 7 26.0391 7.21071 26.4142 7.58579C26.7893 7.96086 27 8.46957 27 9Z"
                                                      fill="" />
                                                   <path
                                                      d="M29 21H28V9C28 8.20435 27.6839 7.44129 27.1213 6.87868C26.5587 6.31607 25.7956 6 25 6H7C6.20435 6 5.44129 6.31607 4.87868 6.87868C4.31607 7.44129 4 8.20435 4 9V21H3C2.73478 21 2.48043 21.1054 2.29289 21.2929C2.10536 21.4804 2 21.7348 2 22V24C2 24.7956 2.31607 25.5587 2.87868 26.1213C3.44129 26.6839 4.20435 27 5 27H27C27.7956 27 28.5587 26.6839 29.1213 26.1213C29.6839 25.5587 30 24.7956 30 24V22C30 21.7348 29.8946 21.4804 29.7071 21.2929C29.5196 21.1054 29.2652 21 29 21ZM6 9C6 8.73478 6.10536 8.48043 6.29289 8.29289C6.48043 8.10536 6.73478 8 7 8H25C25.2652 8 25.5196 8.10536 25.7071 8.29289C25.8946 8.48043 26 8.73478 26 9V21H6V9ZM28 24C28 24.2652 27.8946 24.5196 27.7071 24.7071C27.5196 24.8946 27.2652 25 27 25H5C4.73478 25 4.48043 24.8946 4.29289 24.7071C4.10536 24.5196 4 24.2652 4 24V23H28V24ZM19 11C19 11.2652 18.8946 11.5196 18.7071 11.7071C18.5196 11.8946 18.2652 12 18 12H14C13.7348 12 13.4804 11.8946 13.2929 11.7071C13.1054 11.5196 13 11.2652 13 11C13 10.7348 13.1054 10.4804 13.2929 10.2929C13.4804 10.1054 13.7348 10 14 10H18C18.2652 10 18.5196 10.1054 18.7071 10.2929C18.8946 10.4804 19 10.7348 19 11Z"
                                                      fill="" />
                                                </svg>
                                             </span>
                                             <h4 class="mb-0 card-text fs-5">Software</h4>
                                          </a>
                                       </div>
                                    </div>
                                 </div>
                              </section>
                              <!--Industry we serve end-->
                           </div>
                           <div class="tab-pane tab-example-code fade" id="pills-example-code" role="tabpanel" aria-labelledby="pills-example-code-tab">
                              <pre class="language-markup" tabindex="0"><code class="language-markup"> <span class="token comment">&lt;!--Industry we serve start--&gt;</span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>section</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>py-xl-9 py-5 bg-light<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
   <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>container<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
      <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>row<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
         <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>col-lg-8 offset-lg-2 col-12<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
            <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>text-center mb-xl-7 mb-5<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
               <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>small</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>text-uppercase ls-md fw-semibold text-primary<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Industry We Serve<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>small</span><span class="token punctuation">&gt;</span></span>
               <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>h2</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>my-3<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Our industry expertise and solutions<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>h2</span><span class="token punctuation">&gt;</span></span>
               <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>p</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>mb-0 text-body<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                  At [Your Company Name], we specialize in delivering innovative IT solutions tailored for a diverse range of industries. Our expertise extends
                  across:
               <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>p</span><span class="token punctuation">&gt;</span></span>
            <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
         <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
      <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
      <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>row g-4<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
         <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>col-lg-2 col-md-4 col-6<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
            <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>a</span> <span class="token attr-name">href</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>#!<span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>card-hover bg-white card card-lift text-center p-4<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
               <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>span</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>border rounded-circle icon-shape icon-xxl mb-4<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>svg</span> <span class="token attr-name">width</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>32<span class="token punctuation">"</span></span> <span class="token attr-name">height</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>32<span class="token punctuation">"</span></span> <span class="token attr-name">viewBox</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>0 0 32 32<span class="token punctuation">"</span></span> <span class="token attr-name">fill</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>none<span class="token punctuation">"</span></span> <span class="token attr-name">xmlns</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>http://www.w3.org/2000/svg<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                     <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>path</span>
                        <span class="token attr-name">opacity</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>0.2<span class="token punctuation">"</span></span>
                        <span class="token attr-name">d</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>M27 14.1338V20.7863C27.0006 21.0307 26.9116 21.2668 26.75 21.45C25.3375 23.0238 21.8875 26 16 26C10.1125 26 6.665 23.0238 5.25 21.45C5.08837 21.2668 4.99944 21.0307 5 20.7863V14.1338L16 20L27 14.1338Z<span class="token punctuation">"</span></span> <span class="token punctuation">/&gt;</span></span>
                     <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>path</span>
                        <span class="token attr-name">d</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>M31.4697 11.1176L16.4697 3.11758C16.325 3.04054 16.1636 3.00024 15.9997 3.00024C15.8358 3.00024 15.6744 3.04054 15.5297 3.11758L0.529688 11.1176C0.369693 11.2028 0.235888 11.33 0.142587 11.4854C0.0492867 11.6409 0 11.8188 0 12.0001C0 12.1814 0.0492867 12.3593 0.142587 12.5147C0.235888 12.6702 0.369693 12.7973 0.529688 12.8826L3.99969 14.7338V20.7863C3.99865 21.2775 4.17942 21.7517 4.50719 22.1176C6.14469 23.9413 9.81344 27.0001 15.9997 27.0001C18.0509 27.017 20.0867 26.6455 21.9997 25.9051V30.0001C21.9997 30.2653 22.105 30.5196 22.2926 30.7072C22.4801 30.8947 22.7345 31.0001 22.9997 31.0001C23.2649 31.0001 23.5193 30.8947 23.7068 30.7072C23.8943 30.5196 23.9997 30.2653 23.9997 30.0001V24.9388C25.3037 24.1861 26.4821 23.2342 27.4922 22.1176C27.82 21.7517 28.0007 21.2775 27.9997 20.7863V14.7338L31.4697 12.8826C31.6297 12.7973 31.7635 12.6702 31.8568 12.5147C31.9501 12.3593 31.9994 12.1814 31.9994 12.0001C31.9994 11.8188 31.9501 11.6409 31.8568 11.4854C31.7635 11.33 31.6297 11.2028 31.4697 11.1176ZM15.9997 25.0001C10.5909 25.0001 7.40969 22.3576 5.99969 20.7863V15.8001L15.5297 20.8826C15.6744 20.9596 15.8358 20.9999 15.9997 20.9999C16.1636 20.9999 16.325 20.9596 16.4697 20.8826L21.9997 17.9338V23.7263C20.4247 24.4613 18.4397 25.0001 15.9997 25.0001ZM25.9997 20.7813C25.4002 21.4465 24.7296 22.0438 23.9997 22.5626V16.8663L25.9997 15.8001V20.7813ZM23.4997 14.8676L23.4722 14.8513L16.4722 11.1176C16.2386 10.9983 15.9676 10.9755 15.7174 11.0542C15.4672 11.1328 15.258 11.3066 15.1347 11.5381C15.0114 11.7695 14.984 12.0402 15.0583 12.2917C15.1327 12.5431 15.3029 12.7554 15.5322 12.8826L21.3747 16.0001L15.9997 18.8663L3.12469 12.0001L15.9997 5.13383L28.8747 12.0001L23.4997 14.8676Z<span class="token punctuation">"</span></span> <span class="token punctuation">/&gt;</span></span>
                  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>svg</span><span class="token punctuation">&gt;</span></span>
               <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>span</span><span class="token punctuation">&gt;</span></span>
               <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>h4</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>mb-0 card-text fs-5<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Education<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>h4</span><span class="token punctuation">&gt;</span></span>
            <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>a</span><span class="token punctuation">&gt;</span></span>
         <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
         <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>col-lg-2 col-md-4 col-6<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
            <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>a</span> <span class="token attr-name">href</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>#!<span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>card-hover bg-white card card-lift text-center p-4<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
               <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>span</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>border rounded-circle icon-shape icon-xxl mb-4<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>svg</span> <span class="token attr-name">width</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>32<span class="token punctuation">"</span></span> <span class="token attr-name">height</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>32<span class="token punctuation">"</span></span> <span class="token attr-name">viewBox</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>0 0 32 32<span class="token punctuation">"</span></span> <span class="token attr-name">fill</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>none<span class="token punctuation">"</span></span> <span class="token attr-name">xmlns</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>http://www.w3.org/2000/svg<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                     <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>path</span>
                        <span class="token attr-name">opacity</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>0.2<span class="token punctuation">"</span></span>
                        <span class="token attr-name">d</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>M28.0009 9L24.4359 20.5888C24.3099 20.9976 24.0562 21.3553 23.712 21.6093C23.3678 21.8633 22.9512 22.0002 22.5234 22H10.5096C10.0749 22.0002 9.65204 21.8588 9.30493 21.5971C8.95782 21.3355 8.70539 20.9679 8.58586 20.55L5.28711 9H28.0009Z<span class="token punctuation">"</span></span>
                        <span class="token attr-name">fill</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span><span class="token punctuation">"</span></span> <span class="token punctuation">/&gt;</span></span>
                     <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>path</span>
                        <span class="token attr-name">d</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>M12 27C12 27.3956 11.8827 27.7822 11.6629 28.1111C11.4432 28.44 11.1308 28.6964 10.7654 28.8478C10.3999 28.9991 9.99778 29.0387 9.60982 28.9616C9.22186 28.8844 8.86549 28.6939 8.58579 28.4142C8.30608 28.1345 8.1156 27.7781 8.03843 27.3902C7.96126 27.0022 8.00087 26.6001 8.15224 26.2346C8.30362 25.8692 8.55996 25.5568 8.88886 25.3371C9.21776 25.1173 9.60444 25 10 25C10.5304 25 11.0391 25.2107 11.4142 25.5858C11.7893 25.9609 12 26.4696 12 27ZM23 25C22.6044 25 22.2178 25.1173 21.8889 25.3371C21.56 25.5568 21.3036 25.8692 21.1522 26.2346C21.0009 26.6001 20.9613 27.0022 21.0384 27.3902C21.1156 27.7781 21.3061 28.1345 21.5858 28.4142C21.8655 28.6939 22.2219 28.8844 22.6098 28.9616C22.9978 29.0387 23.3999 28.9991 23.7654 28.8478C24.1308 28.6964 24.4432 28.44 24.6629 28.1111C24.8827 27.7822 25 27.3956 25 27C25 26.4696 24.7893 25.9609 24.4142 25.5858C24.0391 25.2107 23.5304 25 23 25ZM28.9562 9.29375L25.39 20.8825C25.2032 21.4968 24.8234 22.0346 24.3068 22.416C23.7903 22.7975 23.1646 23.0022 22.5225 23H10.5087C9.85747 22.9976 9.2245 22.7842 8.70463 22.3919C8.18477 21.9996 7.80598 21.4494 7.625 20.8237L3.1025 5H1C0.734784 5 0.48043 4.89464 0.292893 4.70711C0.105357 4.51957 0 4.26522 0 4C0 3.73478 0.105357 3.48043 0.292893 3.29289C0.48043 3.10536 0.734784 3 1 3H3.1025C3.53676 3.00144 3.95888 3.14349 4.30567 3.40487C4.65246 3.66626 4.90526 4.03293 5.02625 4.45L6.04 8H28C28.1565 7.99994 28.3108 8.03661 28.4505 8.10706C28.5903 8.17751 28.7115 8.27976 28.8045 8.40561C28.8976 8.53145 28.9597 8.67737 28.9861 8.83163C29.0124 8.98588 29.0022 9.14417 28.9562 9.29375ZM26.6462 10H6.61125L9.5475 20.275C9.60724 20.4839 9.73336 20.6676 9.90679 20.7984C10.0802 20.9292 10.2915 21 10.5087 21H22.5225C22.7365 21.0001 22.9448 20.9315 23.117 20.8044C23.2891 20.6773 23.4159 20.4983 23.4788 20.2938L26.6462 10Z<span class="token punctuation">"</span></span>
                        <span class="token attr-name">fill</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span><span class="token punctuation">"</span></span> <span class="token punctuation">/&gt;</span></span>
                  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>svg</span><span class="token punctuation">&gt;</span></span>
               <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>span</span><span class="token punctuation">&gt;</span></span>
               <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>h4</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>mb-0 card-text fs-5<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>E-Commerce<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>h4</span><span class="token punctuation">&gt;</span></span>
            <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>a</span><span class="token punctuation">&gt;</span></span>
         <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
         <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>col-lg-2 col-md-4 col-6<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
            <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>a</span> <span class="token attr-name">href</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>#!<span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>card-hover bg-white card card-lift text-center p-4<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
               <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>span</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>border rounded-circle icon-shape icon-xxl mb-4<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>svg</span> <span class="token attr-name">width</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>32<span class="token punctuation">"</span></span> <span class="token attr-name">height</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>32<span class="token punctuation">"</span></span> <span class="token attr-name">viewBox</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>0 0 32 32<span class="token punctuation">"</span></span> <span class="token attr-name">fill</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>none<span class="token punctuation">"</span></span> <span class="token attr-name">xmlns</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>http://www.w3.org/2000/svg<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                     <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>path</span>
                        <span class="token attr-name">opacity</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>0.2<span class="token punctuation">"</span></span>
                        <span class="token attr-name">d</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>M29 11.75C29 20 16 27 16 27C16 27 3 20 3 11.75C3 9.95979 3.71116 8.2429 4.97703 6.97703C6.2429 5.71116 7.95979 5 9.75 5C12.5738 5 14.9925 6.53875 16 9C17.0075 6.53875 19.4262 5 22.25 5C24.0402 5 25.7571 5.71116 27.023 6.97703C28.2888 8.2429 29 9.95979 29 11.75Z<span class="token punctuation">"</span></span>
                        <span class="token attr-name">fill</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span><span class="token punctuation">"</span></span> <span class="token punctuation">/&gt;</span></span>
                     <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>path</span>
                        <span class="token attr-name">d</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>M9 17H4C3.73478 17 3.48043 16.8946 3.29289 16.7071C3.10536 16.5196 3 16.2652 3 16C3 15.7348 3.10536 15.4804 3.29289 15.2929C3.48043 15.1054 3.73478 15 4 15H8.465L10.1675 12.445C10.2588 12.3078 10.3825 12.1953 10.5278 12.1175C10.673 12.0397 10.8352 11.999 11 11.999C11.1648 11.999 11.327 12.0397 11.4722 12.1175C11.6175 12.1953 11.7412 12.3078 11.8325 12.445L15 17.195L16.1675 15.445C16.2589 15.308 16.3827 15.1957 16.5279 15.1181C16.6732 15.0405 16.8353 14.9999 17 15H20C20.2652 15 20.5196 15.1054 20.7071 15.2929C20.8946 15.4804 21 15.7348 21 16C21 16.2652 20.8946 16.5196 20.7071 16.7071C20.5196 16.8946 20.2652 17 20 17H17.535L15.8325 19.555C15.7412 19.6922 15.6175 19.8047 15.4722 19.8825C15.327 19.9603 15.1648 20.001 15 20.001C14.8352 20.001 14.673 19.9603 14.5278 19.8825C14.3825 19.8047 14.2588 19.6922 14.1675 19.555L11 14.8025L9.8325 16.5525C9.74139 16.6899 9.6177 16.8027 9.47246 16.8808C9.32721 16.9589 9.1649 16.9998 9 17ZM22.25 4C19.6688 4 17.4088 5.11 16 6.98625C14.5912 5.11 12.3312 4 9.75 4C7.69528 4.00232 5.72539 4.81958 4.27248 6.27248C2.81958 7.72539 2.00232 9.69528 2 11.75C2 11.8438 2 11.9375 2 12.0312C2.00829 12.2965 2.12159 12.5475 2.31499 12.7292C2.50839 12.9109 2.76603 13.0083 3.03125 13C3.29647 12.9917 3.54753 12.8784 3.7292 12.685C3.91088 12.4916 4.00829 12.234 4 11.9688C4 11.8963 4 11.8225 4 11.75C4.00198 10.2256 4.60842 8.76423 5.68633 7.68633C6.76423 6.60842 8.22561 6.00198 9.75 6C12.1813 6 14.2225 7.295 15.075 9.375C15.1503 9.55841 15.2785 9.71528 15.4432 9.82569C15.6079 9.93609 15.8017 9.99503 16 9.99503C16.1983 9.99503 16.3921 9.93609 16.5568 9.82569C16.7215 9.71528 16.8497 9.55841 16.925 9.375C17.7775 7.29125 19.8187 6 22.25 6C23.7744 6.00198 25.2358 6.60842 26.3137 7.68633C27.3916 8.76423 27.998 10.2256 28 11.75C28 18.4513 18.28 24.5188 16 25.85C14.6463 25.0613 10.6712 22.6 7.665 19.3237C7.48565 19.1282 7.23594 19.0118 6.97081 19.0003C6.70568 18.9888 6.44685 19.0831 6.25125 19.2625C6.05565 19.4419 5.93931 19.6916 5.92783 19.9567C5.91634 20.2218 6.01065 20.4807 6.19 20.6763C10.0863 24.9263 15.3062 27.7612 15.5262 27.88C15.6719 27.9583 15.8346 27.9993 16 27.9993C16.1654 27.9993 16.3281 27.9583 16.4737 27.88C17.0262 27.5825 30 20.5 30 11.75C29.9977 9.69528 29.1804 7.72539 27.7275 6.27248C26.2746 4.81958 24.3047 4.00232 22.25 4Z<span class="token punctuation">"</span></span>
                        <span class="token attr-name">fill</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span><span class="token punctuation">"</span></span> <span class="token punctuation">/&gt;</span></span>
                  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>svg</span><span class="token punctuation">&gt;</span></span>
               <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>span</span><span class="token punctuation">&gt;</span></span>
               <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>h4</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>mb-0 card-text fs-5<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Healthcare<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>h4</span><span class="token punctuation">&gt;</span></span>
            <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>a</span><span class="token punctuation">&gt;</span></span>
         <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
         <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>col-lg-2 col-md-4 col-6<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
            <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>a</span> <span class="token attr-name">href</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>#!<span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>card-hover bg-white card card-lift text-center p-4<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
               <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>span</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>border rounded-circle icon-shape icon-xxl mb-4<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>svg</span> <span class="token attr-name">width</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>32<span class="token punctuation">"</span></span> <span class="token attr-name">height</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>32<span class="token punctuation">"</span></span> <span class="token attr-name">viewBox</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>0 0 32 32<span class="token punctuation">"</span></span> <span class="token attr-name">fill</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>none<span class="token punctuation">"</span></span> <span class="token attr-name">xmlns</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>http://www.w3.org/2000/svg<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                     <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>path</span> <span class="token attr-name">opacity</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>0.2<span class="token punctuation">"</span></span> <span class="token attr-name">d</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>M29 12H3L16 4L29 12Z<span class="token punctuation">"</span></span> <span class="token attr-name">fill</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span><span class="token punctuation">"</span></span> <span class="token punctuation">/&gt;</span></span>
                     <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>path</span>
                        <span class="token attr-name">d</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>M3 13.0001H6V21.0001H4C3.73478 21.0001 3.48043 21.1055 3.29289 21.293C3.10536 21.4805 3 21.7349 3 22.0001C3 22.2653 3.10536 22.5197 3.29289 22.7072C3.48043 22.8948 3.73478 23.0001 4 23.0001H28C28.2652 23.0001 28.5196 22.8948 28.7071 22.7072C28.8946 22.5197 29 22.2653 29 22.0001C29 21.7349 28.8946 21.4805 28.7071 21.293C28.5196 21.1055 28.2652 21.0001 28 21.0001H26V13.0001H29C29.2176 12.9999 29.4292 12.9287 29.6026 12.7974C29.7761 12.666 29.902 12.4817 29.9612 12.2723C30.0205 12.063 30.0098 11.84 29.9308 11.6373C29.8519 11.4345 29.709 11.263 29.5238 11.1489L16.5238 3.14886C16.3662 3.05201 16.1849 3.00073 16 3.00073C15.8151 3.00073 15.6338 3.05201 15.4762 3.14886L2.47625 11.1489C2.29103 11.263 2.14811 11.4345 2.06916 11.6373C1.99021 11.84 1.97955 12.063 2.03878 12.2723C2.09801 12.4817 2.22391 12.666 2.39738 12.7974C2.57085 12.9287 2.78242 12.9999 3 13.0001ZM8 13.0001H12V21.0001H8V13.0001ZM18 13.0001V21.0001H14V13.0001H18ZM24 21.0001H20V13.0001H24V21.0001ZM16 5.17386L25.4675 11.0001H6.5325L16 5.17386ZM31 26.0001C31 26.2653 30.8946 26.5197 30.7071 26.7072C30.5196 26.8948 30.2652 27.0001 30 27.0001H2C1.73478 27.0001 1.48043 26.8948 1.29289 26.7072C1.10536 26.5197 1 26.2653 1 26.0001C1 25.7349 1.10536 25.4805 1.29289 25.293C1.48043 25.1055 1.73478 25.0001 2 25.0001H30C30.2652 25.0001 30.5196 25.1055 30.7071 25.293C30.8946 25.4805 31 25.7349 31 26.0001Z<span class="token punctuation">"</span></span>
                        <span class="token attr-name">fill</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span><span class="token punctuation">"</span></span> <span class="token punctuation">/&gt;</span></span>
                  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>svg</span><span class="token punctuation">&gt;</span></span>
               <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>span</span><span class="token punctuation">&gt;</span></span>
               <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>h4</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>mb-0 card-text fs-5<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Finance<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>h4</span><span class="token punctuation">&gt;</span></span>
            <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>a</span><span class="token punctuation">&gt;</span></span>
         <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
         <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>col-lg-2 col-md-4 col-6<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
            <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>a</span> <span class="token attr-name">href</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>#!<span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>card-hover bg-white card card-lift text-center p-4<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
               <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>span</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>border rounded-circle icon-shape icon-xxl mb-4<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>svg</span> <span class="token attr-name">width</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>32<span class="token punctuation">"</span></span> <span class="token attr-name">height</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>32<span class="token punctuation">"</span></span> <span class="token attr-name">viewBox</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>0 0 32 32<span class="token punctuation">"</span></span> <span class="token attr-name">fill</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>none<span class="token punctuation">"</span></span> <span class="token attr-name">xmlns</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>http://www.w3.org/2000/svg<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                     <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>path</span>
                        <span class="token attr-name">opacity</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>0.2<span class="token punctuation">"</span></span>
                        <span class="token attr-name">d</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>M23 23H28V26C28 26.2652 27.8946 26.5196 27.7071 26.7071C27.5196 26.8946 27.2652 27 27 27H24C23.7348 27 23.4804 26.8946 23.2929 26.7071C23.1054 26.5196 23 26.2652 23 26V23ZM4 26C4 26.2652 4.10536 26.5196 4.29289 26.7071C4.48043 26.8946 4.73478 27 5 27H8C8.26522 27 8.51957 26.8946 8.70711 26.7071C8.89464 26.5196 9 26.2652 9 26V23H4V26ZM24.2638 6.59375C24.1852 6.41702 24.057 6.26686 23.8949 6.16148C23.7327 6.05609 23.5434 6 23.35 6H8.65C8.45659 6 8.26733 6.05609 8.10514 6.16148C7.94296 6.26686 7.81482 6.41702 7.73625 6.59375L4 15H28L24.2638 6.59375Z<span class="token punctuation">"</span></span>
                        <span class="token attr-name">fill</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span><span class="token punctuation">"</span></span> <span class="token punctuation">/&gt;</span></span>
                     <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>path</span>
                        <span class="token attr-name">d</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>M30 14H28.65L25.1775 6.1875C25.0204 5.83403 24.7641 5.53372 24.4397 5.32296C24.1153 5.11219 23.7368 5 23.35 5H8.65C8.26317 5 7.88465 5.11219 7.56029 5.32296C7.23593 5.53372 6.97965 5.83403 6.8225 6.1875L3.35 14H2C1.73478 14 1.48043 14.1054 1.29289 14.2929C1.10536 14.4804 1 14.7348 1 15C1 15.2652 1.10536 15.5196 1.29289 15.7071C1.48043 15.8946 1.73478 16 2 16H3V26C3 26.5304 3.21071 27.0391 3.58579 27.4142C3.96086 27.7893 4.46957 28 5 28H8C8.53043 28 9.03914 27.7893 9.41421 27.4142C9.78929 27.0391 10 26.5304 10 26V24H22V26C22 26.5304 22.2107 27.0391 22.5858 27.4142C22.9609 27.7893 23.4696 28 24 28H27C27.5304 28 28.0391 27.7893 28.4142 27.4142C28.7893 27.0391 29 26.5304 29 26V16H30C30.2652 16 30.5196 15.8946 30.7071 15.7071C30.8946 15.5196 31 15.2652 31 15C31 14.7348 30.8946 14.4804 30.7071 14.2929C30.5196 14.1054 30.2652 14 30 14ZM8.65 7H23.35L26.4613 14H5.53875L8.65 7ZM8 26H5V24H8V26ZM24 26V24H27V26H24ZM27 22H5V16H27V22ZM7 19C7 18.7348 7.10536 18.4804 7.29289 18.2929C7.48043 18.1054 7.73478 18 8 18H10C10.2652 18 10.5196 18.1054 10.7071 18.2929C10.8946 18.4804 11 18.7348 11 19C11 19.2652 10.8946 19.5196 10.7071 19.7071C10.5196 19.8946 10.2652 20 10 20H8C7.73478 20 7.48043 19.8946 7.29289 19.7071C7.10536 19.5196 7 19.2652 7 19ZM21 19C21 18.7348 21.1054 18.4804 21.2929 18.2929C21.4804 18.1054 21.7348 18 22 18H24C24.2652 18 24.5196 18.1054 24.7071 18.2929C24.8946 18.4804 25 18.7348 25 19C25 19.2652 24.8946 19.5196 24.7071 19.7071C24.5196 19.8946 24.2652 20 24 20H22C21.7348 20 21.4804 19.8946 21.2929 19.7071C21.1054 19.5196 21 19.2652 21 19Z<span class="token punctuation">"</span></span>
                        <span class="token attr-name">fill</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span><span class="token punctuation">"</span></span> <span class="token punctuation">/&gt;</span></span>
                  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>svg</span><span class="token punctuation">&gt;</span></span>
               <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>span</span><span class="token punctuation">&gt;</span></span>
               <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>h4</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>mb-0 card-text fs-5<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Automotive<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>h4</span><span class="token punctuation">&gt;</span></span>
            <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>a</span><span class="token punctuation">&gt;</span></span>
         <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
         <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>col-lg-2 col-md-4 col-6<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
            <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>a</span> <span class="token attr-name">href</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>#!<span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>card-hover bg-white card card-lift text-center p-4<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
               <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>span</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>border rounded-circle icon-shape icon-xxl mb-4<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>svg</span> <span class="token attr-name">width</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>32<span class="token punctuation">"</span></span> <span class="token attr-name">height</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>32<span class="token punctuation">"</span></span> <span class="token attr-name">viewBox</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>0 0 32 32<span class="token punctuation">"</span></span> <span class="token attr-name">fill</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>none<span class="token punctuation">"</span></span> <span class="token attr-name">xmlns</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>http://www.w3.org/2000/svg<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                     <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>path</span>
                        <span class="token attr-name">opacity</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>0.2<span class="token punctuation">"</span></span>
                        <span class="token attr-name">d</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>M27 9V22H5V9C5 8.46957 5.21071 7.96086 5.58579 7.58579C5.96086 7.21071 6.46957 7 7 7H25C25.5304 7 26.0391 7.21071 26.4142 7.58579C26.7893 7.96086 27 8.46957 27 9Z<span class="token punctuation">"</span></span>
                        <span class="token attr-name">fill</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span><span class="token punctuation">"</span></span> <span class="token punctuation">/&gt;</span></span>
                     <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>path</span>
                        <span class="token attr-name">d</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>M29 21H28V9C28 8.20435 27.6839 7.44129 27.1213 6.87868C26.5587 6.31607 25.7956 6 25 6H7C6.20435 6 5.44129 6.31607 4.87868 6.87868C4.31607 7.44129 4 8.20435 4 9V21H3C2.73478 21 2.48043 21.1054 2.29289 21.2929C2.10536 21.4804 2 21.7348 2 22V24C2 24.7956 2.31607 25.5587 2.87868 26.1213C3.44129 26.6839 4.20435 27 5 27H27C27.7956 27 28.5587 26.6839 29.1213 26.1213C29.6839 25.5587 30 24.7956 30 24V22C30 21.7348 29.8946 21.4804 29.7071 21.2929C29.5196 21.1054 29.2652 21 29 21ZM6 9C6 8.73478 6.10536 8.48043 6.29289 8.29289C6.48043 8.10536 6.73478 8 7 8H25C25.2652 8 25.5196 8.10536 25.7071 8.29289C25.8946 8.48043 26 8.73478 26 9V21H6V9ZM28 24C28 24.2652 27.8946 24.5196 27.7071 24.7071C27.5196 24.8946 27.2652 25 27 25H5C4.73478 25 4.48043 24.8946 4.29289 24.7071C4.10536 24.5196 4 24.2652 4 24V23H28V24ZM19 11C19 11.2652 18.8946 11.5196 18.7071 11.7071C18.5196 11.8946 18.2652 12 18 12H14C13.7348 12 13.4804 11.8946 13.2929 11.7071C13.1054 11.5196 13 11.2652 13 11C13 10.7348 13.1054 10.4804 13.2929 10.2929C13.4804 10.1054 13.7348 10 14 10H18C18.2652 10 18.5196 10.1054 18.7071 10.2929C18.8946 10.4804 19 10.7348 19 11Z<span class="token punctuation">"</span></span>
                        <span class="token attr-name">fill</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span><span class="token punctuation">"</span></span> <span class="token punctuation">/&gt;</span></span>
                  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>svg</span><span class="token punctuation">&gt;</span></span>
               <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>span</span><span class="token punctuation">&gt;</span></span>
               <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>h4</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>mb-0 card-text fs-5<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Software<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>h4</span><span class="token punctuation">&gt;</span></span>
            <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>a</span><span class="token punctuation">&gt;</span></span>
         <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
      <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
   <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>section</span><span class="token punctuation">&gt;</span></span>
<span class="token comment">&lt;!--Industry we serve end--&gt;</span></code></pre>
                           </div>
                        </div>
                     </div>
                  </div>
               </div>
            </div>
         </section>
      </main>
      <footer class="pt-7">
   <div class="container">
      <!-- Footer 4 column -->
      <div class="row">
         <div class="col-lg-9 col-12">
            <div class="row" id="ft-links">
               <div class="col-lg-3 col-12">
                  <div class="position-relative">
                     <div class="mb-3 pb-2 d-flex justify-content-between border-bottom border-bottom-lg-0">
                        <h4>Service</h4>
                        <a class="d-block d-lg-none stretched-link text-body" data-bs-toggle="collapse" href="#collapseLanding" role="button" aria-expanded="true" aria-controls="collapseLanding">
                           <i class="bi bi-chevron-down"></i>
                        </a>
                     </div>
                     <div class="d-lg-block collapse show" id="collapseLanding" data-bs-parent="#ft-links" style="">
                        <ul class="list-unstyled mb-0 py-3 py-lg-0">
                           <li class="mb-2">
                              <a href="./index.html" class="text-decoration-none text-reset">Web App Development</a>
                           </li>
                           <li class="mb-2">
                              <a href="#!" class="text-decoration-none text-reset">Front End Development</a>
                           </li>
                           <li class="mb-2">
                              <a href="#!" class="text-decoration-none text-reset">MVP Development</a>
                           </li>
                           <li class="mb-2">
                              <a href="#!" class="text-decoration-none text-reset">Digital Marketing</a>
                           </li>
                           <li class="mb-2">
                              <a href="#!" class="text-decoration-none text-reset">Content Writing</a>
                           </li>
                        </ul>
                     </div>
                  </div>
               </div>
               <div class="col-lg-3 col-12">
                  <div>
                     <div class="mb-3 pb-2 d-flex justify-content-between border-bottom border-bottom-lg-0 position-relative">
                        <h4>About us</h4>
                        <a
                           class="d-block d-lg-none stretched-link text-body collapsed"
                           data-bs-toggle="collapse"
                           href="#collapseAccounts"
                           role="button"
                           aria-expanded="false"
                           aria-controls="collapseAccounts">
                           <i class="bi bi-chevron-down"></i>
                        </a>
                     </div>
                     <div class="collapse d-lg-block" id="collapseAccounts" data-bs-parent="#ft-links">
                        <ul class="list-unstyled mb-0 py-3 py-lg-0">
                           <li class="mb-2">
                              <a href="#!" class="text-decoration-none text-reset">Case Studies</a>
                           </li>
                           <li class="mb-2">
                              <a href="#!" class="text-decoration-none text-reset">Blog</a>
                           </li>
                           <li class="mb-2">
                              <a href="#!" class="text-decoration-none text-reset">Services</a>
                           </li>
                           <li class="mb-2">
                              <a href="#!" class="text-decoration-none text-reset">About</a>
                           </li>
                           <li class="mb-2">
                              <a href="#!" class="text-decoration-none text-reset">Career</a>
                           </li>
                        </ul>
                     </div>
                  </div>
               </div>
               <div class="col-lg-3 col-12">
                  <div class="mb-3 pb-2 d-flex justify-content-between border-bottom border-bottom-lg-0 position-relative">
                     <h4>Technology</h4>
                     <a
                        class="d-block d-lg-none stretched-link text-body collapsed"
                        data-bs-toggle="collapse"
                        href="#collapseResources"
                        role="button"
                        aria-expanded="false"
                        aria-controls="collapseResources">
                        <i class="bi bi-chevron-down"></i>
                     </a>
                  </div>
                  <div class="collapse d-lg-block" id="collapseResources" data-bs-parent="#ft-links">
                     <ul class="list-unstyled mb-0 py-3 py-lg-0">
                        <li class="mb-2">
                           <a href="./docs/index.html" class="text-decoration-none text-reset">Next.js</a>
                        </li>
                        <li class="mb-2">
                           <a href="#!" class="text-decoration-none text-reset">Sanity</a>
                        </li>
                        <li class="mb-2">
                           <a href="./changelog.html" class="text-decoration-none text-reset">Content ful</a>
                        </li>
                        <li class="mb-2">
                           <a href="#!" class="text-decoration-none text-reset">Vercel</a>
                        </li>
                        <li class="mb-2">
                           <a href="#!" class="text-decoration-none text-reset">Netlify</a>
                        </li>
                     </ul>
                  </div>
               </div>
               <div class="col-lg-3 col-12">
                  <div class="mb-3 pb-2 d-flex justify-content-between border-bottom border-bottom-lg-0 position-relative">
                     <h4>Locations</h4>
                     <a
                        class="d-block d-lg-none stretched-link text-body collapsed"
                        data-bs-toggle="collapse"
                        href="#collapseLocations"
                        role="button"
                        aria-expanded="false"
                        aria-controls="collapseLocations">
                        <i class="bi bi-chevron-down"></i>
                     </a>
                  </div>
                  <div class="collapse d-lg-block" id="collapseLocations" data-bs-parent="#ft-links">
                     <ul class="list-unstyled mb-0 py-3 py-lg-0">
                        <li class="mb-2">
                           <a href="./docs/index.html" class="text-decoration-none text-reset">India</a>
                        </li>
                        <li class="mb-2">
                           <a href="#!" class="text-decoration-none text-reset">Australia</a>
                        </li>
                        <li class="mb-2">
                           <a href="./changelog.html" class="text-decoration-none text-reset">Brazil</a>
                        </li>
                        <li class="mb-2">
                           <a href="#!" class="text-decoration-none text-reset">Canada</a>
                        </li>
                     </ul>
                  </div>
               </div>
            </div>
         </div>
         <div class="col-lg-3 col-12">
            <div class="me-7">
               <h4 class="mb-4">Headquarters</h4>
               <p class="text-body-secondary">Codescandy, 412, Residency Rd, Shanthala Nagar, Ashok Nagar, Bengaluru, Karnataka, India 560025</p>
            </div>
         </div>
      </div>
   </div>
   <div class="container mt-7 pt-lg-7 pb-4">
      <div class="row align-items-center">
         <div class="col-md-3">
            <a class="mb-4 mb-lg-0 d-block text-inverse" href="../index.html"><img src="./assets/images/logo/logo.svg" alt="" /></a>
         </div>
         <div class="col-md-9 col-lg-6">
            <div class="small mb-3 mb-lg-0 text-lg-center">
               Copyright © 2024

               <span class="text-primary"><a href="#">Block Bootstrap 5 Theme</a></span>
               | Designed by
               <span class="text-primary"><a href="#">CodesCandy</a></span>
            </div>
         </div>
         <div class="col-lg-3">
            <div class="text-lg-end d-flex align-items-center justify-content-lg-end">
               <div class="dropdown">
                  <button class="btn btn-light btn-icon rounded-circle d-flex align-items-center" type="button" aria-expanded="false" data-bs-toggle="dropdown" aria-label="Toggle theme (auto)">
                     <i class="bi theme-icon-active lh-1"><i class="bi theme-icon bi-sun-fill"></i></i>
                     <span class="visually-hidden bs-theme-text">Toggle theme</span>
                  </button>
                  <ul class="dropdown-menu dropdown-menu-end shadow" aria-labelledby="bs-theme-text">
                     <li>
                        <button type="button" class="dropdown-item d-flex align-items-center active" data-bs-theme-value="light" aria-pressed="true">
                           <i class="bi theme-icon bi-sun-fill"></i>
                           <span class="ms-2">Light</span>
                        </button>
                     </li>
                     <li>
                        <button type="button" class="dropdown-item d-flex align-items-center" data-bs-theme-value="dark" aria-pressed="false">
                           <i class="bi theme-icon bi-moon-stars-fill"></i>
                           <span class="ms-2">Dark</span>
                        </button>
                     </li>
                     <li>
                        <button type="button" class="dropdown-item d-flex align-items-center" data-bs-theme-value="auto" aria-pressed="false">
                           <i class="bi theme-icon bi-circle-half"></i>
                           <span class="ms-2">Auto</span>
                        </button>
                     </li>
                  </ul>
               </div>
               <div class="ms-3 d-flex gap-2">
                  <a href="#!" class="btn btn-instagram btn-light btn-icon">
                     <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-instagram" viewBox="0 0 16 16">
                        <path
                           d="M8 0C5.829 0 5.556.01 4.703.048 3.85.088 3.269.222 2.76.42a3.917 3.917 0 0 0-1.417.923A3.927 3.927 0 0 0 .42 2.76C.222 3.268.087 3.85.048 4.7.01 5.555 0 5.827 0 8.001c0 2.172.01 2.444.048 3.297.04.852.174 1.433.372 1.942.205.526.478.972.923 1.417.444.445.89.719 1.416.923.51.198 1.09.333 1.942.372C5.555 15.99 5.827 16 8 16s2.444-.01 3.298-.048c.851-.04 1.434-.174 1.943-.372a3.916 3.916 0 0 0 1.416-.923c.445-.445.718-.891.923-1.417.197-.509.332-1.09.372-1.942C15.99 10.445 16 10.173 16 8s-.01-2.445-.048-3.299c-.04-.851-.175-1.433-.372-1.941a3.926 3.926 0 0 0-.923-1.417A3.911 3.911 0 0 0 13.24.42c-.51-.198-1.092-.333-1.943-.372C10.443.01 10.172 0 7.998 0h.003zm-.717 1.442h.718c2.136 0 2.389.007 3.232.046.78.035 1.204.166 1.486.275.373.145.64.319.92.599.28.28.453.546.598.92.11.281.24.705.275 1.485.039.843.047 1.096.047 3.231s-.008 2.389-.047 3.232c-.035.78-.166 1.203-.275 1.485a2.47 2.47 0 0 1-.599.919c-.28.28-.546.453-.92.598-.28.11-.704.24-1.485.276-.843.038-1.096.047-3.232.047s-2.39-.009-3.233-.047c-.78-.036-1.203-.166-1.485-.276a2.478 2.478 0 0 1-.92-.598 2.48 2.48 0 0 1-.6-.92c-.109-.281-.24-.705-.275-1.485-.038-.843-.046-1.096-.046-3.233 0-2.136.008-2.388.046-3.231.036-.78.166-1.204.276-1.486.145-.373.319-.64.599-.92.28-.28.546-.453.92-.598.282-.11.705-.24 1.485-.276.738-.034 1.024-.044 2.515-.045v.002zm4.988 1.328a.96.96 0 1 0 0 1.92.96.96 0 0 0 0-1.92zm-4.27 1.122a4.109 4.109 0 1 0 0 8.217 4.109 4.109 0 0 0 0-8.217zm0 1.441a2.667 2.667 0 1 1 0 5.334 2.667 2.667 0 0 1 0-5.334z"></path>
                     </svg>
                  </a>
                  <a href="#!" class="btn btn-facebook btn-icon">
                     <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-facebook" viewBox="0 0 16 16">
                        <path
                           d="M16 8.049c0-4.446-3.582-8.05-8-8.05C3.58 0-.002 3.603-.002 8.05c0 4.017 2.926 7.347 6.75 7.951v-5.625h-2.03V8.05H6.75V6.275c0-2.017 1.195-3.131 3.022-3.131.876 0 1.791.157 1.791.157v1.98h-1.009c-.993 0-1.303.621-1.303 1.258v1.51h2.218l-.354 2.326H9.25V16c3.824-.604 6.75-3.934 6.75-7.951z"></path>
                     </svg>
                  </a>
                  <a href="#!" class="btn btn-twitter btn-icon">
                     <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-twitter" viewBox="0 0 16 16">
                        <path
                           d="M5.026 15c6.038 0 9.341-5.003 9.341-9.334 0-.14 0-.282-.006-.422A6.685 6.685 0 0 0 16 3.542a6.658 6.658 0 0 1-1.889.518 3.301 3.301 0 0 0 1.447-1.817 6.533 6.533 0 0 1-2.087.793A3.286 3.286 0 0 0 7.875 6.03a9.325 9.325 0 0 1-6.767-3.429 3.289 3.289 0 0 0 1.018 4.382A3.323 3.323 0 0 1 .64 6.575v.045a3.288 3.288 0 0 0 2.632 3.218 3.203 3.203 0 0 1-.865.115 3.23 3.23 0 0 1-.614-.057 3.283 3.283 0 0 0 3.067 2.277A6.588 6.588 0 0 1 .78 13.58a6.32 6.32 0 0 1-.78-.045A9.344 9.344 0 0 0 5.026 15z"></path>
                     </svg>
                  </a>
               </div>
            </div>
         </div>
      </div>
   </div>
</footer>
 <div class="btn-scroll-top">
   <svg class="progress-square svg-content" width="100%" height="100%" viewBox="0 0 40 40">
      <path d="M8 1H32C35.866 1 39 4.13401 39 8V32C39 35.866 35.866 39 32 39H8C4.13401 39 1 35.866 1 32V8C1 4.13401 4.13401 1 8 1Z" />
   </svg>
</div>
 <!-- Libs JS -->
<script src="../assets/libs/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
<script src="../assets/libs/simplebar/dist/simplebar.min.js"></script>
<script src="../assets/libs/headhesive/dist/headhesive.min.js"></script>

<!-- Theme JS -->
<script src="../assets/js/theme.min.js"></script>

      <script src="../assets/libs/prismjs/prism.js"></script>
      <script src="../assets/libs/prismjs/components/prism-scss.min.js"></script>
      <script src="../assets/libs/prismjs/plugins/toolbar/prism-toolbar.min.js"></script>
      <script src="../assets/libs/prismjs/plugins/copy-to-clipboard/prism-copy-to-clipboard.min.js"></script>
      s
   </body>
</html>
