<!doctype html>
<html lang="en">
   <head>
      <!-- Required meta tags -->
<meta charset="utf-8" />
<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />

      <link href="../assets/libs/prismjs/themes/prism-okaidia.min.css" rel="stylesheet" />
      <link rel="stylesheet" href="../assets/libs/intl-tel-input/build/css/intlTelInput.min.css" />
      <!-- Favicon icon-->
<link rel="apple-touch-icon" sizes="180x180" href="../assets/images/favicon/apple-touch-icon.png" />
<link rel="icon" type="image/png" sizes="32x32" href="../assets/images/favicon/favicon-32x32.png" />
<link rel="icon" type="image/png" sizes="16x16" href="../assets/images/favicon/favicon-16x16.png" />
<link rel="manifest" href="../assets/images/favicon/site.webmanifest" />
<link rel="mask-icon" href="../assets/images/favicon/block-safari-pinned-tab.svg" color="#8b3dff" />
<link rel="shortcut icon" href="../assets/images/favicon/favicon.ico" />
<meta name="msapplication-TileColor" content="#8b3dff" />
<meta name="msapplication-config" content="../assets/images/favicon/tile.xml" />

<!-- Color modes -->
<script src="../assets/js/vendors/color-modes.js"></script>

<!-- Libs CSS -->
<link href="../assets/libs/simplebar/dist/simplebar.min.css" rel="stylesheet" />
<link href="../assets/libs/bootstrap-icons/font/bootstrap-icons.min.css" rel="stylesheet" />

<!-- Scroll Cue -->
<link rel="stylesheet" href="../assets/libs/scrollcue/scrollCue.css" />

<!-- Box icons -->
<link rel="stylesheet" href="../assets/fonts/css/boxicons.min.css" />

<!-- Theme CSS -->
<link rel="stylesheet" href="../assets/css/theme.min.css">

      <title>Hero Snippet v9 - Responsive Website Template | Block</title>
   </head>

   <body>
      <!-- Navbar -->
<header>
   <nav class="navbar navbar-expand-lg  navbar-light w-100 transparent">
      <div class="container px-3">
         <a class="navbar-brand" href="../index.html"><img src="../assets/images/logo/logo.svg" alt /></a>
         <button class="navbar-toggler offcanvas-nav-btn" type="button">
            <i class="bi bi-list"></i>
         </button>
         <div class="offcanvas offcanvas-start offcanvas-nav" style="width: 20rem">
            <div class="offcanvas-header">
               <a href="../index.html" class="text-inverse"><img src="../assets/images/logo/logo.svg" alt /></a>
               <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close"></button>
            </div>
            <div class="offcanvas-body pt-0 align-items-center">
               <ul class="navbar-nav mx-auto align-items-lg-center">
                  <li class="nav-item">
                     <a class="nav-link" href="../home.html">Home</a>
                  </li>
                  <li class="nav-item">
                     <a class="nav-link" href="../scan-pay.html">Scan & Pay</a>
                  </li>
                  <li class="nav-item">
                     <a class="nav-link" href="../service.html">Services</a>
                  </li>
                  <li class="nav-item">
                     <a class="nav-link" href="../contact.html">Contact</a>
                  </li>
                  <li class="nav-item">
                     <a class="nav-link" href="../become-an-agent.html">Become An Agent</a>
                  </li>
               </ul>
               <div class="mt-3 mt-lg-0 d-flex align-items-center">
                  <a href="../signin.html" class="btn btn-light mx-2">Login</a>
                  <a href="https://play.google.com/store/apps/details?id=com.qsoft.aidapay&hl=en&pli=1" class="btn btn-primary">Create account</a>
               </div>
            </div>
         </div>
      </div>
   </nav>
</header>

      <main>
         <!--Hero start-->
         <section class="py-xl-8 py-lg-7 py-5 position-relative z-1" data-cue="fadeIn">
            <div class="container pb-xl-8 mb-xl-8">
               <div class="row align-items-center gy-6">
                  <div class="col-lg-5 col-xl-5" data-cue="zoomOut">
                     <div class="d-flex flex-column gap-4">
                        <div class="d-flex flex-row gap-2 align-items-center lh-1">
                           <span>
                              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                 <path
                                    opacity="0.2"
                                    d="M6.75 15L9 17.25C9 17.25 8.25 20.25 3.75 20.25C3.75 15.75 6.75 15 6.75 15ZM12.75 6.75H6.97031C6.77166 6.75009 6.58116 6.82899 6.44062 6.96937L3.22031 10.1897C3.12199 10.2882 3.05303 10.4122 3.02115 10.5477C2.98927 10.6832 2.99574 10.8249 3.03981 10.9569C3.08389 11.089 3.16384 11.2061 3.27073 11.2953C3.37761 11.3845 3.5072 11.4422 3.645 11.4619L7.5 12L12.75 6.75ZM12 16.5L12.5381 20.355C12.5578 20.4928 12.6155 20.6224 12.7047 20.7293C12.7939 20.8362 12.911 20.9161 13.0431 20.9602C13.1751 21.0043 13.3168 21.0107 13.4523 20.9788C13.5878 20.947 13.7118 20.878 13.8103 20.7797L17.0306 17.5594C17.171 17.4188 17.2499 17.2283 17.25 17.0297V11.25L12 16.5Z"
                                    fill="#198754" />
                                 <path
                                    d="M9.72843 17.4317C9.69186 17.577 8.7778 20.9998 3.74999 20.9998C3.55108 20.9998 3.36031 20.9208 3.21966 20.7801C3.07901 20.6395 2.99999 20.4487 2.99999 20.2498C2.99999 15.222 6.4228 14.3079 6.56812 14.2714C6.76118 14.2231 6.96551 14.2536 7.13614 14.356C7.30677 14.4584 7.42972 14.6244 7.47796 14.8175C7.5262 15.0105 7.49576 15.2148 7.39335 15.3855C7.29093 15.5561 7.12493 15.6791 6.93187 15.7273C6.84749 15.7507 4.82999 16.3404 4.53562 19.4642C7.65937 19.1698 8.24999 17.156 8.27437 17.0623C8.32409 16.8695 8.44838 16.7043 8.61989 16.6031C8.7914 16.5019 8.99608 16.473 9.1889 16.5228C9.38172 16.5725 9.54689 16.6968 9.64807 16.8683C9.74925 17.0398 9.77816 17.2445 9.72843 17.4373V17.4317ZM18.4472 11.1129L18 11.5601V17.0285C18.0011 17.2263 17.9629 17.4223 17.8876 17.6051C17.8122 17.7879 17.7013 17.954 17.5612 18.0935L14.3437 21.3092C14.205 21.4489 14.0399 21.5598 13.8581 21.6354C13.6763 21.7111 13.4813 21.7499 13.2844 21.7498C13.1218 21.7498 12.9604 21.7235 12.8062 21.672C12.5416 21.5844 12.3067 21.4246 12.1281 21.2106C11.9495 20.9965 11.8343 20.7368 11.7956 20.4607L11.2922 16.8523L7.14749 12.7076L3.54093 12.2042C3.2645 12.1654 3.00437 12.0503 2.78981 11.8717C2.57524 11.6932 2.41476 11.4583 2.3264 11.1935C2.23804 10.9287 2.22532 10.6446 2.28966 10.3729C2.35401 10.1013 2.49286 9.85305 2.69062 9.65605L5.90624 6.43855C6.04581 6.29847 6.21185 6.18755 6.39468 6.11222C6.57751 6.0369 6.7735 5.99869 6.97124 5.9998H12.4397L12.8869 5.55261C15.3881 3.0523 18.4022 2.94449 19.5816 3.0148C19.9472 3.03702 20.2922 3.19232 20.5512 3.45138C20.8103 3.71044 20.9656 4.05536 20.9878 4.42105C21.0562 5.59761 20.9484 8.61167 18.4481 11.1129H18.4472ZM3.74999 10.7192L7.2328 11.2048L10.9397 7.4998H6.97124L3.74999 10.7192ZM8.56124 11.9998L12 15.4385L17.3859 10.0526C18.1118 9.33167 18.6749 8.46372 19.0374 7.50708C19.4 6.55044 19.5535 5.52728 19.4878 4.50636C18.4674 4.44303 17.4453 4.59818 16.4896 4.96145C15.534 5.32472 14.6669 5.88775 13.9462 6.61292L8.56124 11.9998ZM16.5 13.0601L12.7941 16.766L13.2816 20.2498L16.5 17.0285V13.0601Z"
                                    fill="#198754" />
                              </svg>
                           </span>
                           <h1 class="fs-5 mb-0">ROI-driven SEO company</h1>
                        </div>
                        <div>
                           <h2 class="display-6 mb-3">
                              Finally, an 
                              <span class="text-primary">SEO company</span>
                              that delivers results
                           </h2>
                           <p class="lead">
                              Partner with
                              <span class="text-primary fw-semibold">[Agency Name]</span>
                              for expert SEO services that deliver measurable results.
                           </p>
                           <ul class="list-unstyled d-flex flex-column gap-2">
                              <li class="d-flex flex-row gap-2">
                                 <span>
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-check-circle text-success" viewBox="0 0 16 16">
                                       <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14m0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16" />
                                       <path d="m10.97 4.97-.02.022-3.473 4.425-2.093-2.094a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-1.071-1.05" />
                                    </svg>
                                 </span>
                                 <span>Rankings Improvement</span>
                              </li>
                              <li class="d-flex flex-row gap-2">
                                 <span>
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-check-circle text-success" viewBox="0 0 16 16">
                                       <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14m0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16" />
                                       <path d="m10.97 4.97-.02.022-3.473 4.425-2.093-2.094a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-1.071-1.05" />
                                    </svg>
                                 </span>
                                 <span>Boost organic traffic</span>
                              </li>
                              <li class="d-flex flex-row gap-2">
                                 <span>
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-check-circle text-success" viewBox="0 0 16 16">
                                       <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14m0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16" />
                                       <path d="m10.97 4.97-.02.022-3.473 4.425-2.093-2.094a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-1.071-1.05" />
                                    </svg>
                                 </span>
                                 <span>Conversion Rate Enhancement</span>
                              </li>
                           </ul>
                        </div>

                        <div class="d-flex flex-row gap-3 align-items-center">
                           <a href="#" class="btn btn-warning" data-bs-toggle="modal" data-bs-target="#bookConsultantModal">Book a consultation</a>
                           <span>
                              <span>
                                 <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path
                                       opacity="0.2"
                                       d="M13.9963 10.8797C13.8991 11.6054 13.5418 12.2711 12.9907 12.753C12.4395 13.2349 11.7321 13.5003 11 13.4997C8.74566 13.4997 6.58365 12.6042 4.98959 11.0101C3.39553 9.41609 2.5 7.25408 2.5 4.99974C2.49944 4.26762 2.7648 3.56021 3.24673 3.00908C3.72865 2.45794 4.39435 2.10059 5.12 2.00349C5.22727 1.99075 5.33578 2.01309 5.4293 2.06717C5.52281 2.12125 5.5963 2.20416 5.63875 2.30349L6.95938 5.25349C6.99182 5.32926 7.00504 5.41187 6.99784 5.49398C6.99064 5.57609 6.96326 5.65515 6.91813 5.72412L5.5825 7.31224C5.53512 7.38373 5.50711 7.46629 5.50119 7.55185C5.49528 7.63741 5.51166 7.72304 5.54875 7.80037C6.06563 8.85849 7.15938 9.93912 8.22063 10.451C8.29836 10.4879 8.38439 10.5039 8.47021 10.4975C8.55602 10.491 8.63867 10.4623 8.71 10.4141L10.2725 9.08287C10.3417 9.03679 10.4214 9.00872 10.5042 9.00118C10.5871 8.99365 10.6705 9.00691 10.7469 9.03974L13.6944 10.3604C13.7942 10.4025 13.8776 10.476 13.932 10.5697C13.9865 10.6633 14.009 10.7722 13.9963 10.8797Z"
                                       fill="#8B3DFF" />
                                    <path
                                       d="M9.51688 2.3705C9.53385 2.30704 9.56316 2.24755 9.60313 2.19543C9.64311 2.14331 9.69296 2.09957 9.74984 2.06672C9.80672 2.03387 9.86951 2.01254 9.93464 2.00397C9.99976 1.9954 10.0659 1.99974 10.1294 2.01675C11.0563 2.25859 11.902 2.74315 12.5794 3.42052C13.2567 4.09789 13.7413 4.94358 13.9831 5.8705C14.0001 5.93394 14.0045 6.00012 13.9959 6.06524C13.9873 6.13037 13.966 6.19316 13.9332 6.25004C13.9003 6.30692 13.8566 6.35677 13.8044 6.39675C13.7523 6.43672 13.6928 6.46603 13.6294 6.483C13.5871 6.49409 13.5437 6.49976 13.5 6.49987C13.3898 6.49987 13.2827 6.46347 13.1954 6.39634C13.108 6.3292 13.0453 6.23509 13.0169 6.12862C12.8196 5.37195 12.4242 4.68157 11.8712 4.12864C11.3183 3.57571 10.6279 3.18024 9.87126 2.983C9.80774 2.96609 9.74819 2.93683 9.696 2.89689C9.6438 2.85695 9.6 2.80711 9.56708 2.75022C9.53417 2.69333 9.51279 2.63051 9.50418 2.56536C9.49557 2.5002 9.49988 2.43399 9.51688 2.3705ZM9.37126 4.983C10.2331 5.213 10.7869 5.76675 11.0169 6.62862C11.0453 6.73509 11.108 6.8292 11.1954 6.89634C11.2827 6.96347 11.3898 6.99987 11.5 6.99987C11.5437 6.99976 11.5871 6.99409 11.6294 6.983C11.6928 6.96603 11.7523 6.93672 11.8044 6.89675C11.8566 6.85677 11.9003 6.80692 11.9332 6.75004C11.966 6.69316 11.9873 6.63037 11.9959 6.56524C12.0045 6.50012 12.0001 6.43394 11.9831 6.3705C11.6631 5.173 10.8269 4.33675 9.62938 4.01675C9.56594 3.9998 9.49977 3.99551 9.43467 4.00413C9.36957 4.01275 9.3068 4.03411 9.24996 4.06699C9.19311 4.09987 9.1433 4.14362 9.10336 4.19575C9.06342 4.24788 9.03414 4.30737 9.01719 4.37081C9.00024 4.43426 8.99596 4.50042 9.00458 4.56552C9.0132 4.63062 9.03456 4.69339 9.06744 4.75023C9.10031 4.80708 9.14407 4.85689 9.1962 4.89683C9.24833 4.93677 9.30781 4.96605 9.37126 4.983ZM14.4925 10.9424C14.3811 11.7893 13.9652 12.5666 13.3224 13.1293C12.6797 13.6919 11.8542 14.0014 11 13.9999C6.03751 13.9999 2.00001 9.96237 2.00001 4.99987C1.99847 4.14568 2.30794 3.32014 2.87059 2.67743C3.43324 2.03473 4.21061 1.61882 5.05751 1.50737C5.27166 1.48122 5.48854 1.52504 5.67575 1.63227C5.86296 1.73951 6.01047 1.90441 6.09626 2.10237L7.41626 5.04925V5.05675C7.48194 5.20828 7.50906 5.37372 7.49521 5.5383C7.48136 5.70287 7.42696 5.86145 7.33688 5.99987C7.32563 6.01675 7.31376 6.03237 7.30126 6.048L6.00001 7.5905C6.46813 8.54175 7.46313 9.528 8.42688 9.99737L9.94813 8.703C9.96307 8.69044 9.97872 8.67875 9.99501 8.668C10.1333 8.57575 10.2924 8.51944 10.458 8.50416C10.6235 8.48888 10.7903 8.51512 10.9431 8.5805L10.9513 8.58425L13.8956 9.90362C14.0939 9.98911 14.2592 10.1365 14.3668 10.3237C14.4744 10.511 14.5185 10.728 14.4925 10.9424ZM13.5 10.8174C13.5 10.8174 13.4956 10.8174 13.4931 10.8174L10.5556 9.50175L9.03376 10.7961C9.01899 10.8086 9.00355 10.8203 8.98751 10.8311C8.8436 10.9271 8.67726 10.9842 8.50472 10.9966C8.33218 11.0091 8.15936 10.9767 8.00313 10.9024C6.83251 10.3367 5.66563 9.17862 5.09938 8.0205C5.0244 7.86541 4.99075 7.69358 5.0017 7.52166C5.01265 7.34975 5.06783 7.18357 5.16188 7.03925C5.17248 7.0223 5.18439 7.00621 5.19751 6.99112L6.50001 5.44675L5.18751 2.50925C5.18726 2.50675 5.18726 2.50424 5.18751 2.50175C4.58141 2.58081 4.02493 2.8781 3.62226 3.33795C3.2196 3.79779 2.99837 4.38865 3.00001 4.99987C3.00232 7.12089 3.84592 9.15438 5.34571 10.6542C6.8455 12.154 8.87898 12.9976 11 12.9999C11.6109 13.002 12.2016 12.7815 12.6617 12.3797C13.1218 11.9779 13.4198 11.4223 13.5 10.8167V10.8174Z"
                                       fill="#8B3DFF" />
                                 </svg>
                              </span>
                              <span>123-456-7890</span>
                           </span>
                        </div>
                     </div>
                  </div>
                  <div class="offset-xl-1 col-xl-6 col-lg-6" data-cue="zoomIn">
                     <div class="position-relative ms-lg-8 ms-xl-0 me-xl-7">
                        <img src="../assets/images/seo/hero-img.jpg" alt="" class="w-100 rounded-3" width="480" />
                        <div class="position-absolute text-center bottom-0 start-0 ms-lg-n8">
                           <div class="bg-white p-3 rounded-3 shadow-lg" style="width: 160px">
                              <div>
                                 <svg width="100" height="64" viewBox="0 0 119 64" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path
                                       d="M18.5191 30.7786L1.05347 63.7252L116.182 61.7405L114.656 4.58008L90.4425 23.6335L62.5803 15.6946L42.015 36.7328L18.5191 30.7786Z"
                                       fill="url(#paint0_linear_2198_942)" />
                                    <path
                                       d="M0.317742 61.1917C0.0145032 61.7753 0.241784 62.4942 0.825388 62.7974C1.40899 63.1007 2.12792 62.8734 2.43116 62.2898L0.317742 61.1917ZM18.1408 29.4728L18.4351 28.3189L17.5196 28.0854L17.0841 28.9237L18.1408 29.4728ZM41.939 35.5422L41.6447 36.6961L42.3069 36.8649L42.7868 36.3785L41.939 35.5422ZM62.4784 14.7217L62.7961 13.5741L62.1219 13.3874L61.6306 13.8854L62.4784 14.7217ZM90.3666 22.4429L90.0488 23.5906L90.628 23.7509L91.101 23.3803L90.3666 22.4429ZM118.739 0.213867L105.125 2.14612L113.605 12.9702L118.739 0.213867ZM2.43116 62.2898L19.1975 30.0218L17.0841 28.9237L0.317742 61.1917L2.43116 62.2898ZM17.8465 30.6267L41.6447 36.6961L42.2333 34.3883L18.4351 28.3189L17.8465 30.6267ZM42.7868 36.3785L63.3262 15.558L61.6306 13.8854L41.0913 34.7058L42.7868 36.3785ZM62.1607 15.8694L90.0488 23.5906L90.6843 21.2952L62.7961 13.5741L62.1607 15.8694ZM91.101 23.3803L111.037 7.76115L109.568 5.88636L89.6321 21.5055L91.101 23.3803Z"
                                       fill="#198754" />
                                    <defs>
                                       <linearGradient id="paint0_linear_2198_942" x1="56.3051" y1="4.97703" x2="64.641" y2="49.0381" gradientUnits="userSpaceOnUse">
                                          <stop stop-color="#198754" />
                                          <stop offset="0.963056" stop-color="white" stop-opacity="0.3" />
                                       </linearGradient>
                                    </defs>
                                 </svg>
                              </div>
                              <div class="fs-4 text-success fw-bold">+280%</div>
                              <div class="fs-6">Average return on investment</div>
                           </div>
                        </div>
                        <div class="position-absolute text-center top-0 end-0 me-lg-n8 mt-8">
                           <div class="bg-white p-3 rounded-3 shadow-lg" style="width: 160px">
                              <div>
                                 <svg width="100" height="71" viewBox="0 0 126 71" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path
                                       d="M20.9785 34.6954L3.22742 68.1804L122.581 64.5495L121.03 6.45508L94.0772 27.4336L65.7596 19.3649L44.8583 40.7469L20.9785 34.6954Z"
                                       fill="url(#paint0_linear_4102_1451)" />
                                    <path
                                       d="M3.55371 66.1631L20.594 33.3679L44.7811 39.5364L65.6561 18.3758L94 26.2231L122.836 3.63086"
                                       stroke="#0DCAF0"
                                       stroke-width="2.4206"
                                       stroke-linecap="round" />
                                    <circle cx="20.9786" cy="33.082" r="3.22747" fill="#0677AC" />
                                    <circle cx="3.22747" cy="66.9697" r="3.22747" fill="#0677AC" />
                                    <circle cx="45.1846" cy="38.7294" r="3.22747" fill="#0677AC" />
                                    <circle cx="65.3563" cy="18.5575" r="3.22747" fill="#0677AC" />
                                    <circle cx="93.5966" cy="25.8202" r="3.22747" fill="#0677AC" />
                                    <circle cx="122.644" cy="3.22747" r="3.22747" fill="#0677AC" />
                                    <defs>
                                       <linearGradient id="paint0_linear_4102_1451" x1="59.3819" y1="8.47224" x2="67.854" y2="53.2534" gradientUnits="userSpaceOnUse">
                                          <stop stop-color="#0DCAF0" />
                                          <stop offset="0.963056" stop-color="white" stop-opacity="0.3" />
                                       </linearGradient>
                                    </defs>
                                 </svg>
                              </div>
                              <div class="fs-4 text-info fw-bold">56.8K</div>
                              <div class="fs-6">Organic traffic per month</div>
                           </div>
                        </div>
                     </div>
                  </div>
               </div>
            </div>
         </section>
         <div class="position-relative d-none d-xl-block">
            <div class="position-absolute bottom-0 w-100 z-n1">
               <img src="../assets/images/seo/gradient.svg" alt="" class="w-100" />
            </div>
            <div class="d-flex position-absolute bottom-0 start-0 w-100 overflow-hidden mb-n1" style="color: var(--bs-body-bg)">
               <div class="position-relative start-50 translate-middle-x flex-shrink-0" style="width: 3774px">
                  <svg width="3774" height="99" viewBox="0 0 3774 99" fill="none" xmlns="http://www.w3.org/2000/svg">
                     <path d="M0 200.003C0 200.003 1137.52 0.188224 1873.5 0.000134392C2614.84 -0.189325 3774 200.003 3774 200.003H0Z" fill="currentColor"></path>
                  </svg>
               </div>
            </div>
         </div>
         <!--Hero end-->

         <section class="py-lg-8 py-5 mt-lg-7">
            <div class="container">
               <div class="row">
                  <div class="col-12">
                     <pre
                        class="language-markup"
                        tabindex="0"><code class="language-markup"><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>section</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>py-xl-8 py-lg-7 py-5 position-relative z-1<span class="token punctuation">"</span></span> <span class="token attr-name">data-cue</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>fadeIn<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                      <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>container pb-xl-8 mb-xl-8<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                         <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>row align-items-center gy-6<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                            <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>col-lg-5 col-xl-5<span class="token punctuation">"</span></span> <span class="token attr-name">data-cue</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>zoomOut<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                               <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>d-flex flex-column gap-4<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                                  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>d-flex flex-row gap-2 align-items-center lh-1<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                                     <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>span</span><span class="token punctuation">&gt;</span></span>
                                        <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>svg</span> <span class="token attr-name">width</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>24<span class="token punctuation">"</span></span> <span class="token attr-name">height</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>24<span class="token punctuation">"</span></span> <span class="token attr-name">viewBox</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>0 0 24 24<span class="token punctuation">"</span></span> <span class="token attr-name">fill</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>none<span class="token punctuation">"</span></span> <span class="token attr-name">xmlns</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>http://www.w3.org/2000/svg<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                                           <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>path</span>
                                              <span class="token attr-name">opacity</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>0.2<span class="token punctuation">"</span></span>
                                              <span class="token attr-name">d</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>M6.75 15L9 17.25C9 17.25 8.25 20.25 3.75 20.25C3.75 15.75 6.75 15 6.75 15ZM12.75 6.75H6.97031C6.77166 6.75009 6.58116 6.82899 6.44062 6.96937L3.22031 10.1897C3.12199 10.2882 3.05303 10.4122 3.02115 10.5477C2.98927 10.6832 2.99574 10.8249 3.03981 10.9569C3.08389 11.089 3.16384 11.2061 3.27073 11.2953C3.37761 11.3845 3.5072 11.4422 3.645 11.4619L7.5 12L12.75 6.75ZM12 16.5L12.5381 20.355C12.5578 20.4928 12.6155 20.6224 12.7047 20.7293C12.7939 20.8362 12.911 20.9161 13.0431 20.9602C13.1751 21.0043 13.3168 21.0107 13.4523 20.9788C13.5878 20.947 13.7118 20.878 13.8103 20.7797L17.0306 17.5594C17.171 17.4188 17.2499 17.2283 17.25 17.0297V11.25L12 16.5Z<span class="token punctuation">"</span></span>
                                              <span class="token attr-name">fill</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>#198754<span class="token punctuation">"</span></span> <span class="token punctuation">/&gt;</span></span>
                                           <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>path</span>
                                              <span class="token attr-name">d</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>M9.72843 17.4317C9.69186 17.577 8.7778 20.9998 3.74999 20.9998C3.55108 20.9998 3.36031 20.9208 3.21966 20.7801C3.07901 20.6395 2.99999 20.4487 2.99999 20.2498C2.99999 15.222 6.4228 14.3079 6.56812 14.2714C6.76118 14.2231 6.96551 14.2536 7.13614 14.356C7.30677 14.4584 7.42972 14.6244 7.47796 14.8175C7.5262 15.0105 7.49576 15.2148 7.39335 15.3855C7.29093 15.5561 7.12493 15.6791 6.93187 15.7273C6.84749 15.7507 4.82999 16.3404 4.53562 19.4642C7.65937 19.1698 8.24999 17.156 8.27437 17.0623C8.32409 16.8695 8.44838 16.7043 8.61989 16.6031C8.7914 16.5019 8.99608 16.473 9.1889 16.5228C9.38172 16.5725 9.54689 16.6968 9.64807 16.8683C9.74925 17.0398 9.77816 17.2445 9.72843 17.4373V17.4317ZM18.4472 11.1129L18 11.5601V17.0285C18.0011 17.2263 17.9629 17.4223 17.8876 17.6051C17.8122 17.7879 17.7013 17.954 17.5612 18.0935L14.3437 21.3092C14.205 21.4489 14.0399 21.5598 13.8581 21.6354C13.6763 21.7111 13.4813 21.7499 13.2844 21.7498C13.1218 21.7498 12.9604 21.7235 12.8062 21.672C12.5416 21.5844 12.3067 21.4246 12.1281 21.2106C11.9495 20.9965 11.8343 20.7368 11.7956 20.4607L11.2922 16.8523L7.14749 12.7076L3.54093 12.2042C3.2645 12.1654 3.00437 12.0503 2.78981 11.8717C2.57524 11.6932 2.41476 11.4583 2.3264 11.1935C2.23804 10.9287 2.22532 10.6446 2.28966 10.3729C2.35401 10.1013 2.49286 9.85305 2.69062 9.65605L5.90624 6.43855C6.04581 6.29847 6.21185 6.18755 6.39468 6.11222C6.57751 6.0369 6.7735 5.99869 6.97124 5.9998H12.4397L12.8869 5.55261C15.3881 3.0523 18.4022 2.94449 19.5816 3.0148C19.9472 3.03702 20.2922 3.19232 20.5512 3.45138C20.8103 3.71044 20.9656 4.05536 20.9878 4.42105C21.0562 5.59761 20.9484 8.61167 18.4481 11.1129H18.4472ZM3.74999 10.7192L7.2328 11.2048L10.9397 7.4998H6.97124L3.74999 10.7192ZM8.56124 11.9998L12 15.4385L17.3859 10.0526C18.1118 9.33167 18.6749 8.46372 19.0374 7.50708C19.4 6.55044 19.5535 5.52728 19.4878 4.50636C18.4674 4.44303 17.4453 4.59818 16.4896 4.96145C15.534 5.32472 14.6669 5.88775 13.9462 6.61292L8.56124 11.9998ZM16.5 13.0601L12.7941 16.766L13.2816 20.2498L16.5 17.0285V13.0601Z<span class="token punctuation">"</span></span>
                                              <span class="token attr-name">fill</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>#198754<span class="token punctuation">"</span></span> <span class="token punctuation">/&gt;</span></span>
                                        <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>svg</span><span class="token punctuation">&gt;</span></span>
                                     <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>span</span><span class="token punctuation">&gt;</span></span>
                                     <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>h1</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>fs-5 mb-0<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>ROI-driven SEO company<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>h1</span><span class="token punctuation">&gt;</span></span>
                                  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
                                  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span><span class="token punctuation">&gt;</span></span>
                                     <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>h2</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>display-6 mb-3<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                                        Finally, an
                                        <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>span</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>text-primary<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>SEO company<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>span</span><span class="token punctuation">&gt;</span></span>
                                        that delivers results
                                     <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>h2</span><span class="token punctuation">&gt;</span></span>
                                     <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>p</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>lead<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                                        Partner with
                                        <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>span</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>text-primary fw-semibold<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>[Agency Name]<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>span</span><span class="token punctuation">&gt;</span></span>
                                        for expert SEO services that deliver measurable results.
                                     <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>p</span><span class="token punctuation">&gt;</span></span>
                                     <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>ul</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>list-unstyled d-flex flex-column gap-2<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                                        <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>li</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>d-flex flex-row gap-2<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                                           <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>span</span><span class="token punctuation">&gt;</span></span>
                                              <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>svg</span> <span class="token attr-name">xmlns</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>http://www.w3.org/2000/svg<span class="token punctuation">"</span></span> <span class="token attr-name">width</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>16<span class="token punctuation">"</span></span> <span class="token attr-name">height</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>16<span class="token punctuation">"</span></span> <span class="token attr-name">fill</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>currentColor<span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>bi bi-check-circle text-success<span class="token punctuation">"</span></span> <span class="token attr-name">viewBox</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>0 0 16 16<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                                                 <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>path</span> <span class="token attr-name">d</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14m0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16<span class="token punctuation">"</span></span> <span class="token punctuation">/&gt;</span></span>
                                                 <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>path</span> <span class="token attr-name">d</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>m10.97 4.97-.02.022-3.473 4.425-2.093-2.094a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-1.071-1.05<span class="token punctuation">"</span></span> <span class="token punctuation">/&gt;</span></span>
                                              <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>svg</span><span class="token punctuation">&gt;</span></span>
                                           <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>span</span><span class="token punctuation">&gt;</span></span>
                                           <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>span</span><span class="token punctuation">&gt;</span></span>Rankings Improvement<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>span</span><span class="token punctuation">&gt;</span></span>
                                        <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>li</span><span class="token punctuation">&gt;</span></span>
                                        <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>li</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>d-flex flex-row gap-2<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                                           <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>span</span><span class="token punctuation">&gt;</span></span>
                                              <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>svg</span> <span class="token attr-name">xmlns</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>http://www.w3.org/2000/svg<span class="token punctuation">"</span></span> <span class="token attr-name">width</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>16<span class="token punctuation">"</span></span> <span class="token attr-name">height</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>16<span class="token punctuation">"</span></span> <span class="token attr-name">fill</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>currentColor<span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>bi bi-check-circle text-success<span class="token punctuation">"</span></span> <span class="token attr-name">viewBox</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>0 0 16 16<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                                                 <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>path</span> <span class="token attr-name">d</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14m0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16<span class="token punctuation">"</span></span> <span class="token punctuation">/&gt;</span></span>
                                                 <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>path</span> <span class="token attr-name">d</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>m10.97 4.97-.02.022-3.473 4.425-2.093-2.094a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-1.071-1.05<span class="token punctuation">"</span></span> <span class="token punctuation">/&gt;</span></span>
                                              <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>svg</span><span class="token punctuation">&gt;</span></span>
                                           <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>span</span><span class="token punctuation">&gt;</span></span>
                                           <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>span</span><span class="token punctuation">&gt;</span></span>Boost organic traffic<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>span</span><span class="token punctuation">&gt;</span></span>
                                        <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>li</span><span class="token punctuation">&gt;</span></span>
                                        <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>li</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>d-flex flex-row gap-2<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                                           <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>span</span><span class="token punctuation">&gt;</span></span>
                                              <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>svg</span> <span class="token attr-name">xmlns</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>http://www.w3.org/2000/svg<span class="token punctuation">"</span></span> <span class="token attr-name">width</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>16<span class="token punctuation">"</span></span> <span class="token attr-name">height</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>16<span class="token punctuation">"</span></span> <span class="token attr-name">fill</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>currentColor<span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>bi bi-check-circle text-success<span class="token punctuation">"</span></span> <span class="token attr-name">viewBox</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>0 0 16 16<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                                                 <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>path</span> <span class="token attr-name">d</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14m0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16<span class="token punctuation">"</span></span> <span class="token punctuation">/&gt;</span></span>
                                                 <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>path</span> <span class="token attr-name">d</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>m10.97 4.97-.02.022-3.473 4.425-2.093-2.094a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-1.071-1.05<span class="token punctuation">"</span></span> <span class="token punctuation">/&gt;</span></span>
                                              <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>svg</span><span class="token punctuation">&gt;</span></span>
                                           <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>span</span><span class="token punctuation">&gt;</span></span>
                                           <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>span</span><span class="token punctuation">&gt;</span></span>Conversion Rate Enhancement<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>span</span><span class="token punctuation">&gt;</span></span>
                                        <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>li</span><span class="token punctuation">&gt;</span></span>
                                     <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>ul</span><span class="token punctuation">&gt;</span></span>
                                  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>

                                  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>d-flex flex-row gap-3 align-items-center<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                                     <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>a</span> <span class="token attr-name">href</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>#<span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>btn btn-warning<span class="token punctuation">"</span></span> <span class="token attr-name">data-bs-toggle</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>modal<span class="token punctuation">"</span></span> <span class="token attr-name">data-bs-target</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>#bookConsultantModal<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Book a consultation<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>a</span><span class="token punctuation">&gt;</span></span>
                                     <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>span</span><span class="token punctuation">&gt;</span></span>
                                        <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>span</span><span class="token punctuation">&gt;</span></span>
                                           <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>svg</span> <span class="token attr-name">width</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>16<span class="token punctuation">"</span></span> <span class="token attr-name">height</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>16<span class="token punctuation">"</span></span> <span class="token attr-name">viewBox</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>0 0 16 16<span class="token punctuation">"</span></span> <span class="token attr-name">fill</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>none<span class="token punctuation">"</span></span> <span class="token attr-name">xmlns</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>http://www.w3.org/2000/svg<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                                              <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>path</span>
                                                 <span class="token attr-name">opacity</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>0.2<span class="token punctuation">"</span></span>
                                                 <span class="token attr-name">d</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>M13.9963 10.8797C13.8991 11.6054 13.5418 12.2711 12.9907 12.753C12.4395 13.2349 11.7321 13.5003 11 13.4997C8.74566 13.4997 6.58365 12.6042 4.98959 11.0101C3.39553 9.41609 2.5 7.25408 2.5 4.99974C2.49944 4.26762 2.7648 3.56021 3.24673 3.00908C3.72865 2.45794 4.39435 2.10059 5.12 2.00349C5.22727 1.99075 5.33578 2.01309 5.4293 2.06717C5.52281 2.12125 5.5963 2.20416 5.63875 2.30349L6.95938 5.25349C6.99182 5.32926 7.00504 5.41187 6.99784 5.49398C6.99064 5.57609 6.96326 5.65515 6.91813 5.72412L5.5825 7.31224C5.53512 7.38373 5.50711 7.46629 5.50119 7.55185C5.49528 7.63741 5.51166 7.72304 5.54875 7.80037C6.06563 8.85849 7.15938 9.93912 8.22063 10.451C8.29836 10.4879 8.38439 10.5039 8.47021 10.4975C8.55602 10.491 8.63867 10.4623 8.71 10.4141L10.2725 9.08287C10.3417 9.03679 10.4214 9.00872 10.5042 9.00118C10.5871 8.99365 10.6705 9.00691 10.7469 9.03974L13.6944 10.3604C13.7942 10.4025 13.8776 10.476 13.932 10.5697C13.9865 10.6633 14.009 10.7722 13.9963 10.8797Z<span class="token punctuation">"</span></span>
                                                 <span class="token attr-name">fill</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>#8B3DFF<span class="token punctuation">"</span></span> <span class="token punctuation">/&gt;</span></span>
                                              <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>path</span>
                                                 <span class="token attr-name">d</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>M9.51688 2.3705C9.53385 2.30704 9.56316 2.24755 9.60313 2.19543C9.64311 2.14331 9.69296 2.09957 9.74984 2.06672C9.80672 2.03387 9.86951 2.01254 9.93464 2.00397C9.99976 1.9954 10.0659 1.99974 10.1294 2.01675C11.0563 2.25859 11.902 2.74315 12.5794 3.42052C13.2567 4.09789 13.7413 4.94358 13.9831 5.8705C14.0001 5.93394 14.0045 6.00012 13.9959 6.06524C13.9873 6.13037 13.966 6.19316 13.9332 6.25004C13.9003 6.30692 13.8566 6.35677 13.8044 6.39675C13.7523 6.43672 13.6928 6.46603 13.6294 6.483C13.5871 6.49409 13.5437 6.49976 13.5 6.49987C13.3898 6.49987 13.2827 6.46347 13.1954 6.39634C13.108 6.3292 13.0453 6.23509 13.0169 6.12862C12.8196 5.37195 12.4242 4.68157 11.8712 4.12864C11.3183 3.57571 10.6279 3.18024 9.87126 2.983C9.80774 2.96609 9.74819 2.93683 9.696 2.89689C9.6438 2.85695 9.6 2.80711 9.56708 2.75022C9.53417 2.69333 9.51279 2.63051 9.50418 2.56536C9.49557 2.5002 9.49988 2.43399 9.51688 2.3705ZM9.37126 4.983C10.2331 5.213 10.7869 5.76675 11.0169 6.62862C11.0453 6.73509 11.108 6.8292 11.1954 6.89634C11.2827 6.96347 11.3898 6.99987 11.5 6.99987C11.5437 6.99976 11.5871 6.99409 11.6294 6.983C11.6928 6.96603 11.7523 6.93672 11.8044 6.89675C11.8566 6.85677 11.9003 6.80692 11.9332 6.75004C11.966 6.69316 11.9873 6.63037 11.9959 6.56524C12.0045 6.50012 12.0001 6.43394 11.9831 6.3705C11.6631 5.173 10.8269 4.33675 9.62938 4.01675C9.56594 3.9998 9.49977 3.99551 9.43467 4.00413C9.36957 4.01275 9.3068 4.03411 9.24996 4.06699C9.19311 4.09987 9.1433 4.14362 9.10336 4.19575C9.06342 4.24788 9.03414 4.30737 9.01719 4.37081C9.00024 4.43426 8.99596 4.50042 9.00458 4.56552C9.0132 4.63062 9.03456 4.69339 9.06744 4.75023C9.10031 4.80708 9.14407 4.85689 9.1962 4.89683C9.24833 4.93677 9.30781 4.96605 9.37126 4.983ZM14.4925 10.9424C14.3811 11.7893 13.9652 12.5666 13.3224 13.1293C12.6797 13.6919 11.8542 14.0014 11 13.9999C6.03751 13.9999 2.00001 9.96237 2.00001 4.99987C1.99847 4.14568 2.30794 3.32014 2.87059 2.67743C3.43324 2.03473 4.21061 1.61882 5.05751 1.50737C5.27166 1.48122 5.48854 1.52504 5.67575 1.63227C5.86296 1.73951 6.01047 1.90441 6.09626 2.10237L7.41626 5.04925V5.05675C7.48194 5.20828 7.50906 5.37372 7.49521 5.5383C7.48136 5.70287 7.42696 5.86145 7.33688 5.99987C7.32563 6.01675 7.31376 6.03237 7.30126 6.048L6.00001 7.5905C6.46813 8.54175 7.46313 9.528 8.42688 9.99737L9.94813 8.703C9.96307 8.69044 9.97872 8.67875 9.99501 8.668C10.1333 8.57575 10.2924 8.51944 10.458 8.50416C10.6235 8.48888 10.7903 8.51512 10.9431 8.5805L10.9513 8.58425L13.8956 9.90362C14.0939 9.98911 14.2592 10.1365 14.3668 10.3237C14.4744 10.511 14.5185 10.728 14.4925 10.9424ZM13.5 10.8174C13.5 10.8174 13.4956 10.8174 13.4931 10.8174L10.5556 9.50175L9.03376 10.7961C9.01899 10.8086 9.00355 10.8203 8.98751 10.8311C8.8436 10.9271 8.67726 10.9842 8.50472 10.9966C8.33218 11.0091 8.15936 10.9767 8.00313 10.9024C6.83251 10.3367 5.66563 9.17862 5.09938 8.0205C5.0244 7.86541 4.99075 7.69358 5.0017 7.52166C5.01265 7.34975 5.06783 7.18357 5.16188 7.03925C5.17248 7.0223 5.18439 7.00621 5.19751 6.99112L6.50001 5.44675L5.18751 2.50925C5.18726 2.50675 5.18726 2.50424 5.18751 2.50175C4.58141 2.58081 4.02493 2.8781 3.62226 3.33795C3.2196 3.79779 2.99837 4.38865 3.00001 4.99987C3.00232 7.12089 3.84592 9.15438 5.34571 10.6542C6.8455 12.154 8.87898 12.9976 11 12.9999C11.6109 13.002 12.2016 12.7815 12.6617 12.3797C13.1218 11.9779 13.4198 11.4223 13.5 10.8167V10.8174Z<span class="token punctuation">"</span></span>
                                                 <span class="token attr-name">fill</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>#8B3DFF<span class="token punctuation">"</span></span> <span class="token punctuation">/&gt;</span></span>
                                           <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>svg</span><span class="token punctuation">&gt;</span></span>
                                        <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>span</span><span class="token punctuation">&gt;</span></span>
                                        <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>span</span><span class="token punctuation">&gt;</span></span>123-456-7890<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>span</span><span class="token punctuation">&gt;</span></span>
                                     <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>span</span><span class="token punctuation">&gt;</span></span>
                                  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
                               <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
                            <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
                            <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>offset-xl-1 col-xl-6 col-lg-6<span class="token punctuation">"</span></span> <span class="token attr-name">data-cue</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>zoomIn<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                               <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>position-relative ms-lg-8 ms-xl-0 me-xl-7<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                                  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>img</span> <span class="token attr-name">src</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>../assets/images/seo/hero-img.jpg<span class="token punctuation">"</span></span> <span class="token attr-name">alt</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span><span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>w-100 rounded-3<span class="token punctuation">"</span></span> <span class="token attr-name">width</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>480<span class="token punctuation">"</span></span> <span class="token punctuation">/&gt;</span></span>
                                  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>position-absolute text-center bottom-0 start-0 ms-lg-n8<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                                     <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>bg-white p-3 rounded-3 shadow-lg<span class="token punctuation">"</span></span> <span class="token attr-name">style</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>width: 160px<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                                        <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span><span class="token punctuation">&gt;</span></span>
                                           <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>svg</span> <span class="token attr-name">width</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>100<span class="token punctuation">"</span></span> <span class="token attr-name">height</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>64<span class="token punctuation">"</span></span> <span class="token attr-name">viewBox</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>0 0 119 64<span class="token punctuation">"</span></span> <span class="token attr-name">fill</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>none<span class="token punctuation">"</span></span> <span class="token attr-name">xmlns</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>http://www.w3.org/2000/svg<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                                              <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>path</span>
                                                 <span class="token attr-name">d</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>M18.5191 30.7786L1.05347 63.7252L116.182 61.7405L114.656 4.58008L90.4425 23.6335L62.5803 15.6946L42.015 36.7328L18.5191 30.7786Z<span class="token punctuation">"</span></span>
                                                 <span class="token attr-name">fill</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>url(#paint0_linear_2198_942)<span class="token punctuation">"</span></span> <span class="token punctuation">/&gt;</span></span>
                                              <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>path</span>
                                                 <span class="token attr-name">d</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>M0.317742 61.1917C0.0145032 61.7753 0.241784 62.4942 0.825388 62.7974C1.40899 63.1007 2.12792 62.8734 2.43116 62.2898L0.317742 61.1917ZM18.1408 29.4728L18.4351 28.3189L17.5196 28.0854L17.0841 28.9237L18.1408 29.4728ZM41.939 35.5422L41.6447 36.6961L42.3069 36.8649L42.7868 36.3785L41.939 35.5422ZM62.4784 14.7217L62.7961 13.5741L62.1219 13.3874L61.6306 13.8854L62.4784 14.7217ZM90.3666 22.4429L90.0488 23.5906L90.628 23.7509L91.101 23.3803L90.3666 22.4429ZM118.739 0.213867L105.125 2.14612L113.605 12.9702L118.739 0.213867ZM2.43116 62.2898L19.1975 30.0218L17.0841 28.9237L0.317742 61.1917L2.43116 62.2898ZM17.8465 30.6267L41.6447 36.6961L42.2333 34.3883L18.4351 28.3189L17.8465 30.6267ZM42.7868 36.3785L63.3262 15.558L61.6306 13.8854L41.0913 34.7058L42.7868 36.3785ZM62.1607 15.8694L90.0488 23.5906L90.6843 21.2952L62.7961 13.5741L62.1607 15.8694ZM91.101 23.3803L111.037 7.76115L109.568 5.88636L89.6321 21.5055L91.101 23.3803Z<span class="token punctuation">"</span></span>
                                                 <span class="token attr-name">fill</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>#198754<span class="token punctuation">"</span></span> <span class="token punctuation">/&gt;</span></span>
                                              <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>defs</span><span class="token punctuation">&gt;</span></span>
                                                 <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>linearGradient</span> <span class="token attr-name">id</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>paint0_linear_2198_942<span class="token punctuation">"</span></span> <span class="token attr-name">x1</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>56.3051<span class="token punctuation">"</span></span> <span class="token attr-name">y1</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>4.97703<span class="token punctuation">"</span></span> <span class="token attr-name">x2</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>64.641<span class="token punctuation">"</span></span> <span class="token attr-name">y2</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>49.0381<span class="token punctuation">"</span></span> <span class="token attr-name">gradientUnits</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>userSpaceOnUse<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                                                    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>stop</span> <span class="token attr-name">stop-color</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>#198754<span class="token punctuation">"</span></span> <span class="token punctuation">/&gt;</span></span>
                                                    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>stop</span> <span class="token attr-name">offset</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>0.963056<span class="token punctuation">"</span></span> <span class="token attr-name">stop-color</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>white<span class="token punctuation">"</span></span> <span class="token attr-name">stop-opacity</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>0.3<span class="token punctuation">"</span></span> <span class="token punctuation">/&gt;</span></span>
                                                 <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>linearGradient</span><span class="token punctuation">&gt;</span></span>
                                              <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>defs</span><span class="token punctuation">&gt;</span></span>
                                           <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>svg</span><span class="token punctuation">&gt;</span></span>
                                        <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
                                        <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>fs-4 text-success fw-bold<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>+280%<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
                                        <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>fs-6<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Average return on investment<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
                                     <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
                                  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
                                  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>position-absolute text-center top-0 end-0 me-lg-n8 mt-8<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                                     <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>bg-white p-3 rounded-3 shadow-lg<span class="token punctuation">"</span></span> <span class="token attr-name">style</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>width: 160px<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                                        <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span><span class="token punctuation">&gt;</span></span>
                                           <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>svg</span> <span class="token attr-name">width</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>100<span class="token punctuation">"</span></span> <span class="token attr-name">height</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>71<span class="token punctuation">"</span></span> <span class="token attr-name">viewBox</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>0 0 126 71<span class="token punctuation">"</span></span> <span class="token attr-name">fill</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>none<span class="token punctuation">"</span></span> <span class="token attr-name">xmlns</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>http://www.w3.org/2000/svg<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                                              <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>path</span>
                                                 <span class="token attr-name">d</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>M20.9785 34.6954L3.22742 68.1804L122.581 64.5495L121.03 6.45508L94.0772 27.4336L65.7596 19.3649L44.8583 40.7469L20.9785 34.6954Z<span class="token punctuation">"</span></span>
                                                 <span class="token attr-name">fill</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>url(#paint0_linear_4102_1451)<span class="token punctuation">"</span></span> <span class="token punctuation">/&gt;</span></span>
                                              <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>path</span>
                                                 <span class="token attr-name">d</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>M3.55371 66.1631L20.594 33.3679L44.7811 39.5364L65.6561 18.3758L94 26.2231L122.836 3.63086<span class="token punctuation">"</span></span>
                                                 <span class="token attr-name">stroke</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>#0DCAF0<span class="token punctuation">"</span></span>
                                                 <span class="token attr-name">stroke-width</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>2.4206<span class="token punctuation">"</span></span>
                                                 <span class="token attr-name">stroke-linecap</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>round<span class="token punctuation">"</span></span> <span class="token punctuation">/&gt;</span></span>
                                              <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>circle</span> <span class="token attr-name">cx</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>20.9786<span class="token punctuation">"</span></span> <span class="token attr-name">cy</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>33.082<span class="token punctuation">"</span></span> <span class="token attr-name">r</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>3.22747<span class="token punctuation">"</span></span> <span class="token attr-name">fill</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>#0677AC<span class="token punctuation">"</span></span> <span class="token punctuation">/&gt;</span></span>
                                              <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>circle</span> <span class="token attr-name">cx</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>3.22747<span class="token punctuation">"</span></span> <span class="token attr-name">cy</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>66.9697<span class="token punctuation">"</span></span> <span class="token attr-name">r</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>3.22747<span class="token punctuation">"</span></span> <span class="token attr-name">fill</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>#0677AC<span class="token punctuation">"</span></span> <span class="token punctuation">/&gt;</span></span>
                                              <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>circle</span> <span class="token attr-name">cx</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>45.1846<span class="token punctuation">"</span></span> <span class="token attr-name">cy</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>38.7294<span class="token punctuation">"</span></span> <span class="token attr-name">r</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>3.22747<span class="token punctuation">"</span></span> <span class="token attr-name">fill</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>#0677AC<span class="token punctuation">"</span></span> <span class="token punctuation">/&gt;</span></span>
                                              <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>circle</span> <span class="token attr-name">cx</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>65.3563<span class="token punctuation">"</span></span> <span class="token attr-name">cy</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>18.5575<span class="token punctuation">"</span></span> <span class="token attr-name">r</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>3.22747<span class="token punctuation">"</span></span> <span class="token attr-name">fill</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>#0677AC<span class="token punctuation">"</span></span> <span class="token punctuation">/&gt;</span></span>
                                              <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>circle</span> <span class="token attr-name">cx</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>93.5966<span class="token punctuation">"</span></span> <span class="token attr-name">cy</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>25.8202<span class="token punctuation">"</span></span> <span class="token attr-name">r</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>3.22747<span class="token punctuation">"</span></span> <span class="token attr-name">fill</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>#0677AC<span class="token punctuation">"</span></span> <span class="token punctuation">/&gt;</span></span>
                                              <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>circle</span> <span class="token attr-name">cx</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>122.644<span class="token punctuation">"</span></span> <span class="token attr-name">cy</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>3.22747<span class="token punctuation">"</span></span> <span class="token attr-name">r</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>3.22747<span class="token punctuation">"</span></span> <span class="token attr-name">fill</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>#0677AC<span class="token punctuation">"</span></span> <span class="token punctuation">/&gt;</span></span>
                                              <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>defs</span><span class="token punctuation">&gt;</span></span>
                                                 <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>linearGradient</span> <span class="token attr-name">id</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>paint0_linear_4102_1451<span class="token punctuation">"</span></span> <span class="token attr-name">x1</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>59.3819<span class="token punctuation">"</span></span> <span class="token attr-name">y1</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>8.47224<span class="token punctuation">"</span></span> <span class="token attr-name">x2</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>67.854<span class="token punctuation">"</span></span> <span class="token attr-name">y2</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>53.2534<span class="token punctuation">"</span></span> <span class="token attr-name">gradientUnits</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>userSpaceOnUse<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                                                    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>stop</span> <span class="token attr-name">stop-color</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>#0DCAF0<span class="token punctuation">"</span></span> <span class="token punctuation">/&gt;</span></span>
                                                    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>stop</span> <span class="token attr-name">offset</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>0.963056<span class="token punctuation">"</span></span> <span class="token attr-name">stop-color</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>white<span class="token punctuation">"</span></span> <span class="token attr-name">stop-opacity</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>0.3<span class="token punctuation">"</span></span> <span class="token punctuation">/&gt;</span></span>
                                                 <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>linearGradient</span><span class="token punctuation">&gt;</span></span>
                                              <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>defs</span><span class="token punctuation">&gt;</span></span>
                                           <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>svg</span><span class="token punctuation">&gt;</span></span>
                                        <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
                                        <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>fs-4 text-info fw-bold<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>56.8K<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
                                        <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>fs-6<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Organic traffic per month<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
                                     <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
                                  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
                               <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
                            <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
                         <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
                      <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
                   <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>section</span><span class="token punctuation">&gt;</span></span>
                   <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>position-relative d-none d-xl-block<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                      <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>position-absolute bottom-0 w-100 z-n1<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                         <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>img</span> <span class="token attr-name">src</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>../assets/images/seo/gradient.svg<span class="token punctuation">"</span></span> <span class="token attr-name">alt</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span><span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>w-100<span class="token punctuation">"</span></span> <span class="token punctuation">/&gt;</span></span>
                      <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
                      <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>d-flex position-absolute bottom-0 start-0 w-100 overflow-hidden mb-n1<span class="token punctuation">"</span></span> <span class="token attr-name">style</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>color: var(--bs-body-bg)<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                         <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>position-relative start-50 translate-middle-x flex-shrink-0<span class="token punctuation">"</span></span> <span class="token attr-name">style</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>width: 3774px<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                            <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>svg</span> <span class="token attr-name">width</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>3774<span class="token punctuation">"</span></span> <span class="token attr-name">height</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>99<span class="token punctuation">"</span></span> <span class="token attr-name">viewBox</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>0 0 3774 99<span class="token punctuation">"</span></span> <span class="token attr-name">fill</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>none<span class="token punctuation">"</span></span> <span class="token attr-name">xmlns</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>http://www.w3.org/2000/svg<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                               <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>path</span> <span class="token attr-name">d</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>M0 200.003C0 200.003 1137.52 0.188224 1873.5 0.000134392C2614.84 -0.189325 3774 200.003 3774 200.003H0Z<span class="token punctuation">"</span></span> <span class="token attr-name">fill</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>currentColor<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>path</span><span class="token punctuation">&gt;</span></span>
                            <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>svg</span><span class="token punctuation">&gt;</span></span>
                         <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
                      <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
                   <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
                   <span class="token comment">&lt;!--Hero end--&gt;</span>
          </code></pre>
                  </div>
               </div>
            </div>
         </section>
      </main>
      <!-- Modal -->
      <div class="modal fade" id="bookConsultantModal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-hidden="true">
         <div class="modal-dialog modal-dialog-centered modal-lg">
            <div class="modal-content">
               <div class="container p-0">
                  <div class="row g-0">
                     <div class="col-lg-6 d-none d-lg-block">
                        <div class="bg-light border-end p-6 rounded-start-3 d-flex flex-column gap-6 object-fit-cover" style="min-height: 100%">
                           <div>
                              <ul class="list-unstyled d-flex flex-column gap-2">
                                 <li class="d-flex flex-row gap-2">
                                    <span>
                                       <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-check-circle text-success" viewBox="0 0 16 16">
                                          <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14m0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16" />
                                          <path d="m10.97 4.97-.02.022-3.473 4.425-2.093-2.094a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-1.071-1.05" />
                                       </svg>
                                    </span>
                                    <span>Rankings Improvement</span>
                                 </li>
                                 <li class="d-flex flex-row gap-2">
                                    <span>
                                       <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-check-circle text-success" viewBox="0 0 16 16">
                                          <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14m0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16" />
                                          <path d="m10.97 4.97-.02.022-3.473 4.425-2.093-2.094a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-1.071-1.05" />
                                       </svg>
                                    </span>
                                    <span>Boost organic traffic</span>
                                 </li>
                                 <li class="d-flex flex-row gap-2">
                                    <span>
                                       <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-check-circle text-success" viewBox="0 0 16 16">
                                          <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14m0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16" />
                                          <path d="m10.97 4.97-.02.022-3.473 4.425-2.093-2.094a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-1.071-1.05" />
                                       </svg>
                                    </span>
                                    <span>Conversion Rate Enhancement</span>
                                 </li>
                              </ul>
                           </div>
                           <div class="my-auto">
                              <p>
                                 “Working with [Agency Name] has been an absolute game-changer for our online presence. Their team took the time to understand our unique needs and crafted a tailored
                                 SEO strategy that has delivered incredible results.”
                              </p>
                              <div>
                                 <span class="text-dark fw-semibold">John D.</span>
                                 <p class="mb-0 fs-6">CEO of [Client Company]</p>
                                 <div>
                                    <img src="./assets/images/brand-logo/brand-logo-13.svg" alt="" />
                                 </div>
                              </div>
                           </div>
                           <div>
                              <span>Do you have a question?</span>
                              <h3 class="mt-2 mb-0">123-456-7890</h3>
                              <span>8 AM - 8 PM IST</span>
                           </div>
                        </div>
                     </div>
                     <div class="col-lg-6">
                        <div class="pt-4 px-4">
                           <div class="text-end">
                              <button type="button" class="btn-close small" data-bs-dismiss="modal" aria-label="Close"></button>
                           </div>
                        </div>
                        <div class="pb-4 px-6">
                           <h3 class="mb-4">Get Started Right Now!</h3>
                           <form class="needs-validation mb-3" novalidate="">
                              <div class="mb-3">
                                 <label for="FullnameInput" class="form-label visually-hidden">Full Name</label>
                                 <input type="text" class="form-control" id="FullnameInput" required="" placeholder="Name" />
                                 <div class="invalid-feedback">Please enter full name</div>
                              </div>

                              <div class="mb-3">
                                 <label for="EmailInput" class="form-label visually-hidden">Email</label>
                                 <input type="email" class="form-control" id="EmailInput" required="" placeholder="Email" />
                                 <div class="invalid-feedback">Please enter email.</div>
                              </div>
                              <div class="mb-3">
                                 <label for="webURL" class="form-label visually-hidden">Website</label>
                                 <input type="text" class="form-control" id="webURL" required="" placeholder="Website URL" />
                                 <div class="invalid-feedback">Please enter URL.</div>
                              </div>
                              <div class="mb-3">
                                 <label for="phone" class="form-label visually-hidden">Phone</label>
                                 <input type="tel" name="phone" placeholder="" class="form-control w-100" required autocomplete="phone" id="phone" />

                                 <div class="invalid-feedback">Please enter Phone.</div>
                              </div>

                              <div class="mb-3">
                                 <label for="messages" class="form-label visually-hidden">Messages</label>
                                 <textarea rows="3" class="form-control" id="messages" placeholder="Messages"></textarea>
                              </div>

                              <div class="">
                                 <button class="btn btn-primary" type="submit">Get Your Free SEO Consultation</button>
                              </div>
                           </form>
                           <div class="">
                              <small>
                                 By submitting this form, you are agree to the terms outlined in our 
                                 <a href="#" class="link-primary">privacy policy</a>
                                 .
                              </small>
                           </div>
                        </div>
                     </div>
                  </div>
               </div>
            </div>
         </div>
      </div>
      <footer class="pt-7">
   <div class="container">
      <!-- Footer 4 column -->
      <div class="row">
         <div class="col-lg-9 col-12">
            <div class="row" id="ft-links">
               <div class="col-lg-3 col-12">
                  <div class="position-relative">
                     <div class="mb-3 pb-2 d-flex justify-content-between border-bottom border-bottom-lg-0">
                        <h4>Service</h4>
                        <a class="d-block d-lg-none stretched-link text-body" data-bs-toggle="collapse" href="#collapseLanding" role="button" aria-expanded="true" aria-controls="collapseLanding">
                           <i class="bi bi-chevron-down"></i>
                        </a>
                     </div>
                     <div class="d-lg-block collapse show" id="collapseLanding" data-bs-parent="#ft-links" style="">
                        <ul class="list-unstyled mb-0 py-3 py-lg-0">
                           <li class="mb-2">
                              <a href="./index.html" class="text-decoration-none text-reset">Web App Development</a>
                           </li>
                           <li class="mb-2">
                              <a href="#!" class="text-decoration-none text-reset">Front End Development</a>
                           </li>
                           <li class="mb-2">
                              <a href="#!" class="text-decoration-none text-reset">MVP Development</a>
                           </li>
                           <li class="mb-2">
                              <a href="#!" class="text-decoration-none text-reset">Digital Marketing</a>
                           </li>
                           <li class="mb-2">
                              <a href="#!" class="text-decoration-none text-reset">Content Writing</a>
                           </li>
                        </ul>
                     </div>
                  </div>
               </div>
               <div class="col-lg-3 col-12">
                  <div>
                     <div class="mb-3 pb-2 d-flex justify-content-between border-bottom border-bottom-lg-0 position-relative">
                        <h4>About us</h4>
                        <a
                           class="d-block d-lg-none stretched-link text-body collapsed"
                           data-bs-toggle="collapse"
                           href="#collapseAccounts"
                           role="button"
                           aria-expanded="false"
                           aria-controls="collapseAccounts">
                           <i class="bi bi-chevron-down"></i>
                        </a>
                     </div>
                     <div class="collapse d-lg-block" id="collapseAccounts" data-bs-parent="#ft-links">
                        <ul class="list-unstyled mb-0 py-3 py-lg-0">
                           <li class="mb-2">
                              <a href="#!" class="text-decoration-none text-reset">Case Studies</a>
                           </li>
                           <li class="mb-2">
                              <a href="#!" class="text-decoration-none text-reset">Blog</a>
                           </li>
                           <li class="mb-2">
                              <a href="#!" class="text-decoration-none text-reset">Services</a>
                           </li>
                           <li class="mb-2">
                              <a href="#!" class="text-decoration-none text-reset">About</a>
                           </li>
                           <li class="mb-2">
                              <a href="#!" class="text-decoration-none text-reset">Career</a>
                           </li>
                        </ul>
                     </div>
                  </div>
               </div>
               <div class="col-lg-3 col-12">
                  <div class="mb-3 pb-2 d-flex justify-content-between border-bottom border-bottom-lg-0 position-relative">
                     <h4>Technology</h4>
                     <a
                        class="d-block d-lg-none stretched-link text-body collapsed"
                        data-bs-toggle="collapse"
                        href="#collapseResources"
                        role="button"
                        aria-expanded="false"
                        aria-controls="collapseResources">
                        <i class="bi bi-chevron-down"></i>
                     </a>
                  </div>
                  <div class="collapse d-lg-block" id="collapseResources" data-bs-parent="#ft-links">
                     <ul class="list-unstyled mb-0 py-3 py-lg-0">
                        <li class="mb-2">
                           <a href="./docs/index.html" class="text-decoration-none text-reset">Next.js</a>
                        </li>
                        <li class="mb-2">
                           <a href="#!" class="text-decoration-none text-reset">Sanity</a>
                        </li>
                        <li class="mb-2">
                           <a href="./changelog.html" class="text-decoration-none text-reset">Content ful</a>
                        </li>
                        <li class="mb-2">
                           <a href="#!" class="text-decoration-none text-reset">Vercel</a>
                        </li>
                        <li class="mb-2">
                           <a href="#!" class="text-decoration-none text-reset">Netlify</a>
                        </li>
                     </ul>
                  </div>
               </div>
               <div class="col-lg-3 col-12">
                  <div class="mb-3 pb-2 d-flex justify-content-between border-bottom border-bottom-lg-0 position-relative">
                     <h4>Locations</h4>
                     <a
                        class="d-block d-lg-none stretched-link text-body collapsed"
                        data-bs-toggle="collapse"
                        href="#collapseLocations"
                        role="button"
                        aria-expanded="false"
                        aria-controls="collapseLocations">
                        <i class="bi bi-chevron-down"></i>
                     </a>
                  </div>
                  <div class="collapse d-lg-block" id="collapseLocations" data-bs-parent="#ft-links">
                     <ul class="list-unstyled mb-0 py-3 py-lg-0">
                        <li class="mb-2">
                           <a href="./docs/index.html" class="text-decoration-none text-reset">India</a>
                        </li>
                        <li class="mb-2">
                           <a href="#!" class="text-decoration-none text-reset">Australia</a>
                        </li>
                        <li class="mb-2">
                           <a href="./changelog.html" class="text-decoration-none text-reset">Brazil</a>
                        </li>
                        <li class="mb-2">
                           <a href="#!" class="text-decoration-none text-reset">Canada</a>
                        </li>
                     </ul>
                  </div>
               </div>
            </div>
         </div>
         <div class="col-lg-3 col-12">
            <div class="me-7">
               <h4 class="mb-4">Headquarters</h4>
               <p class="text-body-secondary">Codescandy, 412, Residency Rd, Shanthala Nagar, Ashok Nagar, Bengaluru, Karnataka, India 560025</p>
            </div>
         </div>
      </div>
   </div>
   <div class="container mt-7 pt-lg-7 pb-4">
      <div class="row align-items-center">
         <div class="col-md-3">
            <a class="mb-4 mb-lg-0 d-block text-inverse" href="../index.html"><img src="./assets/images/logo/logo.svg" alt="" /></a>
         </div>
         <div class="col-md-9 col-lg-6">
            <div class="small mb-3 mb-lg-0 text-lg-center">
               Copyright © 2024

               <span class="text-primary"><a href="#">Block Bootstrap 5 Theme</a></span>
               | Designed by
               <span class="text-primary"><a href="#">CodesCandy</a></span>
            </div>
         </div>
         <div class="col-lg-3">
            <div class="text-lg-end d-flex align-items-center justify-content-lg-end">
               <div class="dropdown">
                  <button class="btn btn-light btn-icon rounded-circle d-flex align-items-center" type="button" aria-expanded="false" data-bs-toggle="dropdown" aria-label="Toggle theme (auto)">
                     <i class="bi theme-icon-active lh-1"><i class="bi theme-icon bi-sun-fill"></i></i>
                     <span class="visually-hidden bs-theme-text">Toggle theme</span>
                  </button>
                  <ul class="dropdown-menu dropdown-menu-end shadow" aria-labelledby="bs-theme-text">
                     <li>
                        <button type="button" class="dropdown-item d-flex align-items-center active" data-bs-theme-value="light" aria-pressed="true">
                           <i class="bi theme-icon bi-sun-fill"></i>
                           <span class="ms-2">Light</span>
                        </button>
                     </li>
                     <li>
                        <button type="button" class="dropdown-item d-flex align-items-center" data-bs-theme-value="dark" aria-pressed="false">
                           <i class="bi theme-icon bi-moon-stars-fill"></i>
                           <span class="ms-2">Dark</span>
                        </button>
                     </li>
                     <li>
                        <button type="button" class="dropdown-item d-flex align-items-center" data-bs-theme-value="auto" aria-pressed="false">
                           <i class="bi theme-icon bi-circle-half"></i>
                           <span class="ms-2">Auto</span>
                        </button>
                     </li>
                  </ul>
               </div>
               <div class="ms-3 d-flex gap-2">
                  <a href="#!" class="btn btn-instagram btn-light btn-icon">
                     <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-instagram" viewBox="0 0 16 16">
                        <path
                           d="M8 0C5.829 0 5.556.01 4.703.048 3.85.088 3.269.222 2.76.42a3.917 3.917 0 0 0-1.417.923A3.927 3.927 0 0 0 .42 2.76C.222 3.268.087 3.85.048 4.7.01 5.555 0 5.827 0 8.001c0 2.172.01 2.444.048 3.297.04.852.174 1.433.372 1.942.205.526.478.972.923 1.417.444.445.89.719 1.416.923.51.198 1.09.333 1.942.372C5.555 15.99 5.827 16 8 16s2.444-.01 3.298-.048c.851-.04 1.434-.174 1.943-.372a3.916 3.916 0 0 0 1.416-.923c.445-.445.718-.891.923-1.417.197-.509.332-1.09.372-1.942C15.99 10.445 16 10.173 16 8s-.01-2.445-.048-3.299c-.04-.851-.175-1.433-.372-1.941a3.926 3.926 0 0 0-.923-1.417A3.911 3.911 0 0 0 13.24.42c-.51-.198-1.092-.333-1.943-.372C10.443.01 10.172 0 7.998 0h.003zm-.717 1.442h.718c2.136 0 2.389.007 3.232.046.78.035 1.204.166 1.486.275.373.145.64.319.92.599.28.28.453.546.598.92.11.281.24.705.275 1.485.039.843.047 1.096.047 3.231s-.008 2.389-.047 3.232c-.035.78-.166 1.203-.275 1.485a2.47 2.47 0 0 1-.599.919c-.28.28-.546.453-.92.598-.28.11-.704.24-1.485.276-.843.038-1.096.047-3.232.047s-2.39-.009-3.233-.047c-.78-.036-1.203-.166-1.485-.276a2.478 2.478 0 0 1-.92-.598 2.48 2.48 0 0 1-.6-.92c-.109-.281-.24-.705-.275-1.485-.038-.843-.046-1.096-.046-3.233 0-2.136.008-2.388.046-3.231.036-.78.166-1.204.276-1.486.145-.373.319-.64.599-.92.28-.28.546-.453.92-.598.282-.11.705-.24 1.485-.276.738-.034 1.024-.044 2.515-.045v.002zm4.988 1.328a.96.96 0 1 0 0 1.92.96.96 0 0 0 0-1.92zm-4.27 1.122a4.109 4.109 0 1 0 0 8.217 4.109 4.109 0 0 0 0-8.217zm0 1.441a2.667 2.667 0 1 1 0 5.334 2.667 2.667 0 0 1 0-5.334z"></path>
                     </svg>
                  </a>
                  <a href="#!" class="btn btn-facebook btn-icon">
                     <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-facebook" viewBox="0 0 16 16">
                        <path
                           d="M16 8.049c0-4.446-3.582-8.05-8-8.05C3.58 0-.002 3.603-.002 8.05c0 4.017 2.926 7.347 6.75 7.951v-5.625h-2.03V8.05H6.75V6.275c0-2.017 1.195-3.131 3.022-3.131.876 0 1.791.157 1.791.157v1.98h-1.009c-.993 0-1.303.621-1.303 1.258v1.51h2.218l-.354 2.326H9.25V16c3.824-.604 6.75-3.934 6.75-7.951z"></path>
                     </svg>
                  </a>
                  <a href="#!" class="btn btn-twitter btn-icon">
                     <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-twitter" viewBox="0 0 16 16">
                        <path
                           d="M5.026 15c6.038 0 9.341-5.003 9.341-9.334 0-.14 0-.282-.006-.422A6.685 6.685 0 0 0 16 3.542a6.658 6.658 0 0 1-1.889.518 3.301 3.301 0 0 0 1.447-1.817 6.533 6.533 0 0 1-2.087.793A3.286 3.286 0 0 0 7.875 6.03a9.325 9.325 0 0 1-6.767-3.429 3.289 3.289 0 0 0 1.018 4.382A3.323 3.323 0 0 1 .64 6.575v.045a3.288 3.288 0 0 0 2.632 3.218 3.203 3.203 0 0 1-.865.115 3.23 3.23 0 0 1-.614-.057 3.283 3.283 0 0 0 3.067 2.277A6.588 6.588 0 0 1 .78 13.58a6.32 6.32 0 0 1-.78-.045A9.344 9.344 0 0 0 5.026 15z"></path>
                     </svg>
                  </a>
               </div>
            </div>
         </div>
      </div>
   </div>
</footer>
 <!--Offcanvas-->
<div class="offcanvas offcanvas-end" tabindex="-1" id="offcanvasRight" aria-labelledby="offcanvasRightLabel" style="width: 480px">
    <div class="offcanvas-header px-5">
        <h5 class="offcanvas-title" id="offcanvasRightLabel">Event Registration</h5>

        <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close"></button>
    </div>
    <div class="offcanvas-body pb-5 px-5">
        <p class="mb-4">Sign up for our free online event and get access to all the sessions, on-demand videos, and more.</p>
        <form class="row g-3 needs-validation" novalidate>
            <div class="col-md-12">
                <label for="eventNameInput" class="form-label">
                    Name
                    <span class="text-danger">*</span>
                </label>
                <input type="text" class="form-control" id="eventNameInput" required />
                <div class="invalid-feedback">Please enter name.</div>
            </div>
            <div class="col-md-12">
                <label for="eventJobTitleInput" class="form-label">
                    Job title
                    <span class="text-danger">*</span>
                </label>
                <input type="text" class="form-control" id="eventJobTitleInput" required />
                <div class="invalid-feedback">Please enter Job title.</div>
            </div>
            <div class="col-md-12">
                <label for="eventCompanyInput" class="form-label">
                    Company
                    <span class="text-danger">*</span>
                </label>
                <input type="text" class="form-control" id="eventCompanyInput" required />
                <div class="invalid-feedback">Please enter company.</div>
            </div>
            <div class="col-md-12">
                <label for="eventEmailInput" class="form-label">
                    Work email
                    <span class="text-danger">*</span>
                </label>
                <input type="email" class="form-control" id="eventEmailInput" required />
                <div class="invalid-feedback">Please enter email.</div>
            </div>
            <div class="col-md-12">
                <label for="eventPhoneInput" class="form-label">Phone</label>
                <input type="tel" class="form-control" id="eventPhoneInput" required />
                <div class="invalid-feedback">Please enter phone.</div>
            </div>
            <div class="col-md-12">
                <label for="eventCountryInput" class="form-label">
                    Country
                    <span class="text-danger">*</span>
                </label>
                <input type="text" class="form-control" id="eventCountryInput" required />
                <div class="invalid-feedback">Please enter country.</div>
            </div>
            <div class="col-md-12">
                <label for="eventRegionInput" class="form-label">
                    Which region are you based in?
                    <span class="text-danger">*</span>
                </label>
                <input type="text" class="form-control" id="eventRegionInput" required />
                <div class="invalid-feedback">Please enter region.</div>
            </div>
            <div class="col-md-12">
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="blockCheck" />
                    <label class="form-check-label ms-2 fs-6" for="blockCheck">
                        *Yes please, I'd like Block and affiliates to use my information for personalized communications, targeted advertising and campaign effectiveness. See the
                        <a href="#">Privacy Policy</a>
                        for more details.
                    </label>
                </div>
            </div>
            <div class="d-grid">
                <button class="btn btn-primary" type="submit">Register</button>
            </div>
            <small>All fields marked with an asterisk (*) are required</small>
        </form>
    </div>
</div>
<!--offcanvas-->
 <div class="btn-scroll-top">
   <svg class="progress-square svg-content" width="100%" height="100%" viewBox="0 0 40 40">
      <path d="M8 1H32C35.866 1 39 4.13401 39 8V32C39 35.866 35.866 39 32 39H8C4.13401 39 1 35.866 1 32V8C1 4.13401 4.13401 1 8 1Z" />
   </svg>
</div>
 <!-- Libs JS -->
<script src="../assets/libs/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
<script src="../assets/libs/simplebar/dist/simplebar.min.js"></script>
<script src="../assets/libs/headhesive/dist/headhesive.min.js"></script>

<!-- Theme JS -->
<script src="../assets/js/theme.min.js"></script>

      <script src="../assets/libs/prismjs/prism.js"></script>
      <script src="../assets/libs/prismjs/components/prism-scss.min.js"></script>
      <script src="../assets/libs/prismjs/plugins/toolbar/prism-toolbar.min.js"></script>
      <script src="../assets/libs/prismjs/plugins/copy-to-clipboard/prism-copy-to-clipboard.min.js"></script>

      <script src="../assets/libs/scrollcue/scrollCue.min.js"></script>
      <script src="../assets/js/vendors/scrollcue.js"></script>
      <script src="../assets/libs/intl-tel-input/build/js/intlTelInput.min.js"></script>
      <script src="../assets/js/vendors/intl-tel.js"></script>
   </body>
</html>
