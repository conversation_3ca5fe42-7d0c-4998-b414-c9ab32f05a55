<!doctype html>
<html lang="en">
   <head>
      @@include("partials/head/meta.html") @@include("partials/head/head-links.html")
      <title>Blog Single - Responsive Website Template | Block</title>
   </head>

   <body>
      @@include("partials/navbar.html",{ "classList": " navbar-light w-100" })
      <main>
         <div class="pattern-square"></div>

         <!--Blog single start-->
         <div class="py-xl-9 py-4">
            <div class="container">
               <div class="row">
                  <article class="col-lg-8 offset-lg-2">
                     <h1>Next Generation UI Design - Block Multipurpose theme</h1>
                     <div class="d-flex align-items-center mt-lg-6 mt-4">
                        <div class="me-5">
                           <span class="fs-6">Written by</span>
                           <div class="d-flex align-items-center mt-2">
                              <img src="@@webRoot/assets/images/avatar/avatar-1.jpg" alt="Avatar" class="avatar avatar-xs rounded-circle" />
                              <div class="ms-2">
                                 <a href="#" class="text-reset fs-6">Sandip Chauhan</a>
                              </div>
                           </div>
                        </div>
                        <div>
                           <span class="fs-6">Published on</span>
                           <div class="mt-2 text-dark">
                              <span class="fs-6">3 Oct, 2023</span>
                           </div>
                        </div>
                     </div>

                     <figure class="my-6">
                        <img src="./assets/images/blog/blog-img-6.jpg" alt="blog" class="rounded-3 img-fluid w-100" />
                     </figure>
                     <p>
                        Lorem ipsum dolor sit amet, consectetur adipiscing elit. Donec lacinia lorem non ullamcorper ornare. Sed mi mauris, porta non lectus sed, sollicitudin sagittis est. Aenean ac
                        eros a magna porta facilisis semper sed eros.
                     </p>
                     <div class="mt-6">
                        <h1>h1. heading</h1>
                        <h2>h2. heading</h2>
                        <h3>h3. heading</h3>
                        <h4>h4. heading</h4>
                        <h5>h5. heading</h5>
                        <h6>h6. heading</h6>
                     </div>

                     <div class="mt-6">
                        <h2>Paragraph</h2>
                        <p>
                           Lorem ipsum dolor sit amet, consectetur adipiscing elit. Donec lacinia lorem non ullamcorper ornare. Sed mi mauris, porta non lectus sed, sollicitudin sagittis est. Aenean
                           ac eros a magna porta facilisis semper sed eros. Lorem ipsum dolor sit amet, consectetur adipiscing elit.
                        </p>
                        <p>Donec lacinia lorem non ullamcorper ornare. Sed mi mauris, porta non lectus sed, sollicitudin sagittis est. Aenean ac eros a magna porta facilisis semper sed eros.</p>
                        <p><strong>This line rendered as bold text.</strong></p>
                        <p><em>This line rendered as italicized text.</em></p>
                        <p><u>This line of text will render as underlined.</u></p>
                     </div>

                     <div class="mt-6">
                        <h2>Unordered List</h2>
                        <ul>
                           <li>
                              Implements This is an
                              <a href="#!">external link</a>
                           </li>
                           <li>
                              Implements This is an
                              <a href="#!">inside link</a>
                           </li>
                           <li>An unordered list starts with the tag.</li>
                           <li>Each list item starts with the tag.</li>
                        </ul>
                     </div>

                     <div class="mt-6">
                        <h2>Ordered Lists</h2>
                        <ol>
                           <li>An ordered list starts with the tag.</li>
                           <li>Each list item starts with the tag.</li>
                           <li>The HTML tag defines an ordered list.</li>
                           <li>An ordered list can be numerical or alphabetical.</li>
                        </ol>
                     </div>

                     <div class="mt-6">
                        <h2>Blockquote</h2>
                        <blockquote class="blockquote">A well-known quote, contained in a blockquote element.</blockquote>
                     </div>

                     <figure class="my-6">
                        <img src="./assets/images/blog/blog-img-7.jpg" alt="blog" class="img-fluid rounded-3 w-100" />
                     </figure>

                     <p>
                        Praesent in euismod dolor. Sed faucibus elit rutrum diam tempus, at varius dui tempor. In non rhoncus leo. Vestibulum sed vehicula tellus. Donec maximus velit eget libero
                        congue, eu condimentum mauris efficitur.
                     </p>
                     <p>Quisque vel odio facilisis, eleifend massa et, tempus nisl. Vestibulum porta sapien in accumsan vulputate. In lectus massa, elementum in libero ut, tempor congue ligula.</p>
                  </article>
               </div>
            </div>
         </div>
         <!--Blog single end-->

         <!--Newsletter start-->
         <section class="mb-7">
            <div class="container">
               <div class="row">
                  <div class="col-lg-8 offset-lg-2">
                     <div class="bg-primary bg-opacity-10 px-5 pt-5 pb-7 mb-2 rounded-3 text-center">
                        <div class="icon-shape bg-primary bg-opacity-10 icon-xl rounded-circle mb-4">
                           <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" class="bi bi-envelope-check text-primary" viewBox="0 0 16 16">
                              <path
                                 d="M2 2a2 2 0 0 0-2 2v8.01A2 2 0 0 0 2 14h5.5a.5.5 0 0 0 0-1H2a1 1 0 0 1-.966-.741l5.64-3.471L8 9.583l7-4.2V8.5a.5.5 0 0 0 1 0V4a2 2 0 0 0-2-2H2Zm3.708 6.208L1 11.105V5.383l4.708 2.825ZM1 4.217V4a1 1 0 0 1 1-1h12a1 1 0 0 1 1 1v.217l-7 4.2-7-4.2Z" />
                              <path
                                 d="M16 12.5a3.5 3.5 0 1 1-7 0 3.5 3.5 0 0 1 7 0Zm-1.993-1.679a.5.5 0 0 0-.686.172l-1.17 1.95-.547-.547a.5.5 0 0 0-.708.708l.774.773a.75.75 0 0 0 1.174-.144l1.335-2.226a.5.5 0 0 0-.172-.686Z" />
                           </svg>
                        </div>
                        <div class="mb-4">
                           <h3>Subscribe to our newsletter</h3>
                           <p class="mb-0 mx-md-7 px-md-4">Lorem ipsum dolor sit amet consectetur adipiscing elit phasellus amet dui quam vitae quis leo velit aliquam.</p>
                        </div>
                        <form class="row g-3 needs-validation d-flex mx-md-7 px-md-4">
                           <div class="col-md-7 col-xl-8 col-12">
                              <label for="subscribeEmail" class="form-label visually-hidden">Email</label>

                              <input type="email" class="form-control" id="subscribeEmail" placeholder="Enter your email address" aria-label="Enter your business email" required />
                           </div>
                           <div class="col-md-5 col-xl-4 col-12">
                              <div class="d-grid">
                                 <button class="btn btn-primary shadow-sm" type="submit">Subscribe</button>
                              </div>
                           </div>
                        </form>
                     </div>
                  </div>
               </div>
            </div>
         </section>
         <!--Newsletter end-->
      </main>
      @@include("partials/footer.html") @@include("partials/btn-scroll-top.html") @@include("partials/scripts.html")
   </body>
</html>
