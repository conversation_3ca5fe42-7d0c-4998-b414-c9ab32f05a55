<!doctype html>
<html lang="en">
   <head>
      <!-- Required meta tags -->
<meta charset="utf-8" />
<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />

      <link rel="stylesheet" href="./assets/libs/swiper/swiper-bundle.min.css" />
      <!-- Favicon icon-->
<link rel="apple-touch-icon" sizes="180x180" href="./assets/images/favicon/apple-touch-icon.png" />
<link rel="icon" type="image/png" sizes="32x32" href="./assets/images/favicon/favicon-32x32.png" />
<link rel="icon" type="image/png" sizes="16x16" href="./assets/images/favicon/favicon-16x16.png" />
<link rel="manifest" href="./assets/images/favicon/site.webmanifest" />
<link rel="mask-icon" href="./assets/images/favicon/block-safari-pinned-tab.svg" color="#8b3dff" />
<link rel="shortcut icon" href="./assets/images/favicon/favicon.ico" />
<meta name="msapplication-TileColor" content="#8b3dff" />
<meta name="msapplication-config" content="./assets/images/favicon/tile.xml" />

<!-- Color modes -->
<script src="./assets/js/vendors/color-modes.js"></script>

<!-- Libs CSS -->
<link href="./assets/libs/simplebar/dist/simplebar.min.css" rel="stylesheet" />
<link href="./assets/libs/bootstrap-icons/font/bootstrap-icons.min.css" rel="stylesheet" />

<!-- Scroll Cue -->
<link rel="stylesheet" href="./assets/libs/scrollcue/scrollCue.css" />

<!-- Box icons -->
<link rel="stylesheet" href="./assets/fonts/css/boxicons.min.css" />

<!-- Theme CSS -->
<link rel="stylesheet" href="./assets/css/theme.min.css">

      <title>Saas v2 Business - Responsive Website Template | Block</title>
   </head>
   <body>
      <!-- Navbar -->
<header>
   <nav class="navbar navbar-expand-lg navbar-light w-100">
      <div class="container px-3">
         <a class="navbar-brand" href="./index.html"><img src="./assets/images/logo/logo.svg" alt /></a>
         <button class="navbar-toggler offcanvas-nav-btn" type="button">
            <i class="bi bi-list"></i>
         </button>
         <div class="offcanvas offcanvas-start offcanvas-nav" style="width: 20rem">
            <div class="offcanvas-header">
               <a href="./index.html" class="text-inverse"><img src="./assets/images/logo/logo.svg" alt /></a>
               <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close"></button>
            </div>
            <div class="offcanvas-body pt-0 align-items-center">
               <ul class="navbar-nav mx-auto align-items-lg-center">
                  <li class="nav-item">
                     <a class="nav-link" href="./home.html">Home</a>
                  </li>
                  <li class="nav-item">
                     <a class="nav-link" href="./scan-pay.html">Scan & Pay</a>
                  </li>
                  <li class="nav-item">
                     <a class="nav-link" href="./service.html">Services</a>
                  </li>
                  <li class="nav-item">
                     <a class="nav-link" href="./contact.html">Contact</a>
                  </li>
                  <li class="nav-item">
                     <a class="nav-link" href="./become-an-agent.html">Become An Agent</a>
                  </li>
               </ul>
               <div class="mt-3 mt-lg-0 d-flex align-items-center">
                  <a href="./signin.html" class="btn btn-light mx-2">Login</a>
                  <a href="https://play.google.com/store/apps/details?id=com.qsoft.aidapay&hl=en&pli=1" class="btn btn-primary">Create account</a>
               </div>
            </div>
         </div>
      </div>
   </nav>
</header>

      <main>
         <!--Hero section start-->
         <section class="container py-lg-8 py-5" data-cue="fadeIn">
            <div class="row justify-content-center">
               <div class="col-xl-8 col-lg-10 col-12" data-cues="zoomIn" data-group="page-title" data-delay="700">
                  <div class="text-center d-flex flex-column gap-5">
                     <div class="d-flex justify-content-center">
                        <span class="bg-primary bg-opacity-10 text-primary border-primary border p-2 fs-6 rounded-pill lh-1 d-flex align-items-center">
                           <span class="badge bg-primary">New</span>
                           <span class="ms-2">Introducing AI Editor</span>
                           <span class="ms-1">
                              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="17" viewBox="0 0 16 17" fill="none">
                                 <path
                                    fill-rule="evenodd"
                                    clip-rule="evenodd"
                                    d="M1 8.89181C1 8.7592 1.05268 8.63202 1.14645 8.53825C1.24021 8.44448 1.36739 8.39181 1.5 8.39181H13.293L10.146 5.24581C10.0521 5.15192 9.99937 5.02458 9.99937 4.89181C9.99937 4.75903 10.0521 4.63169 10.146 4.53781C10.2399 4.44392 10.3672 4.39117 10.5 4.39117C10.6328 4.39117 10.7601 4.44392 10.854 4.53781L14.854 8.53781C14.9006 8.58425 14.9375 8.63943 14.9627 8.70017C14.9879 8.76092 15.0009 8.82604 15.0009 8.89181C15.0009 8.95757 14.9879 9.02269 14.9627 9.08344C14.9375 9.14418 14.9006 9.19936 14.854 9.24581L10.854 13.2458C10.7601 13.3397 10.6328 13.3924 10.5 13.3924C10.3672 13.3924 10.2399 13.3397 10.146 13.2458C10.0521 13.1519 9.99937 13.0246 9.99937 12.8918C9.99937 12.759 10.0521 12.6317 10.146 12.5378L13.293 9.39181H1.5C1.36739 9.39181 1.24021 9.33913 1.14645 9.24536C1.05268 9.15159 1 9.02441 1 8.89181Z"
                                    fill="#8B3DFF" />
                              </svg>
                           </span>
                        </span>
                     </div>
                     <div class="d-flex flex-column gap-3 mx-lg-8">
                        <h1 class="mb-0 display-4">Build better products with customer feedback</h1>
                        <p class="mb-0 lead">Track, manage, engage, and analyse your customer feedback to make product decisions with ease.</p>
                     </div>
                     <div class="d-flex flex-row gap-4 justify-content-center">
                        <a href="#" class="btn btn-primary">Get Started</a>
                        <a href="#" class="icon-link icon-link-hover">
                           <span>Explore products</span>
                           <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" class="bi bi-arrow-right" viewBox="0 0 16 16">
                              <path
                                 fill-rule="evenodd"
                                 d="M1 8a.5.5 0 0 1 .5-.5h11.793l-3.147-3.146a.5.5 0 0 1 .708-.708l4 4a.5.5 0 0 1 0 .708l-4 4a.5.5 0 0 1-.708-.708L13.293 8.5H1.5A.5.5 0 0 1 1 8z"></path>
                           </svg>
                        </a>
                     </div>
                  </div>
               </div>
            </div>
         </section>
         <div class="pattern-square"></div>
         <section class="container py-lg-8 py-5">
            <div class="row justify-content-center">
               <div class="col-md-10 col-12">
                  <div class="text-center position-relative" data-cue="zoomIn">
                     <img src="./assets/images/landings/sass-v3/hero-app-screen.png" class="img-fluid bg-light p-3 rounded-3 border" alt="" />
                     <div class="position-absolute top-50 d-none d-lg-block ms-lg-n5" data-cue="slideInLeft">
                        <img src="./assets/images/landings/sass-v3/hero-frame-left.svg" alt="" />
                     </div>
                     <div class="position-absolute top-50 end-0 translate-middle me-n9 d-none d-lg-block">
                        <img src="./assets/images/landings/sass-v3/hero-frame-right-1.svg" class="me-n9 mb-4" alt="" data-cue="slideInRight" />
                        <br />
                        <img src="./assets/images/landings/sass-v3/hero-frame-right-2.svg" alt="" data-cue="slideInRight" />
                     </div>
                  </div>
               </div>
            </div>
         </section>
         <!--Hero section close-->

         <!--Trusted worldwide start-->
         <div class="py-5" data-cue="fadeIn">
            <div class="container py-2">
               <div class="row">
                  <div class="col-lg-10 offset-lg-1">
                     <div
                        class="swiper-container swiper"
                        id="swiper-1"
                        data-pagination-type=""
                        data-speed="400"
                        data-space-between="100"
                        data-pagination="true"
                        data-navigation="false"
                        data-autoplay="true"
                        data-autoplay-delay="3000"
                        data-breakpoints='{"480": {"slidesPerView": 2}, "768": {"slidesPerView": 3}, "1024": {"slidesPerView": 5}}'>
                        <div class="swiper-wrapper pb-6">
                           <div class="swiper-slide">
                              <figure class="text-center">
                                 <img src="./assets/images/client-logo/clients-logo-1.svg" alt="logo" />
                              </figure>
                           </div>
                           <div class="swiper-slide">
                              <figure class="text-center">
                                 <img src="./assets/images/client-logo/clients-logo-2.svg" alt="logo" />
                              </figure>
                           </div>
                           <div class="swiper-slide">
                              <figure class="text-center">
                                 <img src="./assets/images/client-logo/clients-logo-3.svg" alt="logo" />
                              </figure>
                           </div>
                           <div class="swiper-slide">
                              <figure class="text-center">
                                 <img src="./assets/images/client-logo/clients-logo-4.svg" alt="logo" />
                              </figure>
                           </div>
                           <div class="swiper-slide">
                              <figure class="text-center">
                                 <img src="./assets/images/client-logo/clients-logo-5.svg" alt="logo" />
                              </figure>
                           </div>
                           <!-- Add more slides as needed -->
                        </div>
                        <!-- Add Pagination -->
                        <div class="swiper-pagination"></div>
                        <!-- Add Navigation -->
                        <div class="swiper-navigation">
                           <div class="swiper-button-next"></div>
                           <div class="swiper-button-prev"></div>
                        </div>
                     </div>
                  </div>
               </div>
            </div>
         </div>
         <!--Trusted worldwide end-->

         <!--Feature to boost Start-->
         <section class="container py-lg-8 py-5" data-cue="fadeIn">
            <div class="row justify-content-center mb-8">
               <div class="col-xl-6 col-lg-10 col-12">
                  <div class="text-center d-flex flex-column gap-5">
                     <div class="d-flex justify-content-center">
                        <span class="bg-primary bg-opacity-10 text-primary border-primary border px-3 py-2 fs-6 rounded-pill lh-1 align-items-center d-flex">
                           <svg xmlns="http://www.w3.org/2000/svg" width="17" height="17" viewBox="0 0 17 17" fill="none">
                              <g clip-path="url(#clip0_1717_21196)">
                                 <path
                                    d="M11.7512 0.681967C11.8491 0.738706 11.9248 0.826957 11.966 0.932305C12.0073 1.03765 12.0116 1.15387 11.9782 1.26197L10.1772 7.11397H13.5002C13.5979 7.11393 13.6934 7.14249 13.775 7.19612C13.8566 7.24975 13.9207 7.3261 13.9594 7.41576C13.9981 7.50541 14.0098 7.60444 13.9928 7.70062C13.9759 7.7968 13.9312 7.88591 13.8642 7.95697L5.8642 16.457C5.78679 16.5393 5.68379 16.5929 5.57197 16.6092C5.46016 16.6255 5.34613 16.6034 5.24847 16.5466C5.15081 16.4898 5.07528 16.4015 5.03419 16.2963C4.9931 16.191 4.98887 16.0749 5.0222 15.967L6.8232 10.114H3.5002C3.40254 10.114 3.30702 10.0854 3.22541 10.0318C3.1438 9.97818 3.07968 9.90183 3.04096 9.81217C3.00225 9.72252 2.99064 9.62349 3.00757 9.52731C3.02449 9.43114 3.06921 9.34202 3.1362 9.27097L11.1362 0.770967C11.2135 0.688766 11.3164 0.635136 11.428 0.618796C11.5397 0.602456 11.6536 0.624367 11.7512 0.680967V0.681967ZM4.6572 9.11397H7.5002C7.57845 9.11395 7.65561 9.1323 7.72547 9.16754C7.79533 9.20278 7.85595 9.25392 7.90245 9.31686C7.94895 9.37979 7.98002 9.45276 7.99318 9.5299C8.00633 9.60703 8.0012 9.68618 7.9782 9.76097L6.6102 14.204L12.3422 8.11397H9.5002C9.42195 8.11398 9.34479 8.09563 9.27493 8.06039C9.20506 8.02515 9.14445 7.97401 9.09795 7.91108C9.05145 7.84814 9.02038 7.77517 9.00722 7.69804C8.99406 7.6209 8.99919 7.54176 9.0222 7.46697L10.3902 3.02397L4.6572 9.11397Z"
                                    fill="#8B3DFF" />
                              </g>
                              <defs>
                                 <clipPath id="clip0_1717_21196">
                                    <rect width="16" height="16" fill="white" transform="translate(0.5 0.614014)" />
                                 </clipPath>
                              </defs>
                           </svg>
                           <span class="ms-1 text-uppercase ls-md fw-semibold">Features</span>
                        </span>
                     </div>
                     <div class="d-flex flex-column gap-3 mx-lg-8">
                        <h1 class="mb-0">The features to boost your productivity</h1>
                        <p class="mb-0">Say hello to the Store. A home for Extensions published by our community of Developers using our API.</p>
                     </div>
                  </div>
               </div>
            </div>
            <div class="row g-6">
               <div class="col-12" data-cue="zoomIn">
                  <div class="border rounded-3 bg-white bg-opacity-25 overflow-hidden">
                     <div class="pattern-square"></div>
                     <div class="row align-items-center">
                        <div class="col-lg-6 col-12" data-cue="slideInLeft">
                           <div class="p-lg-8 p-5 my-lg-4 d-flex flex-column gap-4">
                              <div class="icon-shape icon-xl bg-primary bg-opacity-10 rounded-3 border border-primary">
                                 <svg xmlns="http://www.w3.org/2000/svg" width="26" height="27" viewBox="0 0 26 27" fill="none">
                                    <path
                                       d="M13 0.614014C10.4288 0.614014 7.91543 1.37645 5.77759 2.80491C3.63975 4.23337 1.97351 6.26369 0.989572 8.63913C0.0056327 11.0146 -0.251811 13.6284 0.249797 16.1502C0.751405 18.6719 1.98953 20.9883 3.80762 22.8064C5.6257 24.6245 7.94208 25.8626 10.4638 26.3642C12.9856 26.8658 15.5995 26.6084 17.9749 25.6244C20.3503 24.6405 22.3807 22.9743 23.8091 20.8364C25.2376 18.6986 26 16.1852 26 13.614C25.9964 10.1673 24.6256 6.86283 22.1884 4.42565C19.7512 1.98846 16.4467 0.617653 13 0.614014ZM13 24.614C10.8244 24.614 8.69767 23.9689 6.88873 22.7602C5.07979 21.5515 3.66989 19.8335 2.83733 17.8235C2.00477 15.8135 1.78693 13.6018 2.21137 11.468C2.63581 9.33423 3.68345 7.37422 5.22183 5.83584C6.76021 4.29746 8.72022 3.24981 10.854 2.82538C12.9878 2.40094 15.1995 2.61877 17.2095 3.45134C19.2195 4.2839 20.9375 5.6938 22.1462 7.50274C23.3549 9.31168 24 11.4384 24 13.614C23.9967 16.5304 22.8367 19.3264 20.7745 21.3885C18.7123 23.4507 15.9164 24.6107 13 24.614ZM17.7075 11.9065C17.8004 11.9994 17.8741 12.1097 17.9244 12.2311C17.9747 12.3525 18.0006 12.4826 18.0006 12.614C18.0006 12.7454 17.9747 12.8755 17.9244 12.9969C17.8741 13.1183 17.8004 13.2286 17.7075 13.3215C17.6146 13.4144 17.5043 13.4881 17.3829 13.5384C17.2615 13.5887 17.1314 13.6146 17 13.6146C16.8686 13.6146 16.7385 13.5887 16.6171 13.5384C16.4957 13.4881 16.3854 13.4144 16.2925 13.3215L14 11.0278V18.614C14 18.8792 13.8946 19.1336 13.7071 19.3211C13.5196 19.5087 13.2652 19.614 13 19.614C12.7348 19.614 12.4804 19.5087 12.2929 19.3211C12.1054 19.1336 12 18.8792 12 18.614V11.0278L9.70751 13.3215C9.51987 13.5092 9.26537 13.6146 9.00001 13.6146C8.73464 13.6146 8.48015 13.5092 8.29251 13.3215C8.10486 13.1339 7.99945 12.8794 7.99945 12.614C7.99945 12.3486 8.10486 12.0942 8.29251 11.9065L12.2925 7.90651C12.3854 7.81354 12.4957 7.73978 12.6171 7.68945C12.7385 7.63913 12.8686 7.61323 13 7.61323C13.1314 7.61323 13.2615 7.63913 13.3829 7.68945C13.5043 7.73978 13.6146 7.81354 13.7075 7.90651L17.7075 11.9065Z"
                                       fill="#8B3DFF" />
                                 </svg>
                              </div>
                              <div>
                                 <h3>One up your productivity</h3>
                                 <p class="mb-0 text-body">
                                    On top of the core Block Extensions already built in, you can install Extensions built by Developers from the community. Everything you’ve asked for, in one place.
                                 </p>
                              </div>
                              <div>
                                 <a href="#" class="btn btn-primary">Browse Extensions</a>
                              </div>
                           </div>
                        </div>
                        <div class="col-lg-6 col-12 d-lg-flex justify-content-center" data-cue="slideInRight">
                           <figure class="p-3 p-lg-0">
                              <img src="./assets/images/landings/sass-v3/feature-1.png" class="img-fluid" />
                           </figure>
                        </div>
                     </div>
                  </div>
               </div>
               <div class="col-lg-6 col-12" data-cue="zoomIn">
                  <div class="border rounded-3 bg-white bg-opacity-25 overflow-hidden h-100 position-relative">
                     <div class="pattern-square"></div>
                     <div class="p-lg-8 p-5 d-flex flex-column gap-4">
                        <div class="icon-shape icon-xl bg-warning bg-opacity-10 rounded-3 border border-warning">
                           <svg xmlns="http://www.w3.org/2000/svg" width="23" height="31" viewBox="0 0 23 31" fill="none">
                              <path
                                 d="M22.9738 14.3854C22.9359 14.2251 22.8591 14.0767 22.75 13.9532C22.641 13.8298 22.5032 13.7352 22.3488 13.6779L15.1475 10.9766L16.98 1.81038C17.0215 1.59741 16.9926 1.37672 16.8977 1.18161C16.8028 0.986501 16.6469 0.827565 16.4538 0.728783C16.2606 0.630002 16.0405 0.596735 15.8267 0.634003C15.613 0.671271 15.4172 0.777051 15.2688 0.935381L1.26879 15.9354C1.15508 16.0552 1.07283 16.2013 1.02937 16.3607C0.985913 16.52 0.982607 16.6877 1.01975 16.8486C1.05689 17.0096 1.13332 17.1588 1.24222 17.283C1.35111 17.4072 1.48908 17.5025 1.64379 17.5604L8.84754 20.2616L7.02004 29.4179C6.97855 29.6309 7.00745 29.8515 7.10238 30.0467C7.19732 30.2418 7.35314 30.4007 7.54632 30.4995C7.73951 30.5983 7.95959 30.6315 8.17334 30.5943C8.3871 30.557 8.58293 30.4512 8.73129 30.2929L22.7313 15.2929C22.8429 15.173 22.9234 15.0276 22.9657 14.8694C23.008 14.7111 23.0108 14.5449 22.9738 14.3854ZM9.67129 26.3641L10.98 19.8166C11.0269 19.5844 10.9898 19.3431 10.8753 19.1357C10.7608 18.9283 10.5765 18.7683 10.355 18.6841L3.75004 16.2029L14.3275 4.87038L13.02 11.4179C12.9732 11.6501 13.0103 11.8914 13.1248 12.0988C13.2392 12.3062 13.4236 12.4662 13.645 12.5504L20.245 15.0254L9.67129 26.3641Z"
                                 fill="#FFC107" />
                           </svg>
                        </div>
                        <div>
                           <h3>Automate your task</h3>
                           <p class="mb-0 text-body">Designed to be seamless and instant. View an Extension and hit to simply install it in milliseconds.</p>
                        </div>
                        <figure>
                           <img src="./assets/images/landings/sass-v3/feature-2.png" class="img-fluid" />
                        </figure>
                     </div>
                  </div>
               </div>
               <div class="col-lg-6 col-12" data-cue="zoomIn">
                  <div class="border rounded-3 bg-white bg-opacity-25 overflow-hidden h-100 position-relative">
                     <div class="pattern-square"></div>
                     <div class="p-lg-8 p-5 d-flex flex-column gap-4">
                        <div class="icon-shape icon-xl bg-info bg-opacity-10 rounded-3 border border-info">
                           <svg xmlns="http://www.w3.org/2000/svg" width="32" height="33" viewBox="0 0 32 33" fill="none">
                              <path opacity="0.2" d="M28 10.614L16 17.614L4 10.614L16 3.61401L28 10.614Z" fill="#0DCAF0" />
                              <path
                                 d="M28.8638 22.1139C28.9958 22.3431 29.0317 22.6153 28.9635 22.8709C28.8954 23.1265 28.7287 23.3447 28.5 23.4777L16.5 30.4777C16.3471 30.5668 16.1733 30.6138 15.9963 30.6138C15.8192 30.6138 15.6454 30.5668 15.4925 30.4777L3.4925 23.4777C3.26712 23.3422 3.10415 23.1235 3.03888 22.8688C2.9736 22.614 3.01128 22.3439 3.14375 22.1167C3.27622 21.8896 3.49282 21.7238 3.74666 21.6552C4.0005 21.5866 4.27114 21.6207 4.5 21.7502L16 28.4564L27.5 21.7502C27.7292 21.6181 28.0014 21.5822 28.257 21.6504C28.5126 21.7186 28.7308 21.8852 28.8638 22.1139ZM27.5 15.7502L16 22.4564L4.5 15.7502C4.27231 15.6368 4.00997 15.6145 3.76638 15.6877C3.5228 15.7609 3.31627 15.9242 3.18884 16.1444C3.06141 16.3645 3.02266 16.6249 3.08046 16.8726C3.13827 17.1203 3.28829 17.3367 3.5 17.4777L15.5 24.4777C15.6529 24.5668 15.8267 24.6138 16.0037 24.6138C16.1808 24.6138 16.3546 24.5668 16.5075 24.4777L28.5075 17.4777C28.6228 17.4125 28.7239 17.325 28.8051 17.2204C28.8863 17.1158 28.9459 16.9961 28.9804 16.8682C29.015 16.7404 29.0238 16.607 29.0064 16.4757C28.9889 16.3444 28.9456 16.2179 28.8789 16.1035C28.8122 15.9891 28.7234 15.8891 28.6177 15.8093C28.5121 15.7295 28.3916 15.6715 28.2633 15.6386C28.135 15.6057 28.0015 15.5987 27.8705 15.6178C27.7394 15.637 27.6135 15.682 27.5 15.7502ZM3 10.6139C3.0004 10.4388 3.04679 10.2668 3.13454 10.1152C3.22229 9.96362 3.34831 9.83774 3.5 9.75016L15.5 2.75016C15.6529 2.661 15.8267 2.61401 16.0037 2.61401C16.1808 2.61401 16.3546 2.661 16.5075 2.75016L28.5075 9.75016C28.6585 9.83823 28.7837 9.96433 28.8707 10.1159C28.9578 10.2674 29.0036 10.4391 29.0036 10.6139C29.0036 10.7887 28.9578 10.9604 28.8707 11.1119C28.7837 11.2635 28.6585 11.3896 28.5075 11.4777L16.5075 18.4777C16.3546 18.5668 16.1808 18.6138 16.0037 18.6138C15.8267 18.6138 15.6529 18.5668 15.5 18.4777L3.5 11.4777C3.34831 11.3901 3.22229 11.2642 3.13454 11.1126C3.04679 10.961 3.0004 10.7891 3 10.6139ZM5.985 10.6139L16 16.4564L26.015 10.6139L16 4.77141L5.985 10.6139Z"
                                 fill="#0DCAF0" />
                           </svg>
                        </div>
                        <div>
                           <h3>An ever-growing collection</h3>
                           <p class="mb-0 text-body">We offer seamless integration solutions that empower your business with enhanced efficiency and productivity.</p>
                        </div>
                        <figure>
                           <img src="./assets/images/landings/sass-v3/feature-3.png" class="img-fluid" />
                        </figure>
                     </div>
                  </div>
               </div>
            </div>
         </section>
         <!--Feature to boost end-->

         <!--More focus start-->
         <section class="container py-lg-8 py-5" data-cue="fadeIn">
            <div class="row mb-8">
               <div class="col-xl-5 col-lg-10 col-12">
                  <div class="d-flex flex-column gap-5">
                     <div class="d-flex">
                        <span class="bg-primary bg-opacity-10 text-primary border-primary border px-3 py-2 fs-6 rounded-pill lh-1 align-items-center d-flex">
                           <svg xmlns="http://www.w3.org/2000/svg" width="17" height="17" viewBox="0 0 17 17" fill="none">
                              <g clip-path="url(#clip0_1717_21196)">
                                 <path
                                    d="M11.7512 0.681967C11.8491 0.738706 11.9248 0.826957 11.966 0.932305C12.0073 1.03765 12.0116 1.15387 11.9782 1.26197L10.1772 7.11397H13.5002C13.5979 7.11393 13.6934 7.14249 13.775 7.19612C13.8566 7.24975 13.9207 7.3261 13.9594 7.41576C13.9981 7.50541 14.0098 7.60444 13.9928 7.70062C13.9759 7.7968 13.9312 7.88591 13.8642 7.95697L5.8642 16.457C5.78679 16.5393 5.68379 16.5929 5.57197 16.6092C5.46016 16.6255 5.34613 16.6034 5.24847 16.5466C5.15081 16.4898 5.07528 16.4015 5.03419 16.2963C4.9931 16.191 4.98887 16.0749 5.0222 15.967L6.8232 10.114H3.5002C3.40254 10.114 3.30702 10.0854 3.22541 10.0318C3.1438 9.97818 3.07968 9.90183 3.04096 9.81217C3.00225 9.72252 2.99064 9.62349 3.00757 9.52731C3.02449 9.43114 3.06921 9.34202 3.1362 9.27097L11.1362 0.770967C11.2135 0.688766 11.3164 0.635136 11.428 0.618796C11.5397 0.602456 11.6536 0.624367 11.7512 0.680967V0.681967ZM4.6572 9.11397H7.5002C7.57845 9.11395 7.65561 9.1323 7.72547 9.16754C7.79533 9.20278 7.85595 9.25392 7.90245 9.31686C7.94895 9.37979 7.98002 9.45276 7.99318 9.5299C8.00633 9.60703 8.0012 9.68618 7.9782 9.76097L6.6102 14.204L12.3422 8.11397H9.5002C9.42195 8.11398 9.34479 8.09563 9.27493 8.06039C9.20506 8.02515 9.14445 7.97401 9.09795 7.91108C9.05145 7.84814 9.02038 7.77517 9.00722 7.69804C8.99406 7.6209 8.99919 7.54176 9.0222 7.46697L10.3902 3.02397L4.6572 9.11397Z"
                                    fill="#8B3DFF" />
                              </g>
                              <defs>
                                 <clipPath id="clip0_1717_21196">
                                    <rect width="16" height="16" fill="white" transform="translate(0.5 0.614014)" />
                                 </clipPath>
                              </defs>
                           </svg>
                           <span class="ms-1 text-uppercase ls-md fw-semibold">Features</span>
                        </span>
                     </div>
                     <div class="d-flex flex-column gap-3">
                        <h1 class="mb-0">More focus, less clutter.</h1>
                        <p class="mb-0">Keep your workspace centralized, clean and tidy. Engineered with performance and your privacy in mind.</p>
                     </div>
                  </div>
               </div>
            </div>
            <div class="row gy-6 mb-6">
               <div class="col-lg-4 col-12" data-cue="zoomIn">
                  <div class="card overflow-hidden card-lift">
                     <div class="card-body me-xl-8">
                        <h4>Feedback Board</h4>
                        <p class="mb-0">Give your users a place to give feedback, and share ideas</p>
                     </div>
                     <div class="text-end ms-4">
                        <figure>
                           <img src="./assets/images/landings/sass-v3/feedback.png" class="img-fluid" alt="" />
                        </figure>
                     </div>
                  </div>
               </div>
               <div class="col-lg-4 col-12" data-cue="zoomIn">
                  <div class="card card-lift">
                     <div class="card-body me-xl-8">
                        <h4>Product Roadmap</h4>
                        <p class="mb-0">Public roadmap to show what you're working on next</p>
                     </div>
                     <div class="text-end ms-4">
                        <figure>
                           <img src="./assets/images/landings/sass-v3/product-roadmap.png" class="img-fluid" alt="" />
                        </figure>
                     </div>
                  </div>
               </div>
               <div class="col-lg-4 col-12" data-cue="zoomIn">
                  <div class="card card-lift">
                     <div class="card-body me-xl-8">
                        <h4>Changelog</h4>
                        <p class="mb-0">Notify users what's new with product changelog</p>
                     </div>
                     <div class="text-end ms-4">
                        <figure>
                           <img src="./assets/images/landings/sass-v3/changelog.png" class="img-fluid" alt="" />
                        </figure>
                     </div>
                  </div>
               </div>
            </div>
            <div class="row row-cols-lg-3 row-cols-md-2 gy-5">
               <div class="col" data-cue="zoomIn" data-duration="1500">
                  <div class="card">
                     <div class="card-body">
                        <div class="d-flex">
                           <div>
                              <div class="icon-shape icon-lg bg-primary bg-opacity-10 border border-primary rounded-3">
                                 <svg xmlns="http://www.w3.org/2000/svg" width="18" height="27" viewBox="0 0 18 27" fill="none">
                                    <path
                                       d="M18 7.06901V2.61401C18 2.08358 17.7893 1.57487 17.4142 1.1998C17.0391 0.824727 16.5304 0.614014 16 0.614014H2C1.46957 0.614014 0.960859 0.824727 0.585786 1.1998C0.210714 1.57487 0 2.08358 0 2.61401V7.11401C0.000675612 7.42438 0.0732624 7.73038 0.212065 8.00798C0.350867 8.28559 0.552109 8.52725 0.8 8.71401L7.33375 13.614L0.8 18.514C0.552109 18.7008 0.350867 18.9424 0.212065 19.22C0.0732624 19.4977 0.000675612 19.8036 0 20.114V24.614C0 25.1444 0.210714 25.6532 0.585786 26.0282C0.960859 26.4033 1.46957 26.614 2 26.614H16C16.5304 26.614 17.0391 26.4033 17.4142 26.0282C17.7893 25.6532 18 25.1444 18 24.614V20.159C17.9993 19.8498 17.9273 19.545 17.7896 19.2681C17.652 18.9913 17.4523 18.7499 17.2062 18.5628L10.6588 13.614L17.2062 8.66526C17.4523 8.4781 17.652 8.23673 17.7896 7.95989C17.9273 7.68304 17.9993 7.37819 18 7.06901ZM2 2.61401H16V7.06901L15.2787 7.61401H2.66625L2 7.11401V2.61401ZM9 12.364L5.33375 9.61401H12.6338L9 12.364ZM16 24.614H2V20.114L8 15.614V18.614C8 18.8792 8.10536 19.1336 8.29289 19.3211C8.48043 19.5087 8.73478 19.614 9 19.614C9.26522 19.614 9.51957 19.5087 9.70711 19.3211C9.89464 19.1336 10 18.8792 10 18.614V15.624L16 20.159V24.614Z"
                                       fill="#8B3DFF" />
                                 </svg>
                              </div>
                           </div>
                           <div class="ms-4">
                              <h4>Real-time sync</h4>
                              <p class="mb-0">Update anything in Block and see the result. Works like magic.</p>
                           </div>
                        </div>
                     </div>
                  </div>
               </div>
               <div class="col" data-cue="zoomIn" data-duration="1500">
                  <div class="card">
                     <div class="card-body">
                        <div class="d-flex">
                           <div>
                              <div class="icon-shape icon-lg bg-primary bg-opacity-10 border border-primary rounded-3">
                                 <svg xmlns="http://www.w3.org/2000/svg" width="32" height="33" viewBox="0 0 32 33" fill="none">
                                    <path
                                       opacity="0.2"
                                       d="M23 16.614C23 17.9985 22.5895 19.3519 21.8203 20.503C21.0511 21.6542 19.9579 22.5514 18.6788 23.0812C17.3997 23.611 15.9922 23.7496 14.6344 23.4795C13.2765 23.2094 12.0292 22.5427 11.0503 21.5638C10.0713 20.5848 9.4046 19.3375 9.13451 17.9796C8.86441 16.6218 9.00303 15.2143 9.53285 13.9352C10.0627 12.6561 10.9599 11.5629 12.111 10.7937C13.2622 10.0246 14.6155 9.61401 16 9.61401C17.8565 9.61401 19.637 10.3515 20.9497 11.6643C22.2625 12.977 23 14.7575 23 16.614Z"
                                       fill="#8B3DFF" />
                                    <path
                                       d="M15 5.61401V4.61401C15 4.3488 15.1054 4.09444 15.2929 3.90691C15.4804 3.71937 15.7348 3.61401 16 3.61401C16.2652 3.61401 16.5196 3.71937 16.7071 3.90691C16.8946 4.09444 17 4.3488 17 4.61401V5.61401C17 5.87923 16.8946 6.13358 16.7071 6.32112C16.5196 6.50866 16.2652 6.61401 16 6.61401C15.7348 6.61401 15.4804 6.50866 15.2929 6.32112C15.1054 6.13358 15 5.87923 15 5.61401ZM24 16.614C24 18.1963 23.5308 19.743 22.6518 21.0586C21.7727 22.3742 20.5233 23.3995 19.0615 24.0051C17.5997 24.6106 15.9911 24.769 14.4393 24.4603C12.8874 24.1516 11.462 23.3897 10.3431 22.2709C9.22433 21.152 8.4624 19.7266 8.15372 18.1747C7.84504 16.6229 8.00346 15.0144 8.60896 13.5525C9.21447 12.0907 10.2398 10.8413 11.5554 9.96226C12.871 9.08321 14.4177 8.61401 16 8.61401C18.121 8.61633 20.1545 9.45993 21.6543 10.9597C23.1541 12.4595 23.9977 14.493 24 16.614ZM22 16.614C22 15.4273 21.6481 14.2673 20.9888 13.2806C20.3295 12.2939 19.3925 11.5249 18.2961 11.0707C17.1997 10.6166 15.9933 10.4978 14.8295 10.7293C13.6656 10.9608 12.5965 11.5323 11.7574 12.3714C10.9182 13.2105 10.3468 14.2796 10.1153 15.4435C9.88378 16.6074 10.0026 17.8138 10.4567 18.9101C10.9108 20.0065 11.6799 20.9435 12.6666 21.6028C13.6533 22.2621 14.8133 22.614 16 22.614C17.5908 22.6124 19.116 21.9797 20.2408 20.8548C21.3657 19.73 21.9983 18.2048 22 16.614ZM7.2925 9.32151C7.48014 9.50915 7.73464 9.61457 8 9.61457C8.26536 9.61457 8.51986 9.50915 8.7075 9.32151C8.89514 9.13387 9.00056 8.87938 9.00056 8.61401C9.00056 8.34865 8.89514 8.09415 8.7075 7.90651L7.7075 6.90651C7.51986 6.71887 7.26536 6.61346 7 6.61346C6.73464 6.61346 6.48014 6.71887 6.2925 6.90651C6.10486 7.09415 5.99944 7.34865 5.99944 7.61401C5.99944 7.87938 6.10486 8.13387 6.2925 8.32151L7.2925 9.32151ZM7.2925 23.9065L6.2925 24.9065C6.10486 25.0942 5.99944 25.3486 5.99944 25.614C5.99944 25.8794 6.10486 26.1339 6.2925 26.3215C6.48014 26.5092 6.73464 26.6146 7 26.6146C7.26536 26.6146 7.51986 26.5092 7.7075 26.3215L8.7075 25.3215C8.80041 25.2286 8.87411 25.1183 8.92439 24.9969C8.97468 24.8755 9.00056 24.7454 9.00056 24.614C9.00056 24.4826 8.97468 24.3525 8.92439 24.2311C8.87411 24.1097 8.80041 23.9994 8.7075 23.9065C8.61459 23.8136 8.50429 23.7399 8.3829 23.6896C8.2615 23.6393 8.13139 23.6135 8 23.6135C7.86861 23.6135 7.7385 23.6393 7.6171 23.6896C7.49571 23.7399 7.38541 23.8136 7.2925 23.9065ZM24 9.61401C24.1314 9.61412 24.2615 9.58834 24.3829 9.53815C24.5042 9.48796 24.6146 9.41435 24.7075 9.32151L25.7075 8.32151C25.8951 8.13387 26.0006 7.87938 26.0006 7.61401C26.0006 7.34865 25.8951 7.09415 25.7075 6.90651C25.5199 6.71887 25.2654 6.61346 25 6.61346C24.7346 6.61346 24.4801 6.71887 24.2925 6.90651L23.2925 7.90651C23.1525 8.04637 23.0571 8.22462 23.0185 8.41871C22.9798 8.61279 22.9996 8.81398 23.0754 8.9968C23.1511 9.17963 23.2794 9.33586 23.444 9.44573C23.6086 9.5556 23.8021 9.61417 24 9.61401ZM24.7075 23.9065C24.5199 23.7189 24.2654 23.6135 24 23.6135C23.7346 23.6135 23.4801 23.7189 23.2925 23.9065C23.1049 24.0942 22.9994 24.3486 22.9994 24.614C22.9994 24.8794 23.1049 25.1339 23.2925 25.3215L24.2925 26.3215C24.3854 26.4144 24.4957 26.4881 24.6171 26.5384C24.7385 26.5887 24.8686 26.6146 25 26.6146C25.1314 26.6146 25.2615 26.5887 25.3829 26.5384C25.5043 26.4881 25.6146 26.4144 25.7075 26.3215C25.8004 26.2286 25.8741 26.1183 25.9244 25.9969C25.9747 25.8755 26.0006 25.7454 26.0006 25.614C26.0006 25.4826 25.9747 25.3525 25.9244 25.2311C25.8741 25.1097 25.8004 24.9994 25.7075 24.9065L24.7075 23.9065ZM5 15.614H4C3.73478 15.614 3.48043 15.7194 3.29289 15.9069C3.10536 16.0944 3 16.3488 3 16.614C3 16.8792 3.10536 17.1336 3.29289 17.3211C3.48043 17.5087 3.73478 17.614 4 17.614H5C5.26522 17.614 5.51957 17.5087 5.70711 17.3211C5.89464 17.1336 6 16.8792 6 16.614C6 16.3488 5.89464 16.0944 5.70711 15.9069C5.51957 15.7194 5.26522 15.614 5 15.614ZM16 26.614C15.7348 26.614 15.4804 26.7194 15.2929 26.9069C15.1054 27.0944 15 27.3488 15 27.614V28.614C15 28.8792 15.1054 29.1336 15.2929 29.3211C15.4804 29.5087 15.7348 29.614 16 29.614C16.2652 29.614 16.5196 29.5087 16.7071 29.3211C16.8946 29.1336 17 28.8792 17 28.614V27.614C17 27.3488 16.8946 27.0944 16.7071 26.9069C16.5196 26.7194 16.2652 26.614 16 26.614ZM28 15.614H27C26.7348 15.614 26.4804 15.7194 26.2929 15.9069C26.1054 16.0944 26 16.3488 26 16.614C26 16.8792 26.1054 17.1336 26.2929 17.3211C26.4804 17.5087 26.7348 17.614 27 17.614H28C28.2652 17.614 28.5196 17.5087 28.7071 17.3211C28.8946 17.1336 29 16.8792 29 16.614C29 16.3488 28.8946 16.0944 28.7071 15.9069C28.5196 15.7194 28.2652 15.614 28 15.614Z"
                                       fill="#8B3DFF" />
                                 </svg>
                              </div>
                           </div>
                           <div class="ms-4">
                              <h4>Dark mode</h4>
                              <p class="mb-0">For those who prefer Bootstrap 5 dark theme and love too work around.</p>
                           </div>
                        </div>
                     </div>
                  </div>
               </div>
               <div class="col" data-cue="zoomIn" data-duration="1500">
                  <div class="card">
                     <div class="card-body">
                        <div class="d-flex">
                           <div>
                              <div class="icon-shape icon-lg bg-primary bg-opacity-10 border border-primary rounded-3">
                                 <svg xmlns="http://www.w3.org/2000/svg" width="32" height="33" viewBox="0 0 32 33" fill="none">
                                    <path
                                       opacity="0.2"
                                       d="M25.9824 16.0114L28.0799 13.3864C27.797 12.323 27.3736 11.3019 26.8211 10.3502L23.4836 9.97519C23.217 9.6748 22.9327 9.39055 22.6324 9.12394L22.2574 5.78519C21.3046 5.23597 20.2832 4.81564 19.2199 4.53519L16.5949 6.63144C16.1935 6.60769 15.7912 6.60769 15.3899 6.63144L12.7649 4.53394C11.7048 4.81768 10.6872 5.24097 9.73861 5.79269L9.36361 9.13019C9.06323 9.3968 8.77898 9.68105 8.51236 9.98144L5.17361 10.3564C4.6244 11.3092 4.20407 12.3306 3.92361 13.3939L6.01986 16.0189C5.99611 16.4203 5.99611 16.8226 6.01986 17.2239L3.92236 19.8489C4.20527 20.9124 4.62858 21.9335 5.18111 22.8852L8.51861 23.2602C8.78523 23.5606 9.06948 23.8448 9.36986 24.1114L9.74486 27.4502C10.6976 27.9994 11.719 28.4197 12.7824 28.7002L15.4074 26.6039C15.8087 26.6277 16.211 26.6277 16.6124 26.6039L19.2374 28.7014C20.3008 28.4185 21.3219 27.9952 22.2736 27.4427L22.6486 24.1052C22.949 23.8386 23.2332 23.5543 23.4999 23.2539L26.8386 22.8789C27.3878 21.9262 27.8082 20.9048 28.0886 19.8414L25.9924 17.2164C26.0128 16.8149 26.0094 16.4126 25.9824 16.0114ZM15.9999 21.6139C15.011 21.6139 14.0443 21.3207 13.222 20.7713C12.3998 20.2219 11.7589 19.441 11.3805 18.5274C11.002 17.6137 10.903 16.6084 11.0959 15.6385C11.2889 14.6686 11.7651 13.7777 12.4643 13.0784C13.1636 12.3791 14.0545 11.9029 15.0244 11.71C15.9943 11.5171 16.9997 11.6161 17.9133 11.9945C18.8269 12.373 19.6078 13.0138 20.1572 13.8361C20.7066 14.6583 20.9999 15.625 20.9999 16.6139C20.9999 17.94 20.4731 19.2118 19.5354 20.1495C18.5977 21.0872 17.3259 21.6139 15.9999 21.6139Z"
                                       fill="#8B3DFF" />
                                    <path
                                       d="M16.0001 10.6141C14.8134 10.6141 13.6533 10.966 12.6666 11.6253C11.6799 12.2845 10.9109 13.2216 10.4568 14.318C10.0027 15.4143 9.88383 16.6207 10.1153 17.7846C10.3469 18.9485 10.9183 20.0176 11.7574 20.8567C12.5965 21.6958 13.6656 22.2673 14.8295 22.4988C15.9934 22.7303 17.1998 22.6115 18.2962 22.1573C19.3925 21.7032 20.3296 20.9342 20.9889 19.9475C21.6482 18.9608 22.0001 17.8008 22.0001 16.6141C21.9984 15.0233 21.3657 13.4981 20.2409 12.3733C19.116 11.2484 17.5908 10.6157 16.0001 10.6141ZM16.0001 20.6141C15.2089 20.6141 14.4356 20.3795 13.7778 19.9399C13.12 19.5004 12.6073 18.8757 12.3045 18.1448C12.0018 17.4139 11.9226 16.6096 12.0769 15.8337C12.2313 15.0578 12.6122 14.3451 13.1716 13.7856C13.731 13.2262 14.4438 12.8453 15.2197 12.6909C15.9956 12.5366 16.7999 12.6158 17.5308 12.9186C18.2617 13.2213 18.8864 13.734 19.3259 14.3918C19.7655 15.0496 20.0001 15.8229 20.0001 16.6141C20.0001 17.6749 19.5786 18.6924 18.8285 19.4425C18.0783 20.1926 17.0609 20.6141 16.0001 20.6141ZM27.0001 16.8841C27.0051 16.7041 27.0051 16.5241 27.0001 16.3441L28.8651 14.0141C28.9628 13.8917 29.0305 13.7481 29.0627 13.5949C29.0948 13.4416 29.0905 13.2829 29.0501 13.1316C28.7438 11.9825 28.2865 10.8791 27.6901 9.85032C27.6119 9.71572 27.5034 9.60122 27.3732 9.51593C27.243 9.43065 27.0947 9.37694 26.9401 9.35907L23.9751 9.02907C23.8517 8.89907 23.7267 8.77407 23.6001 8.65407L23.2501 5.68157C23.2321 5.52684 23.1782 5.37845 23.0926 5.24823C23.0071 5.11802 22.8924 5.00958 22.7576 4.93157C21.7288 4.33563 20.6254 3.87913 19.4763 3.57407C19.325 3.53365 19.1663 3.52933 19.013 3.56146C18.8597 3.59359 18.7161 3.66128 18.5938 3.75907L16.2701 5.61407C16.0901 5.61407 15.9101 5.61407 15.7301 5.61407L13.4001 3.75282C13.2777 3.65503 13.1341 3.58734 12.9808 3.55521C12.8276 3.52308 12.6689 3.5274 12.5176 3.56782C11.3685 3.8741 10.2651 4.33142 9.23631 4.92782C9.1017 5.00598 8.9872 5.11448 8.90192 5.24468C8.81664 5.37489 8.76292 5.5232 8.74506 5.67782L8.41506 8.64782C8.28506 8.77199 8.16006 8.89699 8.04006 9.02282L5.06756 9.36407C4.91282 9.38207 4.76443 9.43598 4.63422 9.52148C4.50401 9.60699 4.39557 9.72174 4.31756 9.85657C3.72174 10.8855 3.26485 11.9889 2.95881 13.1378C2.91855 13.2892 2.91444 13.448 2.94679 13.6013C2.97914 13.7545 3.04705 13.8981 3.14506 14.0203L5.00006 16.3441C5.00006 16.5241 5.00006 16.7041 5.00006 16.8841L3.13881 19.2141C3.04102 19.3364 2.97333 19.48 2.9412 19.6333C2.90906 19.7866 2.91338 19.9453 2.95381 20.0966C3.26009 21.2456 3.71741 22.349 4.31381 23.3778C4.39196 23.5124 4.50046 23.6269 4.63067 23.7122C4.76087 23.7975 4.90919 23.8512 5.06381 23.8691L8.02881 24.1991C8.15297 24.3291 8.27797 24.4541 8.40381 24.5741L8.75006 27.5466C8.76806 27.7013 8.82196 27.8497 8.90747 27.9799C8.99298 28.1101 9.10772 28.2186 9.24256 28.2966C10.2715 28.8924 11.3749 29.3493 12.5238 29.6553C12.6752 29.6956 12.834 29.6997 12.9872 29.6673C13.1405 29.635 13.2841 29.5671 13.4063 29.4691L15.7301 27.6141C15.9101 27.6191 16.0901 27.6191 16.2701 27.6141L18.6001 29.4791C18.7224 29.5769 18.866 29.6445 19.0193 29.6767C19.1726 29.7088 19.3312 29.7045 19.4826 29.6641C20.6318 29.3583 21.7352 28.901 22.7638 28.3041C22.8984 28.2259 23.0129 28.1174 23.0982 27.9872C23.1835 27.857 23.2372 27.7087 23.2551 27.5541L23.5851 24.5891C23.7151 24.4657 23.8401 24.3407 23.9601 24.2141L26.9326 23.8641C27.0873 23.8461 27.2357 23.7922 27.3659 23.7067C27.4961 23.6212 27.6045 23.5064 27.6826 23.3716C28.2784 22.3426 28.7353 21.2393 29.0413 20.0903C29.0816 19.9389 29.0857 19.7802 29.0533 19.6269C29.021 19.4736 28.9531 19.33 28.8551 19.2078L27.0001 16.8841ZM24.9876 16.0716C25.0088 16.4329 25.0088 16.7952 24.9876 17.1566C24.9727 17.404 25.0502 17.6481 25.2051 17.8416L26.9788 20.0578C26.7753 20.7047 26.5147 21.3321 26.2001 21.9328L23.3751 22.2528C23.129 22.2801 22.9019 22.3977 22.7376 22.5828C22.4969 22.8534 22.2407 23.1097 21.9701 23.3503C21.7849 23.5147 21.6674 23.7418 21.6401 23.9878L21.3263 26.8103C20.7257 27.1251 20.0982 27.3857 19.4513 27.5891L17.2338 25.8153C17.0564 25.6735 16.8359 25.5964 16.6088 25.5966H16.5488C16.1875 25.6178 15.8252 25.6178 15.4638 25.5966C15.2165 25.5824 14.9726 25.6598 14.7788 25.8141L12.5563 27.5891C11.9095 27.3855 11.282 27.1249 10.6813 26.8103L10.3613 23.9891C10.334 23.7431 10.2164 23.5159 10.0313 23.3516C9.76069 23.111 9.50442 22.8547 9.26381 22.5841C9.09947 22.399 8.87233 22.2814 8.62631 22.2541L5.80381 21.9391C5.48905 21.3384 5.22843 20.711 5.02506 20.0641L6.79881 17.8466C6.95369 17.6531 7.0312 17.409 7.01631 17.1616C6.99506 16.8002 6.99506 16.4379 7.01631 16.0766C7.0312 15.8292 6.95369 15.5851 6.79881 15.3916L5.02506 13.1703C5.22859 12.5235 5.4892 11.896 5.80381 11.2953L8.62506 10.9753C8.87108 10.948 9.09822 10.8304 9.26256 10.6453C9.50317 10.3747 9.75944 10.1184 10.0301 9.87782C10.2159 9.71338 10.334 9.48571 10.3613 9.23907L10.6751 6.41782C11.2757 6.10306 11.9032 5.84245 12.5501 5.63907L14.7676 7.41282C14.961 7.5677 15.2052 7.64521 15.4526 7.63032C15.8139 7.60907 16.1762 7.60907 16.5376 7.63032C16.7849 7.6445 17.0287 7.56707 17.2226 7.41282L19.4438 5.63907C20.0906 5.8426 20.7181 6.10321 21.3188 6.41782L21.6388 9.23907C21.6661 9.48509 21.7837 9.71224 21.9688 9.87657C22.2394 10.1172 22.4957 10.3735 22.7363 10.6441C22.9006 10.8292 23.1278 10.9468 23.3738 10.9741L26.1963 11.2878C26.5111 11.8884 26.7717 12.5159 26.9751 13.1628L25.2013 15.3803C25.0449 15.5754 24.9673 15.8221 24.9838 16.0716H24.9876Z"
                                       fill="#8B3DFF" />
                                 </svg>
                              </div>
                           </div>
                           <div class="ms-4">
                              <h4>Optimized</h4>
                              <p class="mb-0">All templates are optimized for all four breakpoints.</p>
                           </div>
                        </div>
                     </div>
                  </div>
               </div>
               <div class="col col-lg-6" data-cue="zoomIn" data-duration="1500">
                  <div class="card">
                     <div class="card-body">
                        <div class="d-flex">
                           <div>
                              <div class="icon-shape icon-lg bg-primary bg-opacity-10 border border-primary rounded-3">
                                 <svg xmlns="http://www.w3.org/2000/svg" width="32" height="33" viewBox="0 0 32 33" fill="none">
                                    <path
                                       opacity="0.2"
                                       d="M23.1248 15.989L15.8748 23.239C15.3122 23.8015 14.5491 24.1176 13.7535 24.1176C12.9579 24.1176 12.1949 23.8015 11.6323 23.239L8.87478 20.4777C8.31336 19.9153 7.99805 19.153 7.99805 18.3583C7.99805 17.5636 8.31336 16.8014 8.87478 16.239L16.1248 8.98896C16.6874 8.42639 17.4504 8.11035 18.246 8.11035C19.0416 8.11035 19.8047 8.42639 20.3673 8.98896L23.1248 11.7502C23.6862 12.3127 24.0015 13.0749 24.0015 13.8696C24.0015 14.6643 23.6862 15.4265 23.1248 15.989Z"
                                       fill="#8B3DFF" />
                                    <path
                                       d="M29.7076 2.40657C29.6147 2.31359 29.5044 2.23983 29.383 2.18951C29.2616 2.13918 29.1315 2.11328 29.0001 2.11328C28.8687 2.11328 28.7385 2.13918 28.6171 2.18951C28.4957 2.23983 28.3854 2.31359 28.2926 2.40657L21.7426 8.95782L21.0713 8.28532C20.3203 7.5365 19.3031 7.11601 18.2426 7.11601C17.1821 7.11601 16.1648 7.5365 15.4138 8.28532L12.5001 11.2003L11.7076 10.4066C11.5199 10.2189 11.2654 10.1135 11.0001 10.1135C10.7347 10.1135 10.4802 10.2189 10.2926 10.4066C10.1049 10.5942 9.99951 10.8487 9.99951 11.1141C9.99951 11.3794 10.1049 11.6339 10.2926 11.8216L11.0863 12.6141L8.17132 15.5278C7.79979 15.8993 7.50507 16.3403 7.30399 16.8256C7.10292 17.311 6.99942 17.8312 6.99942 18.3566C6.99942 18.8819 7.10292 19.4022 7.30399 19.8875C7.50507 20.3729 7.79979 20.8139 8.17132 21.1853L8.84382 21.8566L2.29257 28.4066C2.19966 28.4995 2.12596 28.6098 2.07567 28.7312C2.02539 28.8526 1.99951 28.9827 1.99951 29.1141C1.99951 29.2455 2.02539 29.3756 2.07567 29.497C2.12596 29.6184 2.19966 29.7287 2.29257 29.8216C2.48021 30.0092 2.7347 30.1146 3.00007 30.1146C3.13146 30.1146 3.26157 30.0887 3.38296 30.0385C3.50436 29.9882 3.61466 29.9145 3.70757 29.8216L10.2576 23.2703L10.9288 23.9428C11.68 24.6913 12.6972 25.1116 13.7576 25.1116C14.818 25.1116 15.8352 24.6913 16.5863 23.9428L19.5001 21.0278L20.2926 21.8216C20.3855 21.9145 20.4958 21.9882 20.6172 22.0385C20.7386 22.0887 20.8687 22.1146 21.0001 22.1146C21.1315 22.1146 21.2616 22.0887 21.383 22.0385C21.5044 21.9882 21.6147 21.9145 21.7076 21.8216C21.8005 21.7287 21.8742 21.6184 21.9245 21.497C21.9747 21.3756 22.0006 21.2455 22.0006 21.1141C22.0006 20.9827 21.9747 20.8526 21.9245 20.7312C21.8742 20.6098 21.8005 20.4995 21.7076 20.4066L20.9138 19.6141L23.8288 16.7003C24.2003 16.3289 24.4951 15.8879 24.6961 15.4025C24.8972 14.9172 25.0007 14.3969 25.0007 13.8716C25.0007 13.3462 24.8972 12.826 24.6961 12.3406C24.4951 11.8553 24.2003 11.4143 23.8288 11.0428L23.1563 10.3716L29.7076 3.82157C29.8005 3.72869 29.8743 3.61841 29.9246 3.49701C29.975 3.37561 30.0009 3.24548 30.0009 3.11407C30.0009 2.98265 29.975 2.85253 29.9246 2.73113C29.8743 2.60973 29.8005 2.49944 29.7076 2.40657ZM15.1713 22.5316C14.7963 22.9064 14.2878 23.1169 13.7576 23.1169C13.2274 23.1169 12.7189 22.9064 12.3438 22.5316L9.58632 19.7703C9.21153 19.3953 9.00099 18.8868 9.00099 18.3566C9.00099 17.8264 9.21153 17.3179 9.58632 16.9428L12.5001 14.0278L18.0863 19.6141L15.1713 22.5316ZM22.4138 15.2891L19.5001 18.2003L13.9138 12.6141L16.8288 9.70032C17.2039 9.32553 17.7124 9.115 18.2426 9.115C18.7728 9.115 19.2813 9.32553 19.6563 9.70032L22.4138 12.4503C22.5996 12.636 22.7469 12.8565 22.8475 13.0992C22.948 13.3419 22.9998 13.602 22.9998 13.8647C22.9998 14.1274 22.948 14.3875 22.8475 14.6302C22.7469 14.8728 22.5996 15.0933 22.4138 15.2791V15.2891ZM11.0513 4.43032C10.9674 4.17869 10.987 3.90406 11.1056 3.66682C11.2242 3.42959 11.4322 3.24919 11.6838 3.16532C11.9354 3.08144 12.2101 3.10096 12.4473 3.21958C12.6845 3.33819 12.8649 3.54619 12.9488 3.79782L13.9488 6.79782C13.9903 6.92241 14.0069 7.05396 13.9976 7.18496C13.9883 7.31596 13.9533 7.44385 13.8946 7.56131C13.8358 7.67878 13.7545 7.78353 13.6553 7.86958C13.5561 7.95562 13.4409 8.02129 13.3163 8.06282C13.0647 8.14669 12.7901 8.12717 12.5528 8.00856C12.4354 7.94983 12.3306 7.86853 12.2446 7.76932C12.1585 7.6701 12.0928 7.55491 12.0513 7.43032L11.0513 4.43032ZM3.05132 11.7978C3.09284 11.6732 3.1585 11.558 3.24455 11.4588C3.33059 11.3596 3.43534 11.2783 3.55281 11.2196C3.67028 11.1608 3.79817 11.1258 3.92917 11.1165C4.06017 11.1072 4.19173 11.1238 4.31632 11.1653L7.31632 12.1653C7.44091 12.2068 7.5561 12.2725 7.65532 12.3586C7.75453 12.4446 7.83582 12.5494 7.89456 12.6668C7.95329 12.7843 7.98831 12.9122 7.99762 13.0432C8.00693 13.1742 7.99035 13.3057 7.94882 13.4303C7.90729 13.5549 7.84162 13.6701 7.75557 13.7693C7.66953 13.8685 7.56478 13.9498 7.44731 14.0086C7.32985 14.0673 7.20196 14.1023 7.07096 14.1116C6.93996 14.1209 6.80841 14.1043 6.68382 14.0628L3.68382 13.0628C3.55922 13.0213 3.44402 12.9556 3.3448 12.8696C3.24558 12.7835 3.16429 12.6788 3.10555 12.5613C3.04682 12.4439 3.0118 12.316 3.00249 12.185C2.99319 12.054 3.00978 11.9224 3.05132 11.7978ZM28.9488 20.4303C28.9073 20.5549 28.8416 20.6701 28.7556 20.7693C28.6695 20.8686 28.5648 20.9498 28.4473 21.0086C28.3299 21.0673 28.202 21.1023 28.071 21.1116C27.94 21.1209 27.8084 21.1044 27.6838 21.0628L24.6838 20.0628C24.4322 19.9789 24.2242 19.7985 24.1056 19.5613C23.987 19.3241 23.9674 19.0494 24.0513 18.7978C24.1352 18.5462 24.3156 18.3382 24.5528 18.2196C24.7901 18.101 25.0647 18.0814 25.3163 18.1653L28.3163 19.1653C28.4409 19.2068 28.5561 19.2725 28.6553 19.3585C28.7546 19.4446 28.8358 19.5493 28.8946 19.6668C28.9533 19.7843 28.9883 19.9122 28.9976 20.0432C29.007 20.1742 28.9904 20.3057 28.9488 20.4303ZM20.9488 27.7978C21.0327 28.0494 21.0132 28.3241 20.8946 28.5613C20.7759 28.7985 20.5679 28.9789 20.3163 29.0628C20.0647 29.1467 19.7901 29.1272 19.5528 29.0086C19.3156 28.8899 19.1352 28.6819 19.0513 28.4303L18.0513 25.4303C17.9674 25.1787 17.987 24.9041 18.1056 24.6668C18.2242 24.4296 18.4322 24.2492 18.6838 24.1653C18.9354 24.0814 19.2101 24.101 19.4473 24.2196C19.6845 24.3382 19.8649 24.5462 19.9488 24.7978L20.9488 27.7978Z"
                                       fill="#8B3DFF" />
                                 </svg>
                              </div>
                           </div>
                           <div class="ms-4">
                              <h4>Plug & Play</h4>
                              <p class="mb-0">Get the block bootstrap 5 theme and use our ready to use bootstrap 5 components and sections examples.</p>
                           </div>
                        </div>
                     </div>
                  </div>
               </div>
               <div class="col col-lg-6" data-cue="zoomIn" data-duration="1500">
                  <div class="card">
                     <div class="card-body">
                        <div class="d-flex">
                           <div>
                              <div class="icon-shape icon-lg bg-primary bg-opacity-10 border border-primary rounded-3">
                                 <svg xmlns="http://www.w3.org/2000/svg" width="32" height="33" viewBox="0 0 32 33" fill="none">
                                    <path
                                       opacity="0.2"
                                       d="M14 21.114C14 21.9051 13.7654 22.6785 13.3259 23.3363C12.8864 23.9941 12.2616 24.5068 11.5307 24.8095C10.7998 25.1123 9.99556 25.1915 9.21964 25.0372C8.44372 24.8828 7.73098 24.5019 7.17157 23.9424C6.61216 23.383 6.2312 22.6703 6.07686 21.8944C5.92252 21.1185 6.00173 20.3142 6.30448 19.5833C6.60723 18.8524 7.11992 18.2277 7.77772 17.7881C8.43552 17.3486 9.20888 17.114 10 17.114C11.0609 17.114 12.0783 17.5354 12.8284 18.2856C13.5786 19.0357 14 20.0531 14 21.114ZM10 4.11402C9.20888 4.11402 8.43552 4.34861 7.77772 4.78814C7.11992 5.22766 6.60723 5.85238 6.30448 6.58328C6.00173 7.31419 5.92252 8.11845 6.07686 8.89438C6.2312 9.6703 6.61216 10.383 7.17157 10.9424C7.73098 11.5019 8.44372 11.8828 9.21964 12.0372C9.99556 12.1915 10.7998 12.1123 11.5307 11.8095C12.2616 11.5068 12.8864 10.9941 13.3259 10.3363C13.7654 9.6785 14 8.90514 14 8.11402C14 7.05315 13.5786 6.03573 12.8284 5.28559C12.0783 4.53544 11.0609 4.11402 10 4.11402ZM22 17.114C21.2089 17.114 20.4355 17.3486 19.7777 17.7881C19.1199 18.2277 18.6072 18.8524 18.3045 19.5833C18.0017 20.3142 17.9225 21.1185 18.0769 21.8944C18.2312 22.6703 18.6122 23.383 19.1716 23.9424C19.731 24.5019 20.4437 24.8828 21.2196 25.0372C21.9956 25.1915 22.7998 25.1123 23.5307 24.8095C24.2616 24.5068 24.8864 23.9941 25.3259 23.3363C25.7654 22.6785 26 21.9051 26 21.114C26 20.0531 25.5786 19.0357 24.8284 18.2856C24.0783 17.5354 23.0609 17.114 22 17.114ZM22 12.114C22.7911 12.114 23.5645 11.8794 24.2223 11.4399C24.8801 11.0004 25.3928 10.3757 25.6955 9.64475C25.9983 8.91385 26.0775 8.10958 25.9231 7.33365C25.7688 6.55773 25.3878 5.845 24.8284 5.28559C24.269 4.72618 23.5563 4.34522 22.7804 4.19087C22.0044 4.03653 21.2002 4.11575 20.4693 4.4185C19.7384 4.72125 19.1136 5.23394 18.6741 5.89173C18.2346 6.54953 18 7.32289 18 8.11402C18 9.17488 18.4214 10.1923 19.1716 10.9424C19.9217 11.6926 20.9391 12.114 22 12.114Z"
                                       fill="#8B3DFF" />
                                    <path
                                       d="M3.4 15.9141C3.50506 15.9929 3.62461 16.0502 3.75182 16.0828C3.87903 16.1154 4.01142 16.1226 4.14142 16.1041C4.27142 16.0855 4.39649 16.0415 4.50949 15.9746C4.62249 15.9077 4.72121 15.8192 4.8 15.7141C5.40546 14.9068 6.19055 14.2516 7.09311 13.8003C7.99567 13.3491 8.99091 13.1141 10 13.1141C11.0091 13.1141 12.0043 13.3491 12.9069 13.8003C13.8094 14.2516 14.5945 14.9068 15.2 15.7141C15.3593 15.9261 15.5963 16.0661 15.8588 16.1034C16.1214 16.1407 16.388 16.0721 16.6 15.9129C16.6756 15.8567 16.7425 15.7897 16.7987 15.7141C17.4042 14.9068 18.1893 14.2516 19.0919 13.8003C19.9944 13.3491 20.9897 13.1141 21.9988 13.1141C23.0078 13.1141 24.0031 13.3491 24.9056 13.8003C25.8082 14.2516 26.5933 14.9068 27.1987 15.7141C27.358 15.9263 27.5951 16.0665 27.8578 16.1039C28.1204 16.1413 28.3872 16.0728 28.5994 15.9135C28.8115 15.7542 28.9518 15.5171 28.9891 15.2545C29.0265 14.9918 28.958 14.725 28.7987 14.5129C27.9136 13.3262 26.7332 12.3919 25.375 11.8029C26.1196 11.123 26.6413 10.234 26.8716 9.25237C27.1019 8.27075 27.03 7.24244 26.6654 6.30239C26.3009 5.36235 25.6606 4.55449 24.8287 3.98483C23.9967 3.41518 23.012 3.11035 22.0037 3.11035C20.9955 3.11035 20.0108 3.41518 19.1788 3.98483C18.3469 4.55449 17.7066 5.36235 17.3421 6.30239C16.9775 7.24244 16.9056 8.27075 17.1359 9.25237C17.3662 10.234 17.8879 11.123 18.6325 11.8029C17.6518 12.2269 16.7609 12.834 16.0075 13.5916C15.2541 12.834 14.3632 12.2269 13.3825 11.8029C14.1271 11.123 14.6488 10.234 14.8791 9.25237C15.1094 8.27075 15.0375 7.24244 14.6729 6.30239C14.3084 5.36235 13.6681 4.55449 12.8362 3.98483C12.0042 3.41518 11.0195 3.11035 10.0112 3.11035C9.00298 3.11035 8.01827 3.41518 7.18634 3.98483C6.35442 4.55449 5.71415 5.36235 5.34957 6.30239C4.98499 7.24244 4.91313 8.27075 5.14342 9.25237C5.37371 10.234 5.89539 11.123 6.64 11.8029C5.27581 12.39 4.08963 13.3249 3.2 14.5141C3.12121 14.6192 3.06388 14.7387 3.03129 14.8659C2.99869 14.9931 2.99148 15.1255 3.01005 15.2555C3.02862 15.3855 3.07262 15.5106 3.13953 15.6236C3.20643 15.7366 3.29494 15.8353 3.4 15.9141ZM22 5.11412C22.5933 5.11412 23.1734 5.29006 23.6667 5.61971C24.1601 5.94935 24.5446 6.41789 24.7716 6.96607C24.9987 7.51425 25.0581 8.11745 24.9424 8.69939C24.8266 9.28133 24.5409 9.81588 24.1213 10.2354C23.7018 10.655 23.1672 10.9407 22.5853 11.0565C22.0033 11.1722 21.4001 11.1128 20.8519 10.8858C20.3038 10.6587 19.8352 10.2742 19.5056 9.78083C19.1759 9.28748 19 8.70746 19 8.11412C19 7.31847 19.3161 6.55541 19.8787 5.9928C20.4413 5.43019 21.2044 5.11412 22 5.11412ZM10 5.11412C10.5933 5.11412 11.1734 5.29006 11.6667 5.61971C12.1601 5.94935 12.5446 6.41789 12.7716 6.96607C12.9987 7.51425 13.0581 8.11745 12.9424 8.69939C12.8266 9.28133 12.5409 9.81588 12.1213 10.2354C11.7018 10.655 11.1672 10.9407 10.5853 11.0565C10.0033 11.1722 9.40013 11.1128 8.85195 10.8858C8.30377 10.6587 7.83524 10.2742 7.50559 9.78083C7.17595 9.28748 7 8.70746 7 8.11412C7 7.31847 7.31607 6.55541 7.87868 5.9928C8.44129 5.43019 9.20435 5.11412 10 5.11412ZM25.375 24.8029C26.1196 24.123 26.6413 23.234 26.8716 22.2524C27.1019 21.2707 27.03 20.2424 26.6654 19.3024C26.3009 18.3623 25.6606 17.5545 24.8287 16.9848C23.9967 16.4152 23.012 16.1104 22.0037 16.1104C20.9955 16.1104 20.0108 16.4152 19.1788 16.9848C18.3469 17.5545 17.7066 18.3623 17.3421 19.3024C16.9775 20.2424 16.9056 21.2707 17.1359 22.2524C17.3662 23.234 17.8879 24.123 18.6325 24.8029C17.6518 25.2269 16.7609 25.834 16.0075 26.5916C15.2541 25.834 14.3632 25.2269 13.3825 24.8029C14.1271 24.123 14.6488 23.234 14.8791 22.2524C15.1094 21.2707 15.0375 20.2424 14.6729 19.3024C14.3084 18.3623 13.6681 17.5545 12.8362 16.9848C12.0042 16.4152 11.0195 16.1104 10.0112 16.1104C9.00298 16.1104 8.01827 16.4152 7.18634 16.9848C6.35442 17.5545 5.71415 18.3623 5.34957 19.3024C4.98499 20.2424 4.91313 21.2707 5.14342 22.2524C5.37371 23.234 5.89539 24.123 6.64 24.8029C5.27581 25.39 4.08963 26.3249 3.2 27.5141C3.12121 27.6192 3.06388 27.7387 3.03129 27.8659C2.99869 27.9931 2.99148 28.1255 3.01005 28.2555C3.02862 28.3855 3.07262 28.5106 3.13953 28.6236C3.20643 28.7366 3.29494 28.8353 3.4 28.9141C3.50506 28.9929 3.62461 29.0502 3.75182 29.0828C3.87903 29.1154 4.01142 29.1226 4.14142 29.1041C4.27142 29.0855 4.39649 29.0415 4.50949 28.9746C4.62249 28.9077 4.72121 28.8192 4.8 28.7141C5.40546 27.9068 6.19055 27.2516 7.09311 26.8003C7.99567 26.3491 8.99091 26.1141 10 26.1141C11.0091 26.1141 12.0043 26.3491 12.9069 26.8003C13.8094 27.2516 14.5945 27.9068 15.2 28.7141C15.3593 28.9261 15.5963 29.0661 15.8588 29.1034C16.1214 29.1407 16.388 29.0721 16.6 28.9129C16.6756 28.8567 16.7425 28.7897 16.7987 28.7141C17.4042 27.9068 18.1893 27.2516 19.0919 26.8003C19.9944 26.3491 20.9897 26.1141 21.9988 26.1141C23.0078 26.1141 24.0031 26.3491 24.9056 26.8003C25.8082 27.2516 26.5933 27.9068 27.1987 28.7141C27.358 28.9263 27.5951 29.0665 27.8578 29.1039C28.1204 29.1413 28.3872 29.0728 28.5994 28.9135C28.8115 28.7542 28.9518 28.5171 28.9891 28.2545C29.0265 27.9918 28.958 27.725 28.7987 27.5129C27.9136 26.3262 26.7332 25.3919 25.375 24.8029ZM10 18.1141C10.5933 18.1141 11.1734 18.2901 11.6667 18.6197C12.1601 18.9494 12.5446 19.4179 12.7716 19.9661C12.9987 20.5142 13.0581 21.1174 12.9424 21.6994C12.8266 22.2813 12.5409 22.8159 12.1213 23.2354C11.7018 23.655 11.1672 23.9407 10.5853 24.0565C10.0033 24.1722 9.40013 24.1128 8.85195 23.8858C8.30377 23.6587 7.83524 23.2742 7.50559 22.7808C7.17595 22.2875 7 21.7075 7 21.1141C7 20.3185 7.31607 19.5554 7.87868 18.9928C8.44129 18.4302 9.20435 18.1141 10 18.1141ZM22 18.1141C22.5933 18.1141 23.1734 18.2901 23.6667 18.6197C24.1601 18.9494 24.5446 19.4179 24.7716 19.9661C24.9987 20.5142 25.0581 21.1174 24.9424 21.6994C24.8266 22.2813 24.5409 22.8159 24.1213 23.2354C23.7018 23.655 23.1672 23.9407 22.5853 24.0565C22.0033 24.1722 21.4001 24.1128 20.8519 23.8858C20.3038 23.6587 19.8352 23.2742 19.5056 22.7808C19.1759 22.2875 19 21.7075 19 21.1141C19 20.3185 19.3161 19.5554 19.8787 18.9928C20.4413 18.4302 21.2044 18.1141 22 18.1141Z"
                                       fill="#8B3DFF" />
                                 </svg>
                              </div>
                           </div>
                           <div class="ms-4">
                              <h4>Community driven</h4>
                              <p class="mb-0">Block is an independent funded product driven by our community. Simple Bootstrap 5 theme.</p>
                           </div>
                        </div>
                     </div>
                  </div>
               </div>
            </div>
         </section>
         <!--More focus end-->

         <!--Join Community start-->
         <section class="container py-lg-8 py-5" data-cue="fadeIn">
            <div class="row justify-content-center mb-8">
               <div class="col-xl-6 col-lg-10 col-12">
                  <div class="text-center d-flex flex-column gap-4">
                     <div class="d-flex justify-content-center">
                        <span class="bg-primary bg-opacity-10 text-primary border-primary border px-3 py-2 fs-6 rounded-pill lh-1 align-items-center d-flex">
                           <svg xmlns="http://www.w3.org/2000/svg" width="16" height="17" viewBox="0 0 16 17" fill="none">
                              <path
                                 d="M15 14.6143C15 14.6143 16 14.6143 16 13.6143C16 12.6143 15 9.61426 11 9.61426C7 9.61426 6 12.6143 6 13.6143C6 14.6143 7 14.6143 7 14.6143H15ZM7.022 13.6143C7.01461 13.6132 7.00727 13.6119 7 13.6103C7.001 13.3463 7.167 12.5803 7.76 11.8903C8.312 11.2433 9.282 10.6143 11 10.6143C12.717 10.6143 13.687 11.2443 14.24 11.8903C14.833 12.5803 14.998 13.3473 15 13.6103L14.992 13.6123C14.9874 13.613 14.9827 13.6137 14.978 13.6143H7.022ZM11 7.61426C11.5304 7.61426 12.0391 7.40354 12.4142 7.02847C12.7893 6.6534 13 6.14469 13 5.61426C13 5.08382 12.7893 4.57512 12.4142 4.20004C12.0391 3.82497 11.5304 3.61426 11 3.61426C10.4696 3.61426 9.96086 3.82497 9.58579 4.20004C9.21071 4.57512 9 5.08382 9 5.61426C9 6.14469 9.21071 6.6534 9.58579 7.02847C9.96086 7.40354 10.4696 7.61426 11 7.61426ZM14 5.61426C14 6.00822 13.9224 6.39833 13.7716 6.76231C13.6209 7.12629 13.3999 7.457 13.1213 7.73558C12.8427 8.01415 12.512 8.23513 12.1481 8.3859C11.7841 8.53666 11.394 8.61426 11 8.61426C10.606 8.61426 10.2159 8.53666 9.85195 8.3859C9.48797 8.23513 9.15726 8.01415 8.87868 7.73558C8.6001 7.457 8.37913 7.12629 8.22836 6.76231C8.0776 6.39833 8 6.00822 8 5.61426C8 4.81861 8.31607 4.05555 8.87868 3.49294C9.44129 2.93033 10.2044 2.61426 11 2.61426C11.7956 2.61426 12.5587 2.93033 13.1213 3.49294C13.6839 4.05555 14 4.81861 14 5.61426ZM6.936 9.89426C6.53598 9.7683 6.12364 9.68549 5.706 9.64726C5.47133 9.62494 5.23573 9.61393 5 9.61426C1 9.61426 0 12.6143 0 13.6143C0 14.2813 0.333 14.6143 1 14.6143H5.216C5.06776 14.3021 4.99382 13.9598 5 13.6143C5 12.6043 5.377 11.5723 6.09 10.7103C6.333 10.4163 6.616 10.1413 6.936 9.89426ZM4.92 10.6143C4.32815 11.5035 4.00844 12.5461 4 13.6143H1C1 13.3543 1.164 12.5843 1.76 11.8903C2.305 11.2543 3.252 10.6343 4.92 10.6153V10.6143ZM1.5 6.11426C1.5 5.31861 1.81607 4.55555 2.37868 3.99294C2.94129 3.43033 3.70435 3.11426 4.5 3.11426C5.29565 3.11426 6.05871 3.43033 6.62132 3.99294C7.18393 4.55555 7.5 5.31861 7.5 6.11426C7.5 6.90991 7.18393 7.67297 6.62132 8.23558C6.05871 8.79819 5.29565 9.11426 4.5 9.11426C3.70435 9.11426 2.94129 8.79819 2.37868 8.23558C1.81607 7.67297 1.5 6.90991 1.5 6.11426ZM4.5 4.11426C3.96957 4.11426 3.46086 4.32497 3.08579 4.70004C2.71071 5.07512 2.5 5.58382 2.5 6.11426C2.5 6.64469 2.71071 7.1534 3.08579 7.52847C3.46086 7.90354 3.96957 8.11426 4.5 8.11426C5.03043 8.11426 5.53914 7.90354 5.91421 7.52847C6.28929 7.1534 6.5 6.64469 6.5 6.11426C6.5 5.58382 6.28929 5.07512 5.91421 4.70004C5.53914 4.32497 5.03043 4.11426 4.5 4.11426Z"
                                 fill="#8B3DFF" />
                           </svg>
                           <span class="ms-1 text-uppercase ls-md fw-semibold">Community</span>
                        </span>
                     </div>
                     <div class="d-flex flex-column gap-3 mx-lg-8">
                        <h1 class="mb-0">Join the community</h1>
                        <p class="mb-0">Don’t just take it from us. Here’s what some of our happy customers are saying.</p>
                     </div>
                  </div>
               </div>
            </div>
            <div class="row gy-4 gy-md-6">
               <div class="col-lg-4 col-md-6 col-12 flex-column d-flex gap-4 gap-md-6 mt-lg-8" data-cue="slideInLeft">
                  <div class="card bg-light card-lift">
                     <div class="card-body text-body">
                        <p class="mb-0">“liquam pretium justo dui, eget fringilla nisl aliquet id. Vestibulum posuere orci vitae ante suscipit, in auctor elit pulvinar.”</p>
                        <div class="mt-4 d-flex align-items-center">
                           <img src="./assets/images/avatar/avatar-1.jpg" alt="" class="avatar avatar-lg rounded-circle border p-1 bg-white" />
                           <div class="ms-3 lh-1">
                              <h5 class="mb-0">Daniel Leon</h5>
                              <small>Time Management Consultant</small>
                           </div>
                        </div>
                     </div>
                     <a href="#" class="stretched-link"></a>
                  </div>
                  <div class="card bg-light card-lift">
                     <div class="card-body text-body">
                        <p class="mb-0">“Quisque tristique volutpat urna in sagittis. Praesent elementum purus et enim vulputate vestibulum.”</p>
                        <div class="mt-4 d-flex align-items-center">
                           <img src="./assets/images/avatar/avatar-2.jpg" alt="" class="avatar avatar-lg rounded-circle border p-1 bg-white" />
                           <div class="ms-3 lh-1">
                              <h5 class="mb-0">Hector Pratt</h5>
                              <small>Fitness Trainer</small>
                           </div>
                        </div>
                     </div>
                     <a href="#" class="stretched-link"></a>
                  </div>
               </div>
               <div class="col-lg-4 col-md-6 col-12 flex-column d-flex gap-4 gap-md-6" data-cue="zoomIn">
                  <div class="card bg-light card-lift">
                     <div class="card-body text-body">
                        <p class="mb-0">“Morbi fermentum nisl dolor, vel tincidunt turpis consequat at. Morbi vitae sollicitudin quam. Praesent ullamcorper porta sagittis.</p>
                        <div class="mt-4 d-flex align-items-center">
                           <img src="./assets/images/avatar/avatar-4.jpg" alt="" class="avatar avatar-lg rounded-circle border p-1 bg-white" />
                           <div class="ms-3 lh-1">
                              <h5 class="mb-0">Adrian Lopez</h5>
                              <small>Travel Advisor</small>
                           </div>
                        </div>
                     </div>
                     <a href="#" class="stretched-link"></a>
                  </div>
                  <div class="card bg-light card-lift">
                     <div class="card-body text-body">
                        <p class="mb-0">“Nulla id mauris id nisi venenatis fringilla et eget est ullam malesuada dolor at est molestie mattis. Mauris finibus molestie est vitae aliquet.”</p>
                        <div class="mt-4 d-flex align-items-center">
                           <img src="./assets/images/avatar/avatar-3.jpg" alt="" class="avatar avatar-lg rounded-circle border p-1 bg-white" />
                           <div class="ms-3 lh-1">
                              <h5 class="mb-0">Erica Nixon</h5>
                              <small>Event Planner</small>
                           </div>
                        </div>
                     </div>
                     <a href="#" class="stretched-link"></a>
                  </div>
               </div>
               <div class="col-lg-4 col-md-12 col-12 flex-column flex-md-row flex-lg-column d-flex gap-4 gap-md-6 mt-lg-8" data-cue="slideInRight">
                  <div class="card bg-light card-lift">
                     <div class="card-body text-body">
                        <p class="mb-0">“Sed nunc felis, tempor in consectetur ut, consequat quis velit. Mauris at dictum est. Donec quam quam.”</p>
                        <div class="mt-4 d-flex align-items-center">
                           <img src="./assets/images/avatar/avatar-6.jpg" alt="" class="avatar avatar-lg rounded-circle border p-1 bg-white" />
                           <div class="ms-3 lh-1">
                              <h5 class="mb-0">Virginia Santiago</h5>
                              <small>Career Counselor</small>
                           </div>
                        </div>
                     </div>
                     <a href="#" class="stretched-link"></a>
                  </div>
                  <div class="card bg-light card-lift">
                     <div class="card-body text-body">
                        <p class="mb-0">“Phasellus luctus fermentum vehicula. Pellentesque imperdiet, est vitae lacinia consequat, lorem nulla pulvinar nulla, eget varius tellus velit quis metus.”</p>
                        <div class="mt-4 d-flex align-items-center">
                           <img src="./assets/images/avatar/avatar-5.jpg" alt="" class="avatar avatar-lg rounded-circle border p-1 bg-white" />
                           <div class="ms-3 lh-1">
                              <h5 class="mb-0">Amin Matthams</h5>
                              <small>Cybersecurity Expert</small>
                           </div>
                        </div>
                     </div>
                     <a href="#" class="stretched-link"></a>
                  </div>
               </div>
               <div class="col-12 text-center" data-cue="zoomIn">
                  <a href="#" class="btn btn-primary">Talk to sales</a>
                  <a href="#" class="btn btn-outline-primary ms-2">Sign up for free</a>
               </div>
            </div>
         </section>
         <!--Join Community end-->

         <!--Plan to unlock start-->
         <section class="container py-lg-8 py-5 price-wrapper" data-cue="fadeIn">
            <div class="row justify-content-center mb-8">
               <div class="col-xl-6 col-lg-10 col-12">
                  <div class="text-center d-flex flex-column gap-5">
                     <div class="d-flex justify-content-center">
                        <span class="bg-primary bg-opacity-10 text-primary border-primary border px-3 py-2 fs-6 rounded-pill lh-1 align-items-center d-flex">
                           <svg xmlns="http://www.w3.org/2000/svg" width="17" height="17" viewBox="0 0 17 17" fill="none">
                              <g clip-path="url(#clip0_1717_21198)">
                                 <path
                                    d="M6 10.1253C6.076 11.0793 6.83 11.8223 8.182 11.9103V12.6143H8.782V11.9053C10.182 11.8073 11 11.0593 11 9.97326C11 8.98626 10.374 8.47726 9.255 8.21326L8.782 8.10126V6.18426C9.382 6.25226 9.764 6.58026 9.856 7.03426H10.908C10.832 6.11526 10.044 5.39626 8.782 5.31826V4.61426H8.182V5.33326C6.987 5.45026 6.172 6.16926 6.172 7.18626C6.172 8.08626 6.778 8.65826 7.785 8.89326L8.182 8.99126V11.0253C7.567 10.9323 7.16 10.5953 7.068 10.1253H6ZM8.177 7.95926C7.587 7.82226 7.267 7.54326 7.267 7.12326C7.267 6.65326 7.612 6.30126 8.182 6.19826V7.95826L8.177 7.95926ZM8.869 9.15226C9.586 9.31826 9.917 9.58726 9.917 10.0623C9.917 10.6043 9.505 10.9763 8.782 11.0443V9.13226L8.869 9.15226Z"
                                    fill="#8B3DFF" />
                                 <path
                                    d="M8.5 15.6143C6.64348 15.6143 4.86301 14.8768 3.55025 13.564C2.2375 12.2513 1.5 10.4708 1.5 8.61426C1.5 6.75774 2.2375 4.97726 3.55025 3.66451C4.86301 2.35176 6.64348 1.61426 8.5 1.61426C10.3565 1.61426 12.137 2.35176 13.4497 3.66451C14.7625 4.97726 15.5 6.75774 15.5 8.61426C15.5 10.4708 14.7625 12.2513 13.4497 13.564C12.137 14.8768 10.3565 15.6143 8.5 15.6143ZM8.5 16.6143C10.6217 16.6143 12.6566 15.7714 14.1569 14.2711C15.6571 12.7708 16.5 10.736 16.5 8.61426C16.5 6.49253 15.6571 4.45769 14.1569 2.9574C12.6566 1.45711 10.6217 0.614258 8.5 0.614258C6.37827 0.614258 4.34344 1.45711 2.84315 2.9574C1.34285 4.45769 0.5 6.49253 0.5 8.61426C0.5 10.736 1.34285 12.7708 2.84315 14.2711C4.34344 15.7714 6.37827 16.6143 8.5 16.6143Z"
                                    fill="#8B3DFF" />
                                 <path
                                    d="M8.5 14.1143C7.04131 14.1143 5.64236 13.5348 4.61091 12.5033C3.57946 11.4719 3 10.0729 3 8.61426C3 7.15557 3.57946 5.75662 4.61091 4.72517C5.64236 3.69372 7.04131 3.11426 8.5 3.11426C9.95869 3.11426 11.3576 3.69372 12.3891 4.72517C13.4205 5.75662 14 7.15557 14 8.61426C14 10.0729 13.4205 11.4719 12.3891 12.5033C11.3576 13.5348 9.95869 14.1143 8.5 14.1143ZM8.5 14.6143C9.28793 14.6143 10.0681 14.4591 10.7961 14.1575C11.5241 13.856 12.1855 13.4141 12.7426 12.8569C13.2998 12.2997 13.7417 11.6383 14.0433 10.9104C14.3448 10.1824 14.5 9.40219 14.5 8.61426C14.5 7.82633 14.3448 7.04611 14.0433 6.31816C13.7417 5.5902 13.2998 4.92877 12.7426 4.37162C12.1855 3.81447 11.5241 3.37251 10.7961 3.07098C10.0681 2.76945 9.28793 2.61426 8.5 2.61426C6.9087 2.61426 5.38258 3.2464 4.25736 4.37162C3.13214 5.49684 2.5 7.02296 2.5 8.61426C2.5 10.2056 3.13214 11.7317 4.25736 12.8569C5.38258 13.9821 6.9087 14.6143 8.5 14.6143Z"
                                    fill="#8B3DFF" />
                              </g>
                              <defs>
                                 <clipPath id="clip0_1717_21198">
                                    <rect width="16" height="16" fill="white" transform="translate(0.5 0.614258)" />
                                 </clipPath>
                              </defs>
                           </svg>
                           <span class="ms-1 text-uppercase ls-md fw-semibold">Pricing</span>
                        </span>
                     </div>
                     <div class="d-flex flex-column gap-3 mx-lg-8">
                        <h1 class="mb-0">Plan to unlock a new level of productivity</h1>
                        <p class="mb-0">Fair and simple pricing making content editing and generation accessible to everyone.</p>
                     </div>
                     <div class="mb-5">
                        <div class="price-switcher-wrapper switcher">
                           <p class="mb-0 pe-3">Monthly</p>
                           <div class="price-switchers">
                              <div class="price-switcher price-switcher-active"></div>
                              <div class="price-switcher"></div>
                              <div class="switcher-button bg-primary"></div>
                           </div>
                           <p class="mb-0 ps-3">Yearly</p>
                        </div>
                     </div>
                  </div>
               </div>
            </div>
            <div class="row justify-content-center">
               <div class="col-lg-8 col-12">
                  <div class="row align-items-center g-md-0 gy-4">
                     <div class="col-md-6 col-12" data-cue="slideInLeft">
                        <div class="card bg-light pricing rounded-end-md-0">
                           <div class="card-body d-flex flex-column gap-4">
                              <div>
                                 <h3>Free</h3>
                                 <p class="mb-0">Everything you need to supercharge your productivity.</p>
                              </div>
                              <h2 class="mb-0 d-flex align-items-center">
                                 <span>$0</span>
                                 <span class="price-duration fs-6 text-body ms-2">/month</span>
                              </h2>
                              <hr class="my-0" />
                              <div>
                                 <h5 class="mb-3">What’s included</h5>
                                 <ul class="list-unstyled flex-column d-flex gap-2">
                                    <li class="d-flex align-items-start">
                                       <span>
                                          <svg xmlns="http://www.w3.org/2000/svg" width="17" height="17" viewBox="0 0 17 17" fill="none">
                                             <g clip-path="url(#clip0_1710_19550)">
                                                <path
                                                   d="M8.5 15.6143C6.64348 15.6143 4.86301 14.8768 3.55025 13.564C2.2375 12.2513 1.5 10.4708 1.5 8.61426C1.5 6.75774 2.2375 4.97726 3.55025 3.66451C4.86301 2.35176 6.64348 1.61426 8.5 1.61426C10.3565 1.61426 12.137 2.35176 13.4497 3.66451C14.7625 4.97726 15.5 6.75774 15.5 8.61426C15.5 10.4708 14.7625 12.2513 13.4497 13.564C12.137 14.8768 10.3565 15.6143 8.5 15.6143ZM8.5 16.6143C10.6217 16.6143 12.6566 15.7714 14.1569 14.2711C15.6571 12.7708 16.5 10.736 16.5 8.61426C16.5 6.49253 15.6571 4.45769 14.1569 2.9574C12.6566 1.45711 10.6217 0.614258 8.5 0.614258C6.37827 0.614258 4.34344 1.45711 2.84315 2.9574C1.34285 4.45769 0.5 6.49253 0.5 8.61426C0.5 10.736 1.34285 12.7708 2.84315 14.2711C4.34344 15.7714 6.37827 16.6143 8.5 16.6143Z"
                                                   fill="#64748B" />
                                                <path
                                                   d="M11.4699 5.58405C11.4627 5.59095 11.456 5.5983 11.4499 5.60605L7.97685 10.0311L5.88385 7.93705C5.74168 7.80457 5.55363 7.73245 5.35933 7.73588C5.16503 7.7393 4.97964 7.81802 4.84223 7.95543C4.70482 8.09284 4.6261 8.27823 4.62268 8.47253C4.61925 8.66683 4.69137 8.85488 4.82385 8.99705L7.46985 11.6441C7.54113 11.7152 7.62601 11.7713 7.71943 11.8089C7.81285 11.8465 7.9129 11.865 8.0136 11.8631C8.11429 11.8612 8.21359 11.8391 8.30555 11.798C8.39751 11.757 8.48025 11.6978 8.54885 11.6241L12.5409 6.63405C12.6768 6.49138 12.7511 6.30095 12.7477 6.10392C12.7444 5.90689 12.6636 5.7191 12.5229 5.58114C12.3822 5.44319 12.1928 5.36616 11.9958 5.3667C11.7987 5.36725 11.6098 5.44532 11.4699 5.58405Z"
                                                   fill="#64748B" />
                                             </g>
                                             <defs>
                                                <clipPath id="clip0_1710_19550">
                                                   <rect width="16" height="16" fill="white" transform="translate(0.5 0.614258)" />
                                                </clipPath>
                                             </defs>
                                          </svg>
                                       </span>
                                       <span class="ms-2">Calculator, Quicklinks, Snippets, Window Management, and many more core features</span>
                                    </li>
                                    <li class="d-flex align-items-start">
                                       <span>
                                          <svg xmlns="http://www.w3.org/2000/svg" width="17" height="17" viewBox="0 0 17 17" fill="none">
                                             <g clip-path="url(#clip0_1710_19550)">
                                                <path
                                                   d="M8.5 15.6143C6.64348 15.6143 4.86301 14.8768 3.55025 13.564C2.2375 12.2513 1.5 10.4708 1.5 8.61426C1.5 6.75774 2.2375 4.97726 3.55025 3.66451C4.86301 2.35176 6.64348 1.61426 8.5 1.61426C10.3565 1.61426 12.137 2.35176 13.4497 3.66451C14.7625 4.97726 15.5 6.75774 15.5 8.61426C15.5 10.4708 14.7625 12.2513 13.4497 13.564C12.137 14.8768 10.3565 15.6143 8.5 15.6143ZM8.5 16.6143C10.6217 16.6143 12.6566 15.7714 14.1569 14.2711C15.6571 12.7708 16.5 10.736 16.5 8.61426C16.5 6.49253 15.6571 4.45769 14.1569 2.9574C12.6566 1.45711 10.6217 0.614258 8.5 0.614258C6.37827 0.614258 4.34344 1.45711 2.84315 2.9574C1.34285 4.45769 0.5 6.49253 0.5 8.61426C0.5 10.736 1.34285 12.7708 2.84315 14.2711C4.34344 15.7714 6.37827 16.6143 8.5 16.6143Z"
                                                   fill="#64748B" />
                                                <path
                                                   d="M11.4699 5.58405C11.4627 5.59095 11.456 5.5983 11.4499 5.60605L7.97685 10.0311L5.88385 7.93705C5.74168 7.80457 5.55363 7.73245 5.35933 7.73588C5.16503 7.7393 4.97964 7.81802 4.84223 7.95543C4.70482 8.09284 4.6261 8.27823 4.62268 8.47253C4.61925 8.66683 4.69137 8.85488 4.82385 8.99705L7.46985 11.6441C7.54113 11.7152 7.62601 11.7713 7.71943 11.8089C7.81285 11.8465 7.9129 11.865 8.0136 11.8631C8.11429 11.8612 8.21359 11.8391 8.30555 11.798C8.39751 11.757 8.48025 11.6978 8.54885 11.6241L12.5409 6.63405C12.6768 6.49138 12.7511 6.30095 12.7477 6.10392C12.7444 5.90689 12.6636 5.7191 12.5229 5.58114C12.3822 5.44319 12.1928 5.36616 11.9958 5.3667C11.7987 5.36725 11.6098 5.44532 11.4699 5.58405Z"
                                                   fill="#64748B" />
                                             </g>
                                             <defs>
                                                <clipPath id="clip0_1710_19550">
                                                   <rect width="16" height="16" fill="white" transform="translate(0.5 0.614258)" />
                                                </clipPath>
                                             </defs>
                                          </svg>
                                       </span>
                                       <span class="ms-2">More than 1000 Extensions</span>
                                    </li>
                                    <li class="d-flex align-items-start">
                                       <span>
                                          <svg xmlns="http://www.w3.org/2000/svg" width="17" height="17" viewBox="0 0 17 17" fill="none">
                                             <g clip-path="url(#clip0_1710_19550)">
                                                <path
                                                   d="M8.5 15.6143C6.64348 15.6143 4.86301 14.8768 3.55025 13.564C2.2375 12.2513 1.5 10.4708 1.5 8.61426C1.5 6.75774 2.2375 4.97726 3.55025 3.66451C4.86301 2.35176 6.64348 1.61426 8.5 1.61426C10.3565 1.61426 12.137 2.35176 13.4497 3.66451C14.7625 4.97726 15.5 6.75774 15.5 8.61426C15.5 10.4708 14.7625 12.2513 13.4497 13.564C12.137 14.8768 10.3565 15.6143 8.5 15.6143ZM8.5 16.6143C10.6217 16.6143 12.6566 15.7714 14.1569 14.2711C15.6571 12.7708 16.5 10.736 16.5 8.61426C16.5 6.49253 15.6571 4.45769 14.1569 2.9574C12.6566 1.45711 10.6217 0.614258 8.5 0.614258C6.37827 0.614258 4.34344 1.45711 2.84315 2.9574C1.34285 4.45769 0.5 6.49253 0.5 8.61426C0.5 10.736 1.34285 12.7708 2.84315 14.2711C4.34344 15.7714 6.37827 16.6143 8.5 16.6143Z"
                                                   fill="#64748B" />
                                                <path
                                                   d="M11.4699 5.58405C11.4627 5.59095 11.456 5.5983 11.4499 5.60605L7.97685 10.0311L5.88385 7.93705C5.74168 7.80457 5.55363 7.73245 5.35933 7.73588C5.16503 7.7393 4.97964 7.81802 4.84223 7.95543C4.70482 8.09284 4.6261 8.27823 4.62268 8.47253C4.61925 8.66683 4.69137 8.85488 4.82385 8.99705L7.46985 11.6441C7.54113 11.7152 7.62601 11.7713 7.71943 11.8089C7.81285 11.8465 7.9129 11.865 8.0136 11.8631C8.11429 11.8612 8.21359 11.8391 8.30555 11.798C8.39751 11.757 8.48025 11.6978 8.54885 11.6241L12.5409 6.63405C12.6768 6.49138 12.7511 6.30095 12.7477 6.10392C12.7444 5.90689 12.6636 5.7191 12.5229 5.58114C12.3822 5.44319 12.1928 5.36616 11.9958 5.3667C11.7987 5.36725 11.6098 5.44532 11.4699 5.58405Z"
                                                   fill="#64748B" />
                                             </g>
                                             <defs>
                                                <clipPath id="clip0_1710_19550">
                                                   <rect width="16" height="16" fill="white" transform="translate(0.5 0.614258)" />
                                                </clipPath>
                                             </defs>
                                          </svg>
                                       </span>
                                       <span class="ms-2">Custom Extensions</span>
                                    </li>
                                    <li class="d-flex align-items-start">
                                       <span>
                                          <svg xmlns="http://www.w3.org/2000/svg" width="17" height="17" viewBox="0 0 17 17" fill="none">
                                             <g clip-path="url(#clip0_1710_19550)">
                                                <path
                                                   d="M8.5 15.6143C6.64348 15.6143 4.86301 14.8768 3.55025 13.564C2.2375 12.2513 1.5 10.4708 1.5 8.61426C1.5 6.75774 2.2375 4.97726 3.55025 3.66451C4.86301 2.35176 6.64348 1.61426 8.5 1.61426C10.3565 1.61426 12.137 2.35176 13.4497 3.66451C14.7625 4.97726 15.5 6.75774 15.5 8.61426C15.5 10.4708 14.7625 12.2513 13.4497 13.564C12.137 14.8768 10.3565 15.6143 8.5 15.6143ZM8.5 16.6143C10.6217 16.6143 12.6566 15.7714 14.1569 14.2711C15.6571 12.7708 16.5 10.736 16.5 8.61426C16.5 6.49253 15.6571 4.45769 14.1569 2.9574C12.6566 1.45711 10.6217 0.614258 8.5 0.614258C6.37827 0.614258 4.34344 1.45711 2.84315 2.9574C1.34285 4.45769 0.5 6.49253 0.5 8.61426C0.5 10.736 1.34285 12.7708 2.84315 14.2711C4.34344 15.7714 6.37827 16.6143 8.5 16.6143Z"
                                                   fill="#64748B" />
                                                <path
                                                   d="M11.4699 5.58405C11.4627 5.59095 11.456 5.5983 11.4499 5.60605L7.97685 10.0311L5.88385 7.93705C5.74168 7.80457 5.55363 7.73245 5.35933 7.73588C5.16503 7.7393 4.97964 7.81802 4.84223 7.95543C4.70482 8.09284 4.6261 8.27823 4.62268 8.47253C4.61925 8.66683 4.69137 8.85488 4.82385 8.99705L7.46985 11.6441C7.54113 11.7152 7.62601 11.7713 7.71943 11.8089C7.81285 11.8465 7.9129 11.865 8.0136 11.8631C8.11429 11.8612 8.21359 11.8391 8.30555 11.798C8.39751 11.757 8.48025 11.6978 8.54885 11.6241L12.5409 6.63405C12.6768 6.49138 12.7511 6.30095 12.7477 6.10392C12.7444 5.90689 12.6636 5.7191 12.5229 5.58114C12.3822 5.44319 12.1928 5.36616 11.9958 5.3667C11.7987 5.36725 11.6098 5.44532 11.4699 5.58405Z"
                                                   fill="#64748B" />
                                             </g>
                                             <defs>
                                                <clipPath id="clip0_1710_19550">
                                                   <rect width="16" height="16" fill="white" transform="translate(0.5 0.614258)" />
                                                </clipPath>
                                             </defs>
                                          </svg>
                                       </span>
                                       <span class="ms-2">Developer Tools</span>
                                    </li>
                                 </ul>
                              </div>
                              <div class="d-grid">
                                 <a href="#" class="btn btn-outline-secondary text-dark">Start with Free</a>
                              </div>
                           </div>
                        </div>
                     </div>
                     <div class="col-md-6 col-12" data-cue="slideInRight">
                        <div class="card bg-primary text-white pricing border-primary">
                           <div class="card-body d-flex flex-column gap-4 text-white-stable">
                              <div>
                                 <h3 class="text-white-stable">Pro</h3>
                                 <p class="mb-0 text-white-50">Unlock a new level of your personal productivity.</p>
                              </div>
                              <h2 class="d-flex align-items-center text-white-stable">
                                 <div class="price-text">
                                    <div class="price price-show d-flex align-items-center">
                                       <span>$</span>
                                       <span>5.99</span>
                                       <span class="price-duration fs-6 ms-3">/month</span>
                                    </div>
                                    <div class="price price-hide price-hidden d-flex align-items-center">
                                       <span>$</span>
                                       <span>19.99</span>
                                       <span class="price-duration fs-6 ms-3">/year</span>
                                    </div>
                                 </div>
                              </h2>
                              <div class="my-2">
                                 <div class="border-top position-relative z-1 d-flex justify-content-center border-white">
                                    <div class="lh-lg mt-n3 position-absolute top-0 z-2 bg-primary px-3">$96 billed annually</div>
                                 </div>
                              </div>
                              <div class="text-white-stable">
                                 <h5 class="mb-3 text-white-stable">What’s included</h5>
                                 <ul class="list-unstyled flex-column d-flex gap-2">
                                    <li class="d-flex align-items-start">
                                       <span>
                                          <svg xmlns="http://www.w3.org/2000/svg" width="17" height="17" viewBox="0 0 17 17" fill="none">
                                             <g clip-path="url(#clip0_1710_19550)">
                                                <path
                                                   d="M8.5 15.6143C6.64348 15.6143 4.86301 14.8768 3.55025 13.564C2.2375 12.2513 1.5 10.4708 1.5 8.61426C1.5 6.75774 2.2375 4.97726 3.55025 3.66451C4.86301 2.35176 6.64348 1.61426 8.5 1.61426C10.3565 1.61426 12.137 2.35176 13.4497 3.66451C14.7625 4.97726 15.5 6.75774 15.5 8.61426C15.5 10.4708 14.7625 12.2513 13.4497 13.564C12.137 14.8768 10.3565 15.6143 8.5 15.6143ZM8.5 16.6143C10.6217 16.6143 12.6566 15.7714 14.1569 14.2711C15.6571 12.7708 16.5 10.736 16.5 8.61426C16.5 6.49253 15.6571 4.45769 14.1569 2.9574C12.6566 1.45711 10.6217 0.614258 8.5 0.614258C6.37827 0.614258 4.34344 1.45711 2.84315 2.9574C1.34285 4.45769 0.5 6.49253 0.5 8.61426C0.5 10.736 1.34285 12.7708 2.84315 14.2711C4.34344 15.7714 6.37827 16.6143 8.5 16.6143Z"
                                                   fill="#ffffff" />
                                                <path
                                                   d="M11.4699 5.58405C11.4627 5.59095 11.456 5.5983 11.4499 5.60605L7.97685 10.0311L5.88385 7.93705C5.74168 7.80457 5.55363 7.73245 5.35933 7.73588C5.16503 7.7393 4.97964 7.81802 4.84223 7.95543C4.70482 8.09284 4.6261 8.27823 4.62268 8.47253C4.61925 8.66683 4.69137 8.85488 4.82385 8.99705L7.46985 11.6441C7.54113 11.7152 7.62601 11.7713 7.71943 11.8089C7.81285 11.8465 7.9129 11.865 8.0136 11.8631C8.11429 11.8612 8.21359 11.8391 8.30555 11.798C8.39751 11.757 8.48025 11.6978 8.54885 11.6241L12.5409 6.63405C12.6768 6.49138 12.7511 6.30095 12.7477 6.10392C12.7444 5.90689 12.6636 5.7191 12.5229 5.58114C12.3822 5.44319 12.1928 5.36616 11.9958 5.3667C11.7987 5.36725 11.6098 5.44532 11.4699 5.58405Z"
                                                   fill="#ffffff" />
                                             </g>
                                             <defs>
                                                <clipPath id="clip0_1710_19550">
                                                   <rect width="16" height="16" fill="white" transform="translate(0.5 0.614258)" />
                                                </clipPath>
                                             </defs>
                                          </svg>
                                       </span>
                                       <span class="ms-2">Everything in Free</span>
                                    </li>
                                    <li class="d-flex align-items-start">
                                       <span>
                                          <svg xmlns="http://www.w3.org/2000/svg" width="17" height="17" viewBox="0 0 17 17" fill="none">
                                             <g clip-path="url(#clip0_1710_19550)">
                                                <path
                                                   d="M8.5 15.6143C6.64348 15.6143 4.86301 14.8768 3.55025 13.564C2.2375 12.2513 1.5 10.4708 1.5 8.61426C1.5 6.75774 2.2375 4.97726 3.55025 3.66451C4.86301 2.35176 6.64348 1.61426 8.5 1.61426C10.3565 1.61426 12.137 2.35176 13.4497 3.66451C14.7625 4.97726 15.5 6.75774 15.5 8.61426C15.5 10.4708 14.7625 12.2513 13.4497 13.564C12.137 14.8768 10.3565 15.6143 8.5 15.6143ZM8.5 16.6143C10.6217 16.6143 12.6566 15.7714 14.1569 14.2711C15.6571 12.7708 16.5 10.736 16.5 8.61426C16.5 6.49253 15.6571 4.45769 14.1569 2.9574C12.6566 1.45711 10.6217 0.614258 8.5 0.614258C6.37827 0.614258 4.34344 1.45711 2.84315 2.9574C1.34285 4.45769 0.5 6.49253 0.5 8.61426C0.5 10.736 1.34285 12.7708 2.84315 14.2711C4.34344 15.7714 6.37827 16.6143 8.5 16.6143Z"
                                                   fill="#ffffff" />
                                                <path
                                                   d="M11.4699 5.58405C11.4627 5.59095 11.456 5.5983 11.4499 5.60605L7.97685 10.0311L5.88385 7.93705C5.74168 7.80457 5.55363 7.73245 5.35933 7.73588C5.16503 7.7393 4.97964 7.81802 4.84223 7.95543C4.70482 8.09284 4.6261 8.27823 4.62268 8.47253C4.61925 8.66683 4.69137 8.85488 4.82385 8.99705L7.46985 11.6441C7.54113 11.7152 7.62601 11.7713 7.71943 11.8089C7.81285 11.8465 7.9129 11.865 8.0136 11.8631C8.11429 11.8612 8.21359 11.8391 8.30555 11.798C8.39751 11.757 8.48025 11.6978 8.54885 11.6241L12.5409 6.63405C12.6768 6.49138 12.7511 6.30095 12.7477 6.10392C12.7444 5.90689 12.6636 5.7191 12.5229 5.58114C12.3822 5.44319 12.1928 5.36616 11.9958 5.3667C11.7987 5.36725 11.6098 5.44532 11.4699 5.58405Z"
                                                   fill="#ffffff" />
                                             </g>
                                             <defs>
                                                <clipPath id="clip0_1710_19550">
                                                   <rect width="16" height="16" fill="white" transform="translate(0.5 0.614258)" />
                                                </clipPath>
                                             </defs>
                                          </svg>
                                       </span>
                                       <span class="ms-2">Block AI Features</span>
                                    </li>
                                    <li class="d-flex align-items-start">
                                       <span>
                                          <svg xmlns="http://www.w3.org/2000/svg" width="17" height="17" viewBox="0 0 17 17" fill="none">
                                             <g clip-path="url(#clip0_1710_19550)">
                                                <path
                                                   d="M8.5 15.6143C6.64348 15.6143 4.86301 14.8768 3.55025 13.564C2.2375 12.2513 1.5 10.4708 1.5 8.61426C1.5 6.75774 2.2375 4.97726 3.55025 3.66451C4.86301 2.35176 6.64348 1.61426 8.5 1.61426C10.3565 1.61426 12.137 2.35176 13.4497 3.66451C14.7625 4.97726 15.5 6.75774 15.5 8.61426C15.5 10.4708 14.7625 12.2513 13.4497 13.564C12.137 14.8768 10.3565 15.6143 8.5 15.6143ZM8.5 16.6143C10.6217 16.6143 12.6566 15.7714 14.1569 14.2711C15.6571 12.7708 16.5 10.736 16.5 8.61426C16.5 6.49253 15.6571 4.45769 14.1569 2.9574C12.6566 1.45711 10.6217 0.614258 8.5 0.614258C6.37827 0.614258 4.34344 1.45711 2.84315 2.9574C1.34285 4.45769 0.5 6.49253 0.5 8.61426C0.5 10.736 1.34285 12.7708 2.84315 14.2711C4.34344 15.7714 6.37827 16.6143 8.5 16.6143Z"
                                                   fill="#ffffff" />
                                                <path
                                                   d="M11.4699 5.58405C11.4627 5.59095 11.456 5.5983 11.4499 5.60605L7.97685 10.0311L5.88385 7.93705C5.74168 7.80457 5.55363 7.73245 5.35933 7.73588C5.16503 7.7393 4.97964 7.81802 4.84223 7.95543C4.70482 8.09284 4.6261 8.27823 4.62268 8.47253C4.61925 8.66683 4.69137 8.85488 4.82385 8.99705L7.46985 11.6441C7.54113 11.7152 7.62601 11.7713 7.71943 11.8089C7.81285 11.8465 7.9129 11.865 8.0136 11.8631C8.11429 11.8612 8.21359 11.8391 8.30555 11.798C8.39751 11.757 8.48025 11.6978 8.54885 11.6241L12.5409 6.63405C12.6768 6.49138 12.7511 6.30095 12.7477 6.10392C12.7444 5.90689 12.6636 5.7191 12.5229 5.58114C12.3822 5.44319 12.1928 5.36616 11.9958 5.3667C11.7987 5.36725 11.6098 5.44532 11.4699 5.58405Z"
                                                   fill="#ffffff" />
                                             </g>
                                             <defs>
                                                <clipPath id="clip0_1710_19550">
                                                   <rect width="16" height="16" fill="white" transform="translate(0.5 0.614258)" />
                                                </clipPath>
                                             </defs>
                                          </svg>
                                       </span>
                                       <span class="ms-2">Cloud Sync</span>
                                    </li>
                                    <li class="d-flex align-items-start">
                                       <span>
                                          <svg xmlns="http://www.w3.org/2000/svg" width="17" height="17" viewBox="0 0 17 17" fill="none">
                                             <g clip-path="url(#clip0_1710_19550)">
                                                <path
                                                   d="M8.5 15.6143C6.64348 15.6143 4.86301 14.8768 3.55025 13.564C2.2375 12.2513 1.5 10.4708 1.5 8.61426C1.5 6.75774 2.2375 4.97726 3.55025 3.66451C4.86301 2.35176 6.64348 1.61426 8.5 1.61426C10.3565 1.61426 12.137 2.35176 13.4497 3.66451C14.7625 4.97726 15.5 6.75774 15.5 8.61426C15.5 10.4708 14.7625 12.2513 13.4497 13.564C12.137 14.8768 10.3565 15.6143 8.5 15.6143ZM8.5 16.6143C10.6217 16.6143 12.6566 15.7714 14.1569 14.2711C15.6571 12.7708 16.5 10.736 16.5 8.61426C16.5 6.49253 15.6571 4.45769 14.1569 2.9574C12.6566 1.45711 10.6217 0.614258 8.5 0.614258C6.37827 0.614258 4.34344 1.45711 2.84315 2.9574C1.34285 4.45769 0.5 6.49253 0.5 8.61426C0.5 10.736 1.34285 12.7708 2.84315 14.2711C4.34344 15.7714 6.37827 16.6143 8.5 16.6143Z"
                                                   fill="#ffffff" />
                                                <path
                                                   d="M11.4699 5.58405C11.4627 5.59095 11.456 5.5983 11.4499 5.60605L7.97685 10.0311L5.88385 7.93705C5.74168 7.80457 5.55363 7.73245 5.35933 7.73588C5.16503 7.7393 4.97964 7.81802 4.84223 7.95543C4.70482 8.09284 4.6261 8.27823 4.62268 8.47253C4.61925 8.66683 4.69137 8.85488 4.82385 8.99705L7.46985 11.6441C7.54113 11.7152 7.62601 11.7713 7.71943 11.8089C7.81285 11.8465 7.9129 11.865 8.0136 11.8631C8.11429 11.8612 8.21359 11.8391 8.30555 11.798C8.39751 11.757 8.48025 11.6978 8.54885 11.6241L12.5409 6.63405C12.6768 6.49138 12.7511 6.30095 12.7477 6.10392C12.7444 5.90689 12.6636 5.7191 12.5229 5.58114C12.3822 5.44319 12.1928 5.36616 11.9958 5.3667C11.7987 5.36725 11.6098 5.44532 11.4699 5.58405Z"
                                                   fill="#ffffff" />
                                             </g>
                                             <defs>
                                                <clipPath id="clip0_1710_19550">
                                                   <rect width="16" height="16" fill="white" transform="translate(0.5 0.614258)" />
                                                </clipPath>
                                             </defs>
                                          </svg>
                                       </span>
                                       <span class="ms-2">Developer Tools</span>
                                    </li>
                                    <li class="d-flex align-items-start">
                                       <span>
                                          <svg xmlns="http://www.w3.org/2000/svg" width="17" height="17" viewBox="0 0 17 17" fill="none">
                                             <g clip-path="url(#clip0_1710_19550)">
                                                <path
                                                   d="M8.5 15.6143C6.64348 15.6143 4.86301 14.8768 3.55025 13.564C2.2375 12.2513 1.5 10.4708 1.5 8.61426C1.5 6.75774 2.2375 4.97726 3.55025 3.66451C4.86301 2.35176 6.64348 1.61426 8.5 1.61426C10.3565 1.61426 12.137 2.35176 13.4497 3.66451C14.7625 4.97726 15.5 6.75774 15.5 8.61426C15.5 10.4708 14.7625 12.2513 13.4497 13.564C12.137 14.8768 10.3565 15.6143 8.5 15.6143ZM8.5 16.6143C10.6217 16.6143 12.6566 15.7714 14.1569 14.2711C15.6571 12.7708 16.5 10.736 16.5 8.61426C16.5 6.49253 15.6571 4.45769 14.1569 2.9574C12.6566 1.45711 10.6217 0.614258 8.5 0.614258C6.37827 0.614258 4.34344 1.45711 2.84315 2.9574C1.34285 4.45769 0.5 6.49253 0.5 8.61426C0.5 10.736 1.34285 12.7708 2.84315 14.2711C4.34344 15.7714 6.37827 16.6143 8.5 16.6143Z"
                                                   fill="#ffffff" />
                                                <path
                                                   d="M11.4699 5.58405C11.4627 5.59095 11.456 5.5983 11.4499 5.60605L7.97685 10.0311L5.88385 7.93705C5.74168 7.80457 5.55363 7.73245 5.35933 7.73588C5.16503 7.7393 4.97964 7.81802 4.84223 7.95543C4.70482 8.09284 4.6261 8.27823 4.62268 8.47253C4.61925 8.66683 4.69137 8.85488 4.82385 8.99705L7.46985 11.6441C7.54113 11.7152 7.62601 11.7713 7.71943 11.8089C7.81285 11.8465 7.9129 11.865 8.0136 11.8631C8.11429 11.8612 8.21359 11.8391 8.30555 11.798C8.39751 11.757 8.48025 11.6978 8.54885 11.6241L12.5409 6.63405C12.6768 6.49138 12.7511 6.30095 12.7477 6.10392C12.7444 5.90689 12.6636 5.7191 12.5229 5.58114C12.3822 5.44319 12.1928 5.36616 11.9958 5.3667C11.7987 5.36725 11.6098 5.44532 11.4699 5.58405Z"
                                                   fill="#ffffff" />
                                             </g>
                                             <defs>
                                                <clipPath id="clip0_1710_19550">
                                                   <rect width="16" height="16" fill="white" transform="translate(0.5 0.614258)" />
                                                </clipPath>
                                             </defs>
                                          </svg>
                                       </span>
                                       <span class="ms-2">Custom Themes</span>
                                    </li>
                                    <li class="d-flex align-items-start">
                                       <span>
                                          <svg xmlns="http://www.w3.org/2000/svg" width="17" height="17" viewBox="0 0 17 17" fill="none">
                                             <g clip-path="url(#clip0_1710_19550)">
                                                <path
                                                   d="M8.5 15.6143C6.64348 15.6143 4.86301 14.8768 3.55025 13.564C2.2375 12.2513 1.5 10.4708 1.5 8.61426C1.5 6.75774 2.2375 4.97726 3.55025 3.66451C4.86301 2.35176 6.64348 1.61426 8.5 1.61426C10.3565 1.61426 12.137 2.35176 13.4497 3.66451C14.7625 4.97726 15.5 6.75774 15.5 8.61426C15.5 10.4708 14.7625 12.2513 13.4497 13.564C12.137 14.8768 10.3565 15.6143 8.5 15.6143ZM8.5 16.6143C10.6217 16.6143 12.6566 15.7714 14.1569 14.2711C15.6571 12.7708 16.5 10.736 16.5 8.61426C16.5 6.49253 15.6571 4.45769 14.1569 2.9574C12.6566 1.45711 10.6217 0.614258 8.5 0.614258C6.37827 0.614258 4.34344 1.45711 2.84315 2.9574C1.34285 4.45769 0.5 6.49253 0.5 8.61426C0.5 10.736 1.34285 12.7708 2.84315 14.2711C4.34344 15.7714 6.37827 16.6143 8.5 16.6143Z"
                                                   fill="#ffffff" />
                                                <path
                                                   d="M11.4699 5.58405C11.4627 5.59095 11.456 5.5983 11.4499 5.60605L7.97685 10.0311L5.88385 7.93705C5.74168 7.80457 5.55363 7.73245 5.35933 7.73588C5.16503 7.7393 4.97964 7.81802 4.84223 7.95543C4.70482 8.09284 4.6261 8.27823 4.62268 8.47253C4.61925 8.66683 4.69137 8.85488 4.82385 8.99705L7.46985 11.6441C7.54113 11.7152 7.62601 11.7713 7.71943 11.8089C7.81285 11.8465 7.9129 11.865 8.0136 11.8631C8.11429 11.8612 8.21359 11.8391 8.30555 11.798C8.39751 11.757 8.48025 11.6978 8.54885 11.6241L12.5409 6.63405C12.6768 6.49138 12.7511 6.30095 12.7477 6.10392C12.7444 5.90689 12.6636 5.7191 12.5229 5.58114C12.3822 5.44319 12.1928 5.36616 11.9958 5.3667C11.7987 5.36725 11.6098 5.44532 11.4699 5.58405Z"
                                                   fill="#ffffff" />
                                             </g>
                                             <defs>
                                                <clipPath id="clip0_1710_19550">
                                                   <rect width="16" height="16" fill="white" transform="translate(0.5 0.614258)" />
                                                </clipPath>
                                             </defs>
                                          </svg>
                                       </span>
                                       <span class="ms-2">Unlimited Clipboard History</span>
                                    </li>
                                    <li class="d-flex align-items-start">
                                       <span>
                                          <svg xmlns="http://www.w3.org/2000/svg" width="17" height="17" viewBox="0 0 17 17" fill="none">
                                             <g clip-path="url(#clip0_1710_19550)">
                                                <path
                                                   d="M8.5 15.6143C6.64348 15.6143 4.86301 14.8768 3.55025 13.564C2.2375 12.2513 1.5 10.4708 1.5 8.61426C1.5 6.75774 2.2375 4.97726 3.55025 3.66451C4.86301 2.35176 6.64348 1.61426 8.5 1.61426C10.3565 1.61426 12.137 2.35176 13.4497 3.66451C14.7625 4.97726 15.5 6.75774 15.5 8.61426C15.5 10.4708 14.7625 12.2513 13.4497 13.564C12.137 14.8768 10.3565 15.6143 8.5 15.6143ZM8.5 16.6143C10.6217 16.6143 12.6566 15.7714 14.1569 14.2711C15.6571 12.7708 16.5 10.736 16.5 8.61426C16.5 6.49253 15.6571 4.45769 14.1569 2.9574C12.6566 1.45711 10.6217 0.614258 8.5 0.614258C6.37827 0.614258 4.34344 1.45711 2.84315 2.9574C1.34285 4.45769 0.5 6.49253 0.5 8.61426C0.5 10.736 1.34285 12.7708 2.84315 14.2711C4.34344 15.7714 6.37827 16.6143 8.5 16.6143Z"
                                                   fill="#ffffff" />
                                                <path
                                                   d="M11.4699 5.58405C11.4627 5.59095 11.456 5.5983 11.4499 5.60605L7.97685 10.0311L5.88385 7.93705C5.74168 7.80457 5.55363 7.73245 5.35933 7.73588C5.16503 7.7393 4.97964 7.81802 4.84223 7.95543C4.70482 8.09284 4.6261 8.27823 4.62268 8.47253C4.61925 8.66683 4.69137 8.85488 4.82385 8.99705L7.46985 11.6441C7.54113 11.7152 7.62601 11.7713 7.71943 11.8089C7.81285 11.8465 7.9129 11.865 8.0136 11.8631C8.11429 11.8612 8.21359 11.8391 8.30555 11.798C8.39751 11.757 8.48025 11.6978 8.54885 11.6241L12.5409 6.63405C12.6768 6.49138 12.7511 6.30095 12.7477 6.10392C12.7444 5.90689 12.6636 5.7191 12.5229 5.58114C12.3822 5.44319 12.1928 5.36616 11.9958 5.3667C11.7987 5.36725 11.6098 5.44532 11.4699 5.58405Z"
                                                   fill="#ffffff" />
                                             </g>
                                             <defs>
                                                <clipPath id="clip0_1710_19550">
                                                   <rect width="16" height="16" fill="white" transform="translate(0.5 0.614258)" />
                                                </clipPath>
                                             </defs>
                                          </svg>
                                       </span>
                                       <span class="ms-2">More Coming Soon</span>
                                    </li>
                                 </ul>
                              </div>
                              <div class="d-grid">
                                 <a href="#" class="btn btn-dark">Sleact Plan</a>
                              </div>
                           </div>
                        </div>
                     </div>
                  </div>
               </div>
            </div>
         </section>
         <!--Plan to unlock end-->

         <!--Call to action start-->
         <section class="container mb-lg-8 py-lg-8 py-5" data-cue="zoomIn">
            <div class="row g-0">
               <div class="col-12">
                  <div class="bg-gray-900 rounded-3">
                     <div class="row align-items-center">
                        <div class="offset-xl-1 col-xl-4 col-lg-12">
                           <div class="d-flex flex-column gap-4 p-6 p-xl-0">
                              <div>
                                 <h2 class="text-white-stable">Try our powerful work management tools</h2>
                                 <p class="mb-0 me-lg-8">Sign up for a free two-week trial of Block today — no credit card required.</p>
                              </div>
                              <div>
                                 <a href="#" class="btn btn-primary">Get Started for free</a>
                              </div>
                              <div>
                                 <ul class="list-inline">
                                    <li class="list-inline-item">
                                       <span>
                                          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="17" viewBox="0 0 16 17" fill="none">
                                             <g clip-path="url(#clip0_1710_19624)">
                                                <path
                                                   d="M16 8.61426C16 10.736 15.1571 12.7708 13.6569 14.2711C12.1566 15.7714 10.1217 16.6143 8 16.6143C5.87827 16.6143 3.84344 15.7714 2.34315 14.2711C0.842855 12.7708 0 10.736 0 8.61426C0 6.49253 0.842855 4.45769 2.34315 2.9574C3.84344 1.45711 5.87827 0.614258 8 0.614258C10.1217 0.614258 12.1566 1.45711 13.6569 2.9574C15.1571 4.45769 16 6.49253 16 8.61426ZM12.03 5.58426C11.9586 5.51308 11.8735 5.45703 11.7799 5.41947C11.6863 5.38192 11.5861 5.36362 11.4853 5.36567C11.3845 5.36772 11.2851 5.39009 11.1932 5.43143C11.1012 5.47276 11.0185 5.53223 10.95 5.60626L7.477 10.0313L5.384 7.93726C5.24183 7.80478 5.05378 7.73266 4.85948 7.73608C4.66518 7.73951 4.47979 7.81822 4.34238 7.95564C4.20497 8.09305 4.12625 8.27843 4.12283 8.47274C4.1194 8.66704 4.19152 8.85508 4.324 8.99726L6.97 11.6443C7.04128 11.7154 7.12616 11.7715 7.21958 11.8091C7.313 11.8467 7.41305 11.8652 7.51375 11.8633C7.61444 11.8614 7.71374 11.8393 7.8057 11.7982C7.89766 11.7572 7.9804 11.698 8.049 11.6243L12.041 6.63426C12.1771 6.49276 12.2523 6.30353 12.2504 6.10722C12.2485 5.9109 12.1698 5.72314 12.031 5.58426H12.03Z"
                                                   fill="#F1F5F9" />
                                             </g>
                                             <defs>
                                                <clipPath id="clip0_1710_19624">
                                                   <rect width="16" height="16" fill="white" transform="translate(0 0.614258)" />
                                                </clipPath>
                                             </defs>
                                          </svg>
                                       </span>
                                       <span class="ms-1">No credit card required</span>
                                    </li>
                                    <li class="list-inline-item">
                                       <span>
                                          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="17" viewBox="0 0 16 17" fill="none">
                                             <g clip-path="url(#clip0_1710_19624)">
                                                <path
                                                   d="M16 8.61426C16 10.736 15.1571 12.7708 13.6569 14.2711C12.1566 15.7714 10.1217 16.6143 8 16.6143C5.87827 16.6143 3.84344 15.7714 2.34315 14.2711C0.842855 12.7708 0 10.736 0 8.61426C0 6.49253 0.842855 4.45769 2.34315 2.9574C3.84344 1.45711 5.87827 0.614258 8 0.614258C10.1217 0.614258 12.1566 1.45711 13.6569 2.9574C15.1571 4.45769 16 6.49253 16 8.61426ZM12.03 5.58426C11.9586 5.51308 11.8735 5.45703 11.7799 5.41947C11.6863 5.38192 11.5861 5.36362 11.4853 5.36567C11.3845 5.36772 11.2851 5.39009 11.1932 5.43143C11.1012 5.47276 11.0185 5.53223 10.95 5.60626L7.477 10.0313L5.384 7.93726C5.24183 7.80478 5.05378 7.73266 4.85948 7.73608C4.66518 7.73951 4.47979 7.81822 4.34238 7.95564C4.20497 8.09305 4.12625 8.27843 4.12283 8.47274C4.1194 8.66704 4.19152 8.85508 4.324 8.99726L6.97 11.6443C7.04128 11.7154 7.12616 11.7715 7.21958 11.8091C7.313 11.8467 7.41305 11.8652 7.51375 11.8633C7.61444 11.8614 7.71374 11.8393 7.8057 11.7982C7.89766 11.7572 7.9804 11.698 8.049 11.6243L12.041 6.63426C12.1771 6.49276 12.2523 6.30353 12.2504 6.10722C12.2485 5.9109 12.1698 5.72314 12.031 5.58426H12.03Z"
                                                   fill="#F1F5F9" />
                                             </g>
                                             <defs>
                                                <clipPath id="clip0_1710_19624">
                                                   <rect width="16" height="16" fill="white" transform="translate(0 0.614258)" />
                                                </clipPath>
                                             </defs>
                                          </svg>
                                       </span>
                                       <span class="ms-1">1 month free trial</span>
                                    </li>
                                 </ul>
                              </div>
                           </div>
                        </div>
                        <div class="offset-xl-1 col-xl-5 col-lg-12">
                           <div class="pt-xl-8 d-none d-xl-block">
                              <img src="./assets/images/landings/sass-v3/cta-img.svg" class="img-fluid w-xxl-100" />
                           </div>
                        </div>
                     </div>
                  </div>
               </div>
            </div>
         </section>
         <!--Call to action end-->
      </main>
      <footer class="pt-7">
   <div class="container">
      <!-- Footer 4 column -->
      <div class="row">
         <div class="col-lg-9 col-12">
            <div class="row" id="ft-links">
               <div class="col-lg-3 col-12">
                  <div class="position-relative">
                     <div class="mb-3 pb-2 d-flex justify-content-between border-bottom border-bottom-lg-0">
                        <h4>Service</h4>
                        <a class="d-block d-lg-none stretched-link text-body" data-bs-toggle="collapse" href="#collapseLanding" role="button" aria-expanded="true" aria-controls="collapseLanding">
                           <i class="bi bi-chevron-down"></i>
                        </a>
                     </div>
                     <div class="d-lg-block collapse show" id="collapseLanding" data-bs-parent="#ft-links" style="">
                        <ul class="list-unstyled mb-0 py-3 py-lg-0">
                           <li class="mb-2">
                              <a href="./index.html" class="text-decoration-none text-reset">Web App Development</a>
                           </li>
                           <li class="mb-2">
                              <a href="#!" class="text-decoration-none text-reset">Front End Development</a>
                           </li>
                           <li class="mb-2">
                              <a href="#!" class="text-decoration-none text-reset">MVP Development</a>
                           </li>
                           <li class="mb-2">
                              <a href="#!" class="text-decoration-none text-reset">Digital Marketing</a>
                           </li>
                           <li class="mb-2">
                              <a href="#!" class="text-decoration-none text-reset">Content Writing</a>
                           </li>
                        </ul>
                     </div>
                  </div>
               </div>
               <div class="col-lg-3 col-12">
                  <div>
                     <div class="mb-3 pb-2 d-flex justify-content-between border-bottom border-bottom-lg-0 position-relative">
                        <h4>About us</h4>
                        <a
                           class="d-block d-lg-none stretched-link text-body collapsed"
                           data-bs-toggle="collapse"
                           href="#collapseAccounts"
                           role="button"
                           aria-expanded="false"
                           aria-controls="collapseAccounts">
                           <i class="bi bi-chevron-down"></i>
                        </a>
                     </div>
                     <div class="collapse d-lg-block" id="collapseAccounts" data-bs-parent="#ft-links">
                        <ul class="list-unstyled mb-0 py-3 py-lg-0">
                           <li class="mb-2">
                              <a href="#!" class="text-decoration-none text-reset">Case Studies</a>
                           </li>
                           <li class="mb-2">
                              <a href="#!" class="text-decoration-none text-reset">Blog</a>
                           </li>
                           <li class="mb-2">
                              <a href="#!" class="text-decoration-none text-reset">Services</a>
                           </li>
                           <li class="mb-2">
                              <a href="#!" class="text-decoration-none text-reset">About</a>
                           </li>
                           <li class="mb-2">
                              <a href="#!" class="text-decoration-none text-reset">Career</a>
                           </li>
                        </ul>
                     </div>
                  </div>
               </div>
               <div class="col-lg-3 col-12">
                  <div class="mb-3 pb-2 d-flex justify-content-between border-bottom border-bottom-lg-0 position-relative">
                     <h4>Technology</h4>
                     <a
                        class="d-block d-lg-none stretched-link text-body collapsed"
                        data-bs-toggle="collapse"
                        href="#collapseResources"
                        role="button"
                        aria-expanded="false"
                        aria-controls="collapseResources">
                        <i class="bi bi-chevron-down"></i>
                     </a>
                  </div>
                  <div class="collapse d-lg-block" id="collapseResources" data-bs-parent="#ft-links">
                     <ul class="list-unstyled mb-0 py-3 py-lg-0">
                        <li class="mb-2">
                           <a href="./docs/index.html" class="text-decoration-none text-reset">Next.js</a>
                        </li>
                        <li class="mb-2">
                           <a href="#!" class="text-decoration-none text-reset">Sanity</a>
                        </li>
                        <li class="mb-2">
                           <a href="./changelog.html" class="text-decoration-none text-reset">Content ful</a>
                        </li>
                        <li class="mb-2">
                           <a href="#!" class="text-decoration-none text-reset">Vercel</a>
                        </li>
                        <li class="mb-2">
                           <a href="#!" class="text-decoration-none text-reset">Netlify</a>
                        </li>
                     </ul>
                  </div>
               </div>
               <div class="col-lg-3 col-12">
                  <div class="mb-3 pb-2 d-flex justify-content-between border-bottom border-bottom-lg-0 position-relative">
                     <h4>Locations</h4>
                     <a
                        class="d-block d-lg-none stretched-link text-body collapsed"
                        data-bs-toggle="collapse"
                        href="#collapseLocations"
                        role="button"
                        aria-expanded="false"
                        aria-controls="collapseLocations">
                        <i class="bi bi-chevron-down"></i>
                     </a>
                  </div>
                  <div class="collapse d-lg-block" id="collapseLocations" data-bs-parent="#ft-links">
                     <ul class="list-unstyled mb-0 py-3 py-lg-0">
                        <li class="mb-2">
                           <a href="./docs/index.html" class="text-decoration-none text-reset">India</a>
                        </li>
                        <li class="mb-2">
                           <a href="#!" class="text-decoration-none text-reset">Australia</a>
                        </li>
                        <li class="mb-2">
                           <a href="./changelog.html" class="text-decoration-none text-reset">Brazil</a>
                        </li>
                        <li class="mb-2">
                           <a href="#!" class="text-decoration-none text-reset">Canada</a>
                        </li>
                     </ul>
                  </div>
               </div>
            </div>
         </div>
         <div class="col-lg-3 col-12">
            <div class="me-7">
               <h4 class="mb-4">Headquarters</h4>
               <p class="text-body-secondary">Codescandy, 412, Residency Rd, Shanthala Nagar, Ashok Nagar, Bengaluru, Karnataka, India 560025</p>
            </div>
         </div>
      </div>
   </div>
   <div class="container mt-7 pt-lg-7 pb-4">
      <div class="row align-items-center">
         <div class="col-md-3">
            <a class="mb-4 mb-lg-0 d-block text-inverse" href="../index.html"><img src="./assets/images/logo/logo.svg" alt="" /></a>
         </div>
         <div class="col-md-9 col-lg-6">
            <div class="small mb-3 mb-lg-0 text-lg-center">
               Copyright © 2024

               <span class="text-primary"><a href="#">Block Bootstrap 5 Theme</a></span>
               | Designed by
               <span class="text-primary"><a href="#">CodesCandy</a></span>
            </div>
         </div>
         <div class="col-lg-3">
            <div class="text-lg-end d-flex align-items-center justify-content-lg-end">
               <div class="dropdown">
                  <button class="btn btn-light btn-icon rounded-circle d-flex align-items-center" type="button" aria-expanded="false" data-bs-toggle="dropdown" aria-label="Toggle theme (auto)">
                     <i class="bi theme-icon-active lh-1"><i class="bi theme-icon bi-sun-fill"></i></i>
                     <span class="visually-hidden bs-theme-text">Toggle theme</span>
                  </button>
                  <ul class="dropdown-menu dropdown-menu-end shadow" aria-labelledby="bs-theme-text">
                     <li>
                        <button type="button" class="dropdown-item d-flex align-items-center active" data-bs-theme-value="light" aria-pressed="true">
                           <i class="bi theme-icon bi-sun-fill"></i>
                           <span class="ms-2">Light</span>
                        </button>
                     </li>
                     <li>
                        <button type="button" class="dropdown-item d-flex align-items-center" data-bs-theme-value="dark" aria-pressed="false">
                           <i class="bi theme-icon bi-moon-stars-fill"></i>
                           <span class="ms-2">Dark</span>
                        </button>
                     </li>
                     <li>
                        <button type="button" class="dropdown-item d-flex align-items-center" data-bs-theme-value="auto" aria-pressed="false">
                           <i class="bi theme-icon bi-circle-half"></i>
                           <span class="ms-2">Auto</span>
                        </button>
                     </li>
                  </ul>
               </div>
               <div class="ms-3 d-flex gap-2">
                  <a href="#!" class="btn btn-instagram btn-light btn-icon">
                     <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-instagram" viewBox="0 0 16 16">
                        <path
                           d="M8 0C5.829 0 5.556.01 4.703.048 3.85.088 3.269.222 2.76.42a3.917 3.917 0 0 0-1.417.923A3.927 3.927 0 0 0 .42 2.76C.222 3.268.087 3.85.048 4.7.01 5.555 0 5.827 0 8.001c0 2.172.01 2.444.048 3.297.04.852.174 1.433.372 1.942.205.526.478.972.923 1.417.444.445.89.719 1.416.923.51.198 1.09.333 1.942.372C5.555 15.99 5.827 16 8 16s2.444-.01 3.298-.048c.851-.04 1.434-.174 1.943-.372a3.916 3.916 0 0 0 1.416-.923c.445-.445.718-.891.923-1.417.197-.509.332-1.09.372-1.942C15.99 10.445 16 10.173 16 8s-.01-2.445-.048-3.299c-.04-.851-.175-1.433-.372-1.941a3.926 3.926 0 0 0-.923-1.417A3.911 3.911 0 0 0 13.24.42c-.51-.198-1.092-.333-1.943-.372C10.443.01 10.172 0 7.998 0h.003zm-.717 1.442h.718c2.136 0 2.389.007 3.232.046.78.035 1.204.166 1.486.275.373.145.64.319.92.599.28.28.453.546.598.92.11.281.24.705.275 1.485.039.843.047 1.096.047 3.231s-.008 2.389-.047 3.232c-.035.78-.166 1.203-.275 1.485a2.47 2.47 0 0 1-.599.919c-.28.28-.546.453-.92.598-.28.11-.704.24-1.485.276-.843.038-1.096.047-3.232.047s-2.39-.009-3.233-.047c-.78-.036-1.203-.166-1.485-.276a2.478 2.478 0 0 1-.92-.598 2.48 2.48 0 0 1-.6-.92c-.109-.281-.24-.705-.275-1.485-.038-.843-.046-1.096-.046-3.233 0-2.136.008-2.388.046-3.231.036-.78.166-1.204.276-1.486.145-.373.319-.64.599-.92.28-.28.546-.453.92-.598.282-.11.705-.24 1.485-.276.738-.034 1.024-.044 2.515-.045v.002zm4.988 1.328a.96.96 0 1 0 0 1.92.96.96 0 0 0 0-1.92zm-4.27 1.122a4.109 4.109 0 1 0 0 8.217 4.109 4.109 0 0 0 0-8.217zm0 1.441a2.667 2.667 0 1 1 0 5.334 2.667 2.667 0 0 1 0-5.334z"></path>
                     </svg>
                  </a>
                  <a href="#!" class="btn btn-facebook btn-icon">
                     <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-facebook" viewBox="0 0 16 16">
                        <path
                           d="M16 8.049c0-4.446-3.582-8.05-8-8.05C3.58 0-.002 3.603-.002 8.05c0 4.017 2.926 7.347 6.75 7.951v-5.625h-2.03V8.05H6.75V6.275c0-2.017 1.195-3.131 3.022-3.131.876 0 1.791.157 1.791.157v1.98h-1.009c-.993 0-1.303.621-1.303 1.258v1.51h2.218l-.354 2.326H9.25V16c3.824-.604 6.75-3.934 6.75-7.951z"></path>
                     </svg>
                  </a>
                  <a href="#!" class="btn btn-twitter btn-icon">
                     <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-twitter" viewBox="0 0 16 16">
                        <path
                           d="M5.026 15c6.038 0 9.341-5.003 9.341-9.334 0-.14 0-.282-.006-.422A6.685 6.685 0 0 0 16 3.542a6.658 6.658 0 0 1-1.889.518 3.301 3.301 0 0 0 1.447-1.817 6.533 6.533 0 0 1-2.087.793A3.286 3.286 0 0 0 7.875 6.03a9.325 9.325 0 0 1-6.767-3.429 3.289 3.289 0 0 0 1.018 4.382A3.323 3.323 0 0 1 .64 6.575v.045a3.288 3.288 0 0 0 2.632 3.218 3.203 3.203 0 0 1-.865.115 3.23 3.23 0 0 1-.614-.057 3.283 3.283 0 0 0 3.067 2.277A6.588 6.588 0 0 1 .78 13.58a6.32 6.32 0 0 1-.78-.045A9.344 9.344 0 0 0 5.026 15z"></path>
                     </svg>
                  </a>
               </div>
            </div>
         </div>
      </div>
   </div>
</footer>
 <div class="btn-scroll-top">
   <svg class="progress-square svg-content" width="100%" height="100%" viewBox="0 0 40 40">
      <path d="M8 1H32C35.866 1 39 4.13401 39 8V32C39 35.866 35.866 39 32 39H8C4.13401 39 1 35.866 1 32V8C1 4.13401 4.13401 1 8 1Z" />
   </svg>
</div>
 <!-- Libs JS -->
<script src="./assets/libs/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
<script src="./assets/libs/simplebar/dist/simplebar.min.js"></script>
<script src="./assets/libs/headhesive/dist/headhesive.min.js"></script>

<!-- Theme JS -->
<script src="./assets/js/theme.min.js"></script>

      <script src="./assets/libs/parallax-js/dist/parallax.min.js"></script>
      <script src="./assets/js/vendors/parallax.js"></script>
      <script src="./assets/libs/rellax/rellax.min.js"></script>
      <script src="./assets/js/vendors/rellax.js"></script>
      <!-- Swiper JS -->
      <script src="./assets/libs/swiper/swiper-bundle.min.js"></script>
      <script src="./assets/js/vendors/swiper.js"></script>
      <script src="./assets/libs/scrollcue/scrollCue.min.js"></script>
      <script src="./assets/js/vendors/scrollcue.js"></script>
      <script src="./assets/js/vendors/pricing.js"></script>
   </body>
</html>
