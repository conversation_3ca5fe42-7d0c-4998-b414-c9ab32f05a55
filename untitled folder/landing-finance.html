<!doctype html>
<html lang="en">
   <head>
      <!-- Required meta tags -->
<meta charset="utf-8" />
<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />

      <link rel="stylesheet" href="./assets/libs/swiper/swiper-bundle.min.css" />
      <link rel="stylesheet" href="./assets/libs/glightbox/dist/css/glightbox.min.css" />
      <!-- Favicon icon-->
<link rel="apple-touch-icon" sizes="180x180" href="./assets/images/favicon/apple-touch-icon.png" />
<link rel="icon" type="image/png" sizes="32x32" href="./assets/images/favicon/favicon-32x32.png" />
<link rel="icon" type="image/png" sizes="16x16" href="./assets/images/favicon/favicon-16x16.png" />
<link rel="manifest" href="./assets/images/favicon/site.webmanifest" />
<link rel="mask-icon" href="./assets/images/favicon/block-safari-pinned-tab.svg" color="#8b3dff" />
<link rel="shortcut icon" href="./assets/images/favicon/favicon.ico" />
<meta name="msapplication-TileColor" content="#8b3dff" />
<meta name="msapplication-config" content="./assets/images/favicon/tile.xml" />

<!-- Color modes -->
<script src="./assets/js/vendors/color-modes.js"></script>

<!-- Libs CSS -->
<link href="./assets/libs/simplebar/dist/simplebar.min.css" rel="stylesheet" />
<link href="./assets/libs/bootstrap-icons/font/bootstrap-icons.min.css" rel="stylesheet" />

<!-- Scroll Cue -->
<link rel="stylesheet" href="./assets/libs/scrollcue/scrollCue.css" />

<!-- Box icons -->
<link rel="stylesheet" href="./assets/fonts/css/boxicons.min.css" />

<!-- Theme CSS -->
<link rel="stylesheet" href="./assets/css/theme.min.css">

      <title>Finance - Responsive Website Template | Block</title>
   </head>

   <body>
      <!-- Navbar -->
<header>
   <nav class="navbar navbar-expand-lg  transparent navbar-transparent navbar-dark">
      <div class="container px-3">
         <a class="navbar-brand" href="./index.html"><img src="./assets/images/logo/logo.svg" alt /></a>
         <button class="navbar-toggler offcanvas-nav-btn" type="button">
            <i class="bi bi-list"></i>
         </button>
         <div class="offcanvas offcanvas-start offcanvas-nav" style="width: 20rem">
            <div class="offcanvas-header">
               <a href="./index.html" class="text-inverse"><img src="./assets/images/logo/logo.svg" alt /></a>
               <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close"></button>
            </div>
            <div class="offcanvas-body pt-0 align-items-center">
               <ul class="navbar-nav mx-auto align-items-lg-center">
                  <li class="nav-item">
                     <a class="nav-link" href="./home.html">Home</a>
                  </li>
                  <li class="nav-item">
                     <a class="nav-link" href="./scan-pay.html">Scan & Pay</a>
                  </li>
                  <li class="nav-item">
                     <a class="nav-link" href="./service.html">Services</a>
                  </li>
                  <li class="nav-item">
                     <a class="nav-link" href="./contact.html">Contact</a>
                  </li>
                  <li class="nav-item">
                     <a class="nav-link" href="./become-an-agent.html">Become An Agent</a>
                  </li>
               </ul>
               <div class="mt-3 mt-lg-0 d-flex align-items-center">
                  <a href="./signin.html" class="btn btn-light mx-2">Login</a>
                  <a href="https://play.google.com/store/apps/details?id=com.qsoft.aidapay&hl=en&pli=1" class="btn btn-primary">Create account</a>
               </div>
            </div>
         </div>
      </div>
   </nav>
</header>

      <main>
         <!--Hero start-->
         <section class="bg-primary-dark pt-9 right-slant-shape" data-cue="fadeIn">
            <div class="container">
               <div class="row align-items-center">
                  <div class="col-lg-5 col-12">
                     <div class="text-center text-lg-start mb-7 mb-lg-0" data-cues="slideInDown">
                        <div class="mb-4">
                           <h1 class="mb-5 display-5 text-white-stable">
                              Meet the next gen
                              <span class="text-pattern-line text-warning">banking service</span>
                           </h1>
                           <p class="mb-0 text-white-stable lead">Enim sed parturient sem enim nunc sit erat velit eget hac nulla nullam et id praesent nisi ornare risus risus consequat.</p>
                        </div>
                        <div data-cues="slideInDown">
                           <a href="#" class="btn btn-primary me-2">Get Started</a>
                           <a href="#" class="btn btn-outline-warning">Contact Sales</a>
                        </div>
                     </div>
                  </div>
                  <div class="offset-lg-1 col-lg-6 col-12">
                     <div class="position-relative z-1 pt-lg-9" data-cue="slideInRight">
                        <div class="position-relative">
                           <img src="./assets/images/landings/finance/finance-hero-side-img.jpg" alt="video" class="img-fluid rounded-3" width="837" />
                           <a
                              href="https://www.youtube.com/watch?v=CivuutI6lXY"
                              class="play-btn glightbox position-absolute top-50 start-50 translate-middle icon-shape icon-xl rounded-circle text-primary">
                              <i class="bi bi-play-fill"></i>
                           </a>
                        </div>
                     </div>
                  </div>
               </div>
            </div>
         </section>
         <!--Hero start-->

         <!--Featured in start-->
         <div class="my-xl-9 my-5">
            <div class="container">
               <div class="row">
                  <div class="col-lg-10 offset-lg-1 col-12" data-cue="fadeIn">
                     <div class="text-center mb-4 mb-lg-7">
                        <small class="text-uppercase fw-semibold ls-md">As featured in</small>
                     </div>

                     <div
                        class="swiper-container swiper"
                        id="swiper-1"
                        data-pagination-type=""
                        data-speed="400"
                        data-space-between="100"
                        data-pagination="true"
                        data-navigation="false"
                        data-autoplay="true"
                        data-autoplay-delay="3000"
                        data-breakpoints='{"480": {"slidesPerView": 2}, "768": {"slidesPerView": 3}, "1024": {"slidesPerView": 5}}'>
                        <div class="swiper-wrapper pb-6">
                           <div class="swiper-slide">
                              <figure class="text-center">
                                 <img src="./assets/images/client-logo/clients-logo-1.svg" alt="logo" />
                              </figure>
                           </div>
                           <div class="swiper-slide">
                              <figure class="text-center">
                                 <img src="./assets/images/client-logo/clients-logo-2.svg" alt="logo" />
                              </figure>
                           </div>
                           <div class="swiper-slide">
                              <figure class="text-center">
                                 <img src="./assets/images/client-logo/clients-logo-3.svg" alt="logo" />
                              </figure>
                           </div>
                           <div class="swiper-slide">
                              <figure class="text-center">
                                 <img src="./assets/images/client-logo/clients-logo-4.svg" alt="logo" />
                              </figure>
                           </div>
                           <div class="swiper-slide">
                              <figure class="text-center">
                                 <img src="./assets/images/client-logo/clients-logo-5.svg" alt="logo" />
                              </figure>
                           </div>
                           <!-- Add more slides as needed -->
                        </div>
                        <!-- Add Pagination -->
                        <div class="swiper-pagination"></div>
                        <!-- Add Navigation -->
                        <div class="swiper-navigation">
                           <div class="swiper-button-next"></div>
                           <div class="swiper-button-prev"></div>
                        </div>
                     </div>
                  </div>
               </div>
            </div>
         </div>
         <!--Featured in end-->

         <!--Your finance start-->
         <section class="my-xl-9 my-5">
            <div class="container" data-cue="fadeIn">
               <div class="row">
                  <div class="col-xl-8 offset-xl-2">
                     <div class="text-center mb-xl-7 mb-5">
                        <h2 class="mb-3">
                           A one-stop shop for
                           <span class="text-primary">your finances.</span>
                        </h2>
                        <p class="mb-0">Designed to work better together erat velit eget hac nulla nullam et id praesent nisi ornare risus risus consequat nunc nisl pellentesque diam neque.</p>
                     </div>
                  </div>
               </div>
               <div class="table-responsive-lg">
                  <div class="row flex-nowrap pb-4 pb-lg-0 me-5 me-lg-0">
                     <div class="col-lg-4 col-md-6" data-cue="zoomIn">
                        <div class="card border-0 card-primary">
                           <div class="card-body p-5">
                              <div class="position-relative d-inline-block mb-5">
                                 <img src="./assets/images/landings/finance/feature-img-1.jpg" alt="feature" class="avatar avatar-xl rounded-circle border-2 border border-white shadow-sm" />

                                 <div class="position-absolute bottom-0 end-0">
                                    <div class="icon-md icon-shape rounded-circle bg-white me-n2 mb-n2 shadow-sm">
                                       <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" class="bi bi-bank2 text-primary" viewBox="0 0 16 16">
                                          <path
                                             d="M8.277.084a.5.5 0 0 0-.554 0l-7.5 5A.5.5 0 0 0 .5 6h1.875v7H1.5a.5.5 0 0 0 0 1h13a.5.5 0 1 0 0-1h-.875V6H15.5a.5.5 0 0 0 .277-.916l-7.5-5zM12.375 6v7h-1.25V6h1.25zm-2.5 0v7h-1.25V6h1.25zm-2.5 0v7h-1.25V6h1.25zm-2.5 0v7h-1.25V6h1.25zM8 4a1 1 0 1 1 0-2 1 1 0 0 1 0 2zM.5 15a.5.5 0 0 0 0 1h15a.5.5 0 1 0 0-1H.5z" />
                                       </svg>
                                    </div>
                                 </div>
                              </div>
                              <div class="mb-5">
                                 <h4 class="card-title">Checking accounts</h4>
                                 <p class="mb-0 card-text">Interdum et malesuada fames ac ante ipsum primis in faucibus. Sed lacinia gsmod dui euismod id.</p>
                              </div>

                              <a href="#!" class="icon-link icon-link-hover card-link">
                                 View All Accounts
                                 <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" class="bi bi-arrow-right" viewBox="0 0 16 16">
                                    <path
                                       fill-rule="evenodd"
                                       d="M1 8a.5.5 0 0 1 .5-.5h11.793l-3.147-3.146a.5.5 0 0 1 .708-.708l4 4a.5.5 0 0 1 0 .708l-4 4a.5.5 0 0 1-.708-.708L13.293 8.5H1.5A.5.5 0 0 1 1 8z" />
                                 </svg>
                              </a>
                           </div>
                        </div>
                     </div>
                     <div class="col-lg-4 col-md-6" data-cue="zoomIn">
                        <div class="card border-0 card-primary">
                           <div class="card-body p-5">
                              <div class="position-relative d-inline-block mb-5">
                                 <img src="./assets/images/landings/finance/feature-img-2.jpg" alt="feature" class="avatar avatar-xl rounded-circle border-2 border border-white shadow-sm" />

                                 <div class="position-absolute bottom-0 end-0">
                                    <div class="icon-md icon-shape rounded-circle bg-white me-n2 mb-n2 shadow-sm">
                                       <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" class="bi bi-credit-card-2-front-fill text-primary" viewBox="0 0 16 16">
                                          <path
                                             d="M0 4a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V4zm2.5 1a.5.5 0 0 0-.5.5v1a.5.5 0 0 0 .5.5h2a.5.5 0 0 0 .5-.5v-1a.5.5 0 0 0-.5-.5h-2zm0 3a.5.5 0 0 0 0 1h5a.5.5 0 0 0 0-1h-5zm0 2a.5.5 0 0 0 0 1h1a.5.5 0 0 0 0-1h-1zm3 0a.5.5 0 0 0 0 1h1a.5.5 0 0 0 0-1h-1zm3 0a.5.5 0 0 0 0 1h1a.5.5 0 0 0 0-1h-1zm3 0a.5.5 0 0 0 0 1h1a.5.5 0 0 0 0-1h-1z" />
                                       </svg>
                                    </div>
                                 </div>
                              </div>
                              <div class="mb-5">
                                 <h4 class="card-title">Credit cards</h4>
                                 <p class="mb-0 card-text">Nullam sodales, libero ac dictum convallis, ipsum diam cursus stibulum lacinia ultricies eleifend.</p>
                              </div>

                              <a href="#!" class="icon-link icon-link-hover card-link">
                                 Apply Credit Cards
                                 <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" class="bi bi-arrow-right" viewBox="0 0 16 16">
                                    <path
                                       fill-rule="evenodd"
                                       d="M1 8a.5.5 0 0 1 .5-.5h11.793l-3.147-3.146a.5.5 0 0 1 .708-.708l4 4a.5.5 0 0 1 0 .708l-4 4a.5.5 0 0 1-.708-.708L13.293 8.5H1.5A.5.5 0 0 1 1 8z" />
                                 </svg>
                              </a>
                           </div>
                        </div>
                     </div>
                     <div class="col-lg-4 col-md-6" data-cue="zoomIn">
                        <div class="card border-0 card-primary">
                           <div class="card-body p-5">
                              <div class="position-relative d-inline-block mb-5">
                                 <img src="./assets/images/landings/finance/feature-img-3.jpg" alt="feature" class="avatar avatar-xl rounded-circle border-2 border border-white shadow-sm" />

                                 <div class="position-absolute bottom-0 end-0">
                                    <div class="icon-md icon-shape rounded-circle bg-white me-n2 mb-n2 shadow-sm">
                                       <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" class="bi bi-cash-stack text-primary" viewBox="0 0 16 16">
                                          <path d="M1 3a1 1 0 0 1 1-1h12a1 1 0 0 1 1 1H1zm7 8a2 2 0 1 0 0-4 2 2 0 0 0 0 4z" />
                                          <path d="M0 5a1 1 0 0 1 1-1h14a1 1 0 0 1 1 1v8a1 1 0 0 1-1 1H1a1 1 0 0 1-1-1V5zm3 0a2 2 0 0 1-2 2v4a2 2 0 0 1 2 2h10a2 2 0 0 1 2-2V7a2 2 0 0 1-2-2H3z" />
                                       </svg>
                                    </div>
                                 </div>
                              </div>
                              <div class="mb-5">
                                 <h4 class="card-title">Investment</h4>
                                 <p class="mb-0 card-text">In a odio sit amet nisi tincidunt congue. Mauris cursus magna a vestibulum rutrum.</p>
                              </div>

                              <a href="#!" class="icon-link icon-link-hover card-link">
                                 Start Investments
                                 <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" class="bi bi-arrow-right" viewBox="0 0 16 16">
                                    <path
                                       fill-rule="evenodd"
                                       d="M1 8a.5.5 0 0 1 .5-.5h11.793l-3.147-3.146a.5.5 0 0 1 .708-.708l4 4a.5.5 0 0 1 0 .708l-4 4a.5.5 0 0 1-.708-.708L13.293 8.5H1.5A.5.5 0 0 1 1 8z" />
                                 </svg>
                              </a>
                           </div>
                        </div>
                     </div>
                  </div>
               </div>
               <div class="row mt-6" data-cue="fadeIn">
                  <div class="col-xl-10 offset-xl-1">
                     <ul class="list-inline">
                        <li class="list-inline-item d-inline-flex align-items-center me-3 mb-2 mb-lg-0">
                           <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-check-circle-fill text-success" viewBox="0 0 16 16">
                              <path
                                 d="M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zm-3.97-3.03a.75.75 0 0 0-1.08.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-.01-1.05z" />
                           </svg>
                           <span class="ms-2">24/7 account monitoring</span>
                        </li>
                        <li class="list-inline-item d-inline-flex align-items-center me-3 mb-2 mb-lg-0">
                           <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-check-circle-fill text-success" viewBox="0 0 16 16">
                              <path
                                 d="M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zm-3.97-3.03a.75.75 0 0 0-1.08.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-.01-1.05z" />
                           </svg>
                           <span class="ms-2 me-3">Protection & peace of mind</span>
                        </li>
                        <li class="list-inline-item d-inline-flex align-items-center me-3 mb-2 mb-lg-0">
                           <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-check-circle-fill text-success" viewBox="0 0 16 16">
                              <path
                                 d="M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zm-3.97-3.03a.75.75 0 0 0-1.08.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-.01-1.05z" />
                           </svg>
                           <span class="ms-2">Anytime, anywhere support</span>
                        </li>
                        <li class="list-inline-item d-inline-flex align-items-center me-3">
                           <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-check-circle-fill text-success" viewBox="0 0 16 16">
                              <path
                                 d="M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zm-3.97-3.03a.75.75 0 0 0-1.08.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-.01-1.05z" />
                           </svg>
                           <span class="ms-2">Serious security</span>
                        </li>
                     </ul>
                  </div>
               </div>
            </div>
         </section>
         <!--Your finance end-->

         <!--5m member start-->
         <section class="py-xl-9 py-5 bg-primary-dark">
            <div class="container" data-cue="fadeIn">
               <div class="row">
                  <div class="col-xl-8 offset-xl-2 col-12">
                     <div class="text-center mb-xl-7 mb-5">
                        <h2 class="text-white-stable mb-3">Why do over 5M members love?</h2>
                        <p class="mb-0 text-white-50">
                           Enim sed parturient sem enim nunc sit erat velit eget hac nulla nullam et id praesent nisi ornare risus risus consequat nunc nisl pellentesque diam neque.
                        </p>
                     </div>
                  </div>
               </div>
               <div class="row mb-7 pb-4 g-5 text-center text-lg-start">
                  <div class="col-md-4" data-cue="fadeIn">
                     <h4 class="text-white-stable">Flexible payments</h4>
                     <p class="text-white-50 mb-0">you are able to run your business smoothly; handle your daily expenses and secure your cash flow.</p>
                  </div>
                  <div class="col-md-4" data-cue="fadeIn">
                     <h4 class="text-white-stable">Smart payments</h4>
                     <p class="text-white-50 mb-0">Nullam sodales, libero ac dictum convallis, ipsum diam cursus stibulum lacinia ultricies eleifend..</p>
                  </div>
                  <div class="col-md-4" data-cue="fadeIn">
                     <h4 class="text-white-stable">Easy administration</h4>
                     <p class="text-white-50 mb-0">In a odio sit amet nisi tincidunt congue. Mauris cursus magna a vestibulum rutrum. Vivamus sit amet luctus leo.</p>
                  </div>
               </div>
               <div class="row border-primary border-top g-5 g-lg-0 text-center text-lg-start" data-cue="fadeIn">
                  <div class="col-lg-3 col-6 border-end-lg border-md-0 border-lg-primary" data-cue="fadeIn">
                     <div class="p-lg-5">
                        <h5 class="h1 text-white-stable mb-0">5M+</h5>
                        <span class="text-white-50">Members</span>
                     </div>
                  </div>
                  <div class="col-lg-3 col-6 border-end-lg border-md-0 border-lg-primary" data-cue="fadeIn">
                     <div class="p-lg-5">
                        <h5 class="h1 text-white-stable mb-0">95%</h5>
                        <span class="text-white-50">Customer satisfaction</span>
                     </div>
                  </div>
                  <div class="col-lg-3 col-6 border-end-lg border-md-0 border-lg-primary" data-cue="fadeIn">
                     <div class="p-lg-5">
                        <h5 class="h1 text-white-stable mb-0">73%</h5>
                        <span class="text-white-50">Over year growth</span>
                     </div>
                  </div>
                  <div class="col-lg-3 col-6" data-cue="fadeIn">
                     <div class="p-lg-5">
                        <h5 class="h1 text-white-stable mb-0">250B</h5>
                        <span class="text-white-50">Money managed</span>
                     </div>
                  </div>
               </div>
            </div>
         </section>
         <!--5m member end-->

         <!--Product designer start-->
         <section class="my-xl-9 my-5">
            <div class="container">
               <div class="row">
                  <div class="col-lg-8 offset-lg-2">
                     <div class="text-center mb-xl-7 mb-5" data-cue="fadeIn">
                        <h2 class="mb-3">
                           Products designed for
                           <span class="text-primary">all sizes businesses.</span>
                        </h2>
                        <p class="mb-0">Enim sed parturient sem enim nunc sit erat velit eget hac nulla nullam et id praesent nisi ornare risus risus consequat nunc nisl pellentesque diam neque.</p>
                     </div>
                  </div>
               </div>
               <div class="row align-items-center">
                  <div class="col-xl-5 col-md-6 col-12">
                     <div class="nav flex-column nav-pills mb-5 mb-lg-0" id="v-pills-tab" role="tablist" aria-orientation="vertical">
                        <a
                           href="#"
                           class="nav-link active d-flex text-start align-items-center align-items-lg-start p-xl-4 p-3"
                           id="v-pills-small-business-tab"
                           data-bs-toggle="pill"
                           data-bs-target="#v-pills-small-business"
                           role="tab"
                           aria-controls="v-pills-small-business"
                           aria-selected="true">
                           <div class="d-flex">
                              <div class="icon-md icon-shape rounded-circle bg-white shadow-sm">
                                 <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" class="bi bi-bank2 text-primary" viewBox="0 0 16 16">
                                    <path
                                       d="M8.277.084a.5.5 0 0 0-.554 0l-7.5 5A.5.5 0 0 0 .5 6h1.875v7H1.5a.5.5 0 0 0 0 1h13a.5.5 0 1 0 0-1h-.875V6H15.5a.5.5 0 0 0 .277-.916l-7.5-5zM12.375 6v7h-1.25V6h1.25zm-2.5 0v7h-1.25V6h1.25zm-2.5 0v7h-1.25V6h1.25zm-2.5 0v7h-1.25V6h1.25zM8 4a1 1 0 1 1 0-2 1 1 0 0 1 0 2zM.5 15a.5.5 0 0 0 0 1h15a.5.5 0 1 0 0-1H.5z" />
                                 </svg>
                              </div>
                           </div>
                           <div class="ms-4">
                              <h4 class="mb-0">For small business</h4>
                              <p class="mb-0 mt-lg-3 d-none d-lg-block">Interdum et malesuad a fames ac ante ipsum primis in faucibus. Simple dummy content. Sed lacinia gsmod dui euismod id.</p>
                           </div>
                        </a>
                        <a
                           href="#"
                           class="nav-link d-flex text-start align-items-center align-items-lg-start p-xl-4 p-3"
                           id="v-pills-profile-tab"
                           data-bs-toggle="pill"
                           data-bs-target="#v-pills-profile"
                           role="tab"
                           aria-controls="v-pills-profile"
                           aria-selected="false">
                           <div class="d-flex">
                              <div class="icon-md icon-shape rounded-circle bg-white shadow-sm">
                                 <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" class="bi bi-credit-card-2-front-fill text-primary" viewBox="0 0 16 16">
                                    <path
                                       d="M0 4a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V4zm2.5 1a.5.5 0 0 0-.5.5v1a.5.5 0 0 0 .5.5h2a.5.5 0 0 0 .5-.5v-1a.5.5 0 0 0-.5-.5h-2zm0 3a.5.5 0 0 0 0 1h5a.5.5 0 0 0 0-1h-5zm0 2a.5.5 0 0 0 0 1h1a.5.5 0 0 0 0-1h-1zm3 0a.5.5 0 0 0 0 1h1a.5.5 0 0 0 0-1h-1zm3 0a.5.5 0 0 0 0 1h1a.5.5 0 0 0 0-1h-1zm3 0a.5.5 0 0 0 0 1h1a.5.5 0 0 0 0-1h-1z" />
                                 </svg>
                              </div>
                           </div>
                           <div class="ms-4">
                              <h4 class="mb-0">For startups</h4>
                              <p class="mb-0 mt-lg-3 d-none d-lg-block">Nullam sodales, libero ac dictum convallis, ipsum diam cursus stibulum lacinia ultricies eleifend. Simple dummy content.</p>
                           </div>
                        </a>
                        <a
                           href="#"
                           class="nav-link d-flex text-start p-xl-4 p-3 align-items-center align-items-lg-start"
                           id="v-pills-enterprises-tab"
                           data-bs-toggle="pill"
                           data-bs-target="#v-pills-enterprises"
                           role="tab"
                           aria-controls="v-pills-enterprises"
                           aria-selected="false">
                           <div class="d-flex">
                              <div class="icon-md icon-shape rounded-circle bg-white shadow-sm">
                                 <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" class="bi bi-cash-stack text-primary" viewBox="0 0 16 16">
                                    <path d="M1 3a1 1 0 0 1 1-1h12a1 1 0 0 1 1 1H1zm7 8a2 2 0 1 0 0-4 2 2 0 0 0 0 4z" />
                                    <path d="M0 5a1 1 0 0 1 1-1h14a1 1 0 0 1 1 1v8a1 1 0 0 1-1 1H1a1 1 0 0 1-1-1V5zm3 0a2 2 0 0 1-2 2v4a2 2 0 0 1 2 2h10a2 2 0 0 1 2-2V7a2 2 0 0 1-2-2H3z" />
                                 </svg>
                              </div>
                           </div>
                           <div class="ms-4">
                              <h4 class="mb-0">For enterprises</h4>
                              <p class="mb-0 mt-lg-3 d-none d-lg-block">
                                 In a odio sit amet nisi tincidunt congue. Mauris cursus magna a vestibulum rutrum. Vivamus sit amet luctus leo. Simple dummy content.
                              </p>
                           </div>
                        </a>
                     </div>
                  </div>
                  <div class="col-xl-6 offset-xl-1 col-md-6 col-12">
                     <div class="tab-content" id="v-pills-tabContent">
                        <div class="tab-pane fade show active" id="v-pills-small-business" role="tabpanel" aria-labelledby="v-pills-small-business-tab" tabindex="0">
                           <div class="position-relative scene" data-relative-input="true">
                              <figure><img src="./assets/images/landings/finance/finance-tab-3.jpg" alt="finance" class="img-fluid rounded-3" data-cue="fadeIn" /></figure>

                              <div class="position-relative" data-depth="0.05">
                                 <img src="./assets/images/landings/finance/card.svg" alt="" class="position-absolute bottom-0 end-0 px-4" />
                              </div>
                           </div>
                        </div>
                        <div class="tab-pane fade" id="v-pills-profile" role="tabpanel" aria-labelledby="v-pills-profile-tab" tabindex="0">
                           <div class="position-relative scene" data-relative-input="true">
                              <figure><img src="./assets/images/landings/finance/finance-tab-2.jpg" alt="finance" class="img-fluid rounded-3" data-cue="fadeIn" /></figure>

                              <div class="position-relative" data-depth="0.05">
                                 <img src="./assets/images/landings/finance/card.svg" alt="" class="position-absolute bottom-0 start-0 px-4" />
                              </div>
                           </div>
                        </div>

                        <div class="tab-pane fade" id="v-pills-enterprises" role="tabpanel" aria-labelledby="v-pills-enterprises-tab" tabindex="0">
                           <div class="position-relative scene" data-relative-input="true">
                              <figure><img src="./assets/images/landings/finance/finance-tab-1.jpg" alt="finance" class="img-fluid rounded-3" /></figure>

                              <div class="position-relative" data-depth="0.05">
                                 <img src="./assets/images/landings/finance/card.svg" alt="" class="position-absolute bottom-0 start-50 translate-middle-x" />
                              </div>
                           </div>
                        </div>
                     </div>
                  </div>
               </div>
            </div>
         </section>
         <!--Product designer end-->

         <!--Get block card start-->
         <section class="my-xl-7 py-5">
            <div class="container" data-cue="fadeIn">
               <div class="row">
                  <div class="col-lg-5 col-md-12" data-cue="fadeIn">
                     <div class="mb-xl-7 mb-5">
                        <h2 class="mb-3">
                           How to get a Block Card in a
                           <span class="text-primary">simple 3 steps</span>
                        </h2>
                        <p class="mb-0">Designed to work better together erat velit eget hac nulla nullam et id praesent nisi ornare risus risus consequat nunc nisl pellentesque diam neque.</p>
                     </div>
                  </div>
               </div>
               <div class="table-responsive-xl">
                  <div class="row flex-nowrap pb-4 pb-lg-0 me-5 me-lg-0">
                     <div class="col-lg-4 col-md-6 col-12" data-cue="slideInLeft">
                        <div class="p-xl-5">
                           <div class="d-flex align-items-center justify-content-between mb-5">
                              <div class="icon-xl icon-shape rounded-circle bg-primary border border-primary-subtle border-4 text-white-stable fw-semibold fs-3">1</div>
                              <span>
                                 <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" fill="currentColor" class="bi bi-arrow-right text-body-tertiary" viewBox="0 0 16 16">
                                    <path
                                       fill-rule="evenodd"
                                       d="M1 8a.5.5 0 0 1 .5-.5h11.793l-3.147-3.146a.5.5 0 0 1 .708-.708l4 4a.5.5 0 0 1 0 .708l-4 4a.5.5 0 0 1-.708-.708L13.293 8.5H1.5A.5.5 0 0 1 1 8z" />
                                 </svg>
                              </span>
                           </div>

                           <h3 class="h4">Sign up for a free account</h3>
                           <p class="mb-0">Apply online on block website and fill the form by telling us your name, address, date of birth.</p>
                        </div>
                     </div>
                     <div class="col-lg-4 col-md-6 col-12" data-cue="slideInLeft">
                        <div class="p-xl-5">
                           <div class="d-flex align-items-center justify-content-between mb-5">
                              <div class="icon-xl icon-shape rounded-circle bg-primary border border-primary-subtle border-4 text-white-stable fw-semibold fs-3">2</div>
                              <span>
                                 <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" fill="currentColor" class="bi bi-arrow-right text-body-tertiary" viewBox="0 0 16 16">
                                    <path
                                       fill-rule="evenodd"
                                       d="M1 8a.5.5 0 0 1 .5-.5h11.793l-3.147-3.146a.5.5 0 0 1 .708-.708l4 4a.5.5 0 0 1 0 .708l-4 4a.5.5 0 0 1-.708-.708L13.293 8.5H1.5A.5.5 0 0 1 1 8z" />
                                 </svg>
                              </span>
                           </div>

                           <h3 class="h4">Fill in your details</h3>
                           <p class="mb-0">Get started on block or log into the mobile app. Bank account to transfer money to your debit card.</p>
                        </div>
                     </div>
                     <div class="col-lg-4 col-md-6 col-12" data-cue="slideInLeft">
                        <div class="p-xl-5">
                           <div class="d-flex align-items-center justify-content-between mb-5">
                              <div class="icon-xl icon-shape rounded-circle bg-primary border border-primary-subtle border-4 text-white-stable fw-semibold fs-3">3</div>
                              <span>
                                 <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" class="bi bi-check-circle-fill text-success" viewBox="0 0 16 16">
                                    <path
                                       d="M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zm-3.97-3.03a.75.75 0 0 0-1.08.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-.01-1.05z" />
                                 </svg>
                              </span>
                           </div>

                           <h3 class="h4">Start converting!</h3>
                           <p class="mb-0">Set up direct deposit or connect your current bank account to transfer money to your debit card.</p>
                        </div>
                     </div>
                  </div>
               </div>
               <div class="row">
                  <div class="col-lg-12" data-cue="zoomIn">
                     <div class="text-center my-5">
                        <a href="#!" class="btn btn-outline-primary">Open an Account</a>
                     </div>
                  </div>
               </div>
            </div>
         </section>
         <!--Get block card end-->

         <!--Customer stories start-->
         <section class="py-xl-9 py-5 bg-gray-100">
            <div class="container" data-cue="fadeIn">
               <div class="row">
                  <div class="col-lg-6 offset-lg-3">
                     <div class="text-center mb-xl-7 mb-5">
                        <h2 class="mb-3">Customer success stories</h2>
                        <p class="mb-0">Create convincing customer success stories to boost sales, build trust with prospects and increase customer loyalty.</p>
                     </div>
                  </div>
               </div>
               <div class="row g-4">
                  <div class="col-lg-6 col-md-12">
                     <!-- Testimonials with logo -->
                     <div class="card shadow-sm" data-cue="slideInLeft">
                        <div class="card-body">
                           <p class="mb-5 lead">
                              “Lorem ipsum dolor sit amet, consectetur adipiscing elit. In tincidunt, lectus non finibus porta, ipsum lacus tempus quam, a iaculis metus ipsum sed elit.”
                           </p>
                           <div class="d-md-flex align-items-center justify-content-between">
                              <div class="d-flex align-items-center mb-3 mb-lg-0">
                                 <div>
                                    <img src="./assets/images/avatar/avatar-1.jpg" alt="Avatar" class="avatar avatar-lg rounded-circle" />
                                 </div>
                                 <div class="ms-3">
                                    <h6 class="mb-0 h5">Jitu Chauhan</h6>
                                    <span class="small text-body-tertiary">Front End Developers @codescandy</span>
                                 </div>
                              </div>
                              <div class="d-none d-md-block text-inverse">
                                 <img src="./assets/images/client-logo/logoipsum-1.svg" alt="logo" />
                              </div>
                           </div>
                        </div>
                     </div>
                  </div>
                  <div class="col-lg-6 col-md-12">
                     <!-- Testimonials with logo -->
                     <div class="card shadow-sm" data-cue="slideInRight">
                        <div class="card-body">
                           <p class="mb-5 lead">
                              "Cum molestias sed quam eos repudiandae repellat! Sunt ex atque tempore eligendi magni vel obcaecati facere. Cupiditate repellat deleniti eos eum atque possimus ducimus
                              quis."
                           </p>
                           <div class="d-md-flex align-items-center justify-content-between">
                              <div class="d-flex align-items-center mb-3 mb-lg-0">
                                 <div>
                                    <img src="././assets/images/avatar/avatar-4.jpg" alt="Avatar" class="avatar avatar-lg rounded-circle" />
                                 </div>
                                 <div class="ms-3">
                                    <h6 class="mb-0 h5">Sandeep Chauhan</h6>
                                    <span class="small text-body-tertiary">Front End Developers @codescandy</span>
                                 </div>
                              </div>
                              <div class="d-none d-md-block text-inverse">
                                 <img src="././assets/images/client-logo/logoipsum-2.svg" alt="logo" />
                              </div>
                           </div>
                        </div>
                     </div>
                  </div>
               </div>
            </div>
         </section>
         <!--Customer stories end-->

         <!--Call to action start-->
         <section>
            <div style="background-image: url(./assets/images/pattern/cta-pattern.png); background-position: center; background-repeat: no-repeat; background-size: cover" class="py-7 bg-primary-dark">
               <div class="container my-lg-7" data-cue="zoomIn">
                  <div class="row">
                     <div class="col-lg-8 offset-lg-2">
                        <div class="text-center mb-5">
                           <h2 class="text-white-stable mb-3">Experience the next-gen banking</h2>
                           <p class="mb-0 text-white-50">
                              Enim sed parturient sem enim nunc sit erat velit eget hac nulla nullam et id praesent nisi ornare risus risus consequat nunc nisl pellentesque diam neque.
                           </p>
                        </div>
                     </div>
                     <div class="col-lg-12">
                        <div class="text-center">
                           <a href="#!" class="btn btn-primary">Open an account today</a>
                        </div>
                     </div>
                  </div>
               </div>
            </div>
         </section>
         <!--Call to action end-->
      </main>
      <footer class="pt-7">
   <div class="container">
      <!-- Footer 4 column -->
      <div class="row">
         <div class="col-lg-9 col-12">
            <div class="row" id="ft-links">
               <div class="col-lg-3 col-12">
                  <div class="position-relative">
                     <div class="mb-3 pb-2 d-flex justify-content-between border-bottom border-bottom-lg-0">
                        <h4>Service</h4>
                        <a class="d-block d-lg-none stretched-link text-body" data-bs-toggle="collapse" href="#collapseLanding" role="button" aria-expanded="true" aria-controls="collapseLanding">
                           <i class="bi bi-chevron-down"></i>
                        </a>
                     </div>
                     <div class="d-lg-block collapse show" id="collapseLanding" data-bs-parent="#ft-links" style="">
                        <ul class="list-unstyled mb-0 py-3 py-lg-0">
                           <li class="mb-2">
                              <a href="./index.html" class="text-decoration-none text-reset">Web App Development</a>
                           </li>
                           <li class="mb-2">
                              <a href="#!" class="text-decoration-none text-reset">Front End Development</a>
                           </li>
                           <li class="mb-2">
                              <a href="#!" class="text-decoration-none text-reset">MVP Development</a>
                           </li>
                           <li class="mb-2">
                              <a href="#!" class="text-decoration-none text-reset">Digital Marketing</a>
                           </li>
                           <li class="mb-2">
                              <a href="#!" class="text-decoration-none text-reset">Content Writing</a>
                           </li>
                        </ul>
                     </div>
                  </div>
               </div>
               <div class="col-lg-3 col-12">
                  <div>
                     <div class="mb-3 pb-2 d-flex justify-content-between border-bottom border-bottom-lg-0 position-relative">
                        <h4>About us</h4>
                        <a
                           class="d-block d-lg-none stretched-link text-body collapsed"
                           data-bs-toggle="collapse"
                           href="#collapseAccounts"
                           role="button"
                           aria-expanded="false"
                           aria-controls="collapseAccounts">
                           <i class="bi bi-chevron-down"></i>
                        </a>
                     </div>
                     <div class="collapse d-lg-block" id="collapseAccounts" data-bs-parent="#ft-links">
                        <ul class="list-unstyled mb-0 py-3 py-lg-0">
                           <li class="mb-2">
                              <a href="#!" class="text-decoration-none text-reset">Case Studies</a>
                           </li>
                           <li class="mb-2">
                              <a href="#!" class="text-decoration-none text-reset">Blog</a>
                           </li>
                           <li class="mb-2">
                              <a href="#!" class="text-decoration-none text-reset">Services</a>
                           </li>
                           <li class="mb-2">
                              <a href="#!" class="text-decoration-none text-reset">About</a>
                           </li>
                           <li class="mb-2">
                              <a href="#!" class="text-decoration-none text-reset">Career</a>
                           </li>
                        </ul>
                     </div>
                  </div>
               </div>
               <div class="col-lg-3 col-12">
                  <div class="mb-3 pb-2 d-flex justify-content-between border-bottom border-bottom-lg-0 position-relative">
                     <h4>Technology</h4>
                     <a
                        class="d-block d-lg-none stretched-link text-body collapsed"
                        data-bs-toggle="collapse"
                        href="#collapseResources"
                        role="button"
                        aria-expanded="false"
                        aria-controls="collapseResources">
                        <i class="bi bi-chevron-down"></i>
                     </a>
                  </div>
                  <div class="collapse d-lg-block" id="collapseResources" data-bs-parent="#ft-links">
                     <ul class="list-unstyled mb-0 py-3 py-lg-0">
                        <li class="mb-2">
                           <a href="./docs/index.html" class="text-decoration-none text-reset">Next.js</a>
                        </li>
                        <li class="mb-2">
                           <a href="#!" class="text-decoration-none text-reset">Sanity</a>
                        </li>
                        <li class="mb-2">
                           <a href="./changelog.html" class="text-decoration-none text-reset">Content ful</a>
                        </li>
                        <li class="mb-2">
                           <a href="#!" class="text-decoration-none text-reset">Vercel</a>
                        </li>
                        <li class="mb-2">
                           <a href="#!" class="text-decoration-none text-reset">Netlify</a>
                        </li>
                     </ul>
                  </div>
               </div>
               <div class="col-lg-3 col-12">
                  <div class="mb-3 pb-2 d-flex justify-content-between border-bottom border-bottom-lg-0 position-relative">
                     <h4>Locations</h4>
                     <a
                        class="d-block d-lg-none stretched-link text-body collapsed"
                        data-bs-toggle="collapse"
                        href="#collapseLocations"
                        role="button"
                        aria-expanded="false"
                        aria-controls="collapseLocations">
                        <i class="bi bi-chevron-down"></i>
                     </a>
                  </div>
                  <div class="collapse d-lg-block" id="collapseLocations" data-bs-parent="#ft-links">
                     <ul class="list-unstyled mb-0 py-3 py-lg-0">
                        <li class="mb-2">
                           <a href="./docs/index.html" class="text-decoration-none text-reset">India</a>
                        </li>
                        <li class="mb-2">
                           <a href="#!" class="text-decoration-none text-reset">Australia</a>
                        </li>
                        <li class="mb-2">
                           <a href="./changelog.html" class="text-decoration-none text-reset">Brazil</a>
                        </li>
                        <li class="mb-2">
                           <a href="#!" class="text-decoration-none text-reset">Canada</a>
                        </li>
                     </ul>
                  </div>
               </div>
            </div>
         </div>
         <div class="col-lg-3 col-12">
            <div class="me-7">
               <h4 class="mb-4">Headquarters</h4>
               <p class="text-body-secondary">Codescandy, 412, Residency Rd, Shanthala Nagar, Ashok Nagar, Bengaluru, Karnataka, India 560025</p>
            </div>
         </div>
      </div>
   </div>
   <div class="container mt-7 pt-lg-7 pb-4">
      <div class="row align-items-center">
         <div class="col-md-3">
            <a class="mb-4 mb-lg-0 d-block text-inverse" href="../index.html"><img src="./assets/images/logo/logo.svg" alt="" /></a>
         </div>
         <div class="col-md-9 col-lg-6">
            <div class="small mb-3 mb-lg-0 text-lg-center">
               Copyright © 2024

               <span class="text-primary"><a href="#">Block Bootstrap 5 Theme</a></span>
               | Designed by
               <span class="text-primary"><a href="#">CodesCandy</a></span>
            </div>
         </div>
         <div class="col-lg-3">
            <div class="text-lg-end d-flex align-items-center justify-content-lg-end">
               <div class="dropdown">
                  <button class="btn btn-light btn-icon rounded-circle d-flex align-items-center" type="button" aria-expanded="false" data-bs-toggle="dropdown" aria-label="Toggle theme (auto)">
                     <i class="bi theme-icon-active lh-1"><i class="bi theme-icon bi-sun-fill"></i></i>
                     <span class="visually-hidden bs-theme-text">Toggle theme</span>
                  </button>
                  <ul class="dropdown-menu dropdown-menu-end shadow" aria-labelledby="bs-theme-text">
                     <li>
                        <button type="button" class="dropdown-item d-flex align-items-center active" data-bs-theme-value="light" aria-pressed="true">
                           <i class="bi theme-icon bi-sun-fill"></i>
                           <span class="ms-2">Light</span>
                        </button>
                     </li>
                     <li>
                        <button type="button" class="dropdown-item d-flex align-items-center" data-bs-theme-value="dark" aria-pressed="false">
                           <i class="bi theme-icon bi-moon-stars-fill"></i>
                           <span class="ms-2">Dark</span>
                        </button>
                     </li>
                     <li>
                        <button type="button" class="dropdown-item d-flex align-items-center" data-bs-theme-value="auto" aria-pressed="false">
                           <i class="bi theme-icon bi-circle-half"></i>
                           <span class="ms-2">Auto</span>
                        </button>
                     </li>
                  </ul>
               </div>
               <div class="ms-3 d-flex gap-2">
                  <a href="#!" class="btn btn-instagram btn-light btn-icon">
                     <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-instagram" viewBox="0 0 16 16">
                        <path
                           d="M8 0C5.829 0 5.556.01 4.703.048 3.85.088 3.269.222 2.76.42a3.917 3.917 0 0 0-1.417.923A3.927 3.927 0 0 0 .42 2.76C.222 3.268.087 3.85.048 4.7.01 5.555 0 5.827 0 8.001c0 2.172.01 2.444.048 3.297.04.852.174 1.433.372 1.942.205.526.478.972.923 1.417.444.445.89.719 1.416.923.51.198 1.09.333 1.942.372C5.555 15.99 5.827 16 8 16s2.444-.01 3.298-.048c.851-.04 1.434-.174 1.943-.372a3.916 3.916 0 0 0 1.416-.923c.445-.445.718-.891.923-1.417.197-.509.332-1.09.372-1.942C15.99 10.445 16 10.173 16 8s-.01-2.445-.048-3.299c-.04-.851-.175-1.433-.372-1.941a3.926 3.926 0 0 0-.923-1.417A3.911 3.911 0 0 0 13.24.42c-.51-.198-1.092-.333-1.943-.372C10.443.01 10.172 0 7.998 0h.003zm-.717 1.442h.718c2.136 0 2.389.007 3.232.046.78.035 1.204.166 1.486.275.373.145.64.319.92.599.28.28.453.546.598.92.11.281.24.705.275 1.485.039.843.047 1.096.047 3.231s-.008 2.389-.047 3.232c-.035.78-.166 1.203-.275 1.485a2.47 2.47 0 0 1-.599.919c-.28.28-.546.453-.92.598-.28.11-.704.24-1.485.276-.843.038-1.096.047-3.232.047s-2.39-.009-3.233-.047c-.78-.036-1.203-.166-1.485-.276a2.478 2.478 0 0 1-.92-.598 2.48 2.48 0 0 1-.6-.92c-.109-.281-.24-.705-.275-1.485-.038-.843-.046-1.096-.046-3.233 0-2.136.008-2.388.046-3.231.036-.78.166-1.204.276-1.486.145-.373.319-.64.599-.92.28-.28.546-.453.92-.598.282-.11.705-.24 1.485-.276.738-.034 1.024-.044 2.515-.045v.002zm4.988 1.328a.96.96 0 1 0 0 1.92.96.96 0 0 0 0-1.92zm-4.27 1.122a4.109 4.109 0 1 0 0 8.217 4.109 4.109 0 0 0 0-8.217zm0 1.441a2.667 2.667 0 1 1 0 5.334 2.667 2.667 0 0 1 0-5.334z"></path>
                     </svg>
                  </a>
                  <a href="#!" class="btn btn-facebook btn-icon">
                     <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-facebook" viewBox="0 0 16 16">
                        <path
                           d="M16 8.049c0-4.446-3.582-8.05-8-8.05C3.58 0-.002 3.603-.002 8.05c0 4.017 2.926 7.347 6.75 7.951v-5.625h-2.03V8.05H6.75V6.275c0-2.017 1.195-3.131 3.022-3.131.876 0 1.791.157 1.791.157v1.98h-1.009c-.993 0-1.303.621-1.303 1.258v1.51h2.218l-.354 2.326H9.25V16c3.824-.604 6.75-3.934 6.75-7.951z"></path>
                     </svg>
                  </a>
                  <a href="#!" class="btn btn-twitter btn-icon">
                     <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-twitter" viewBox="0 0 16 16">
                        <path
                           d="M5.026 15c6.038 0 9.341-5.003 9.341-9.334 0-.14 0-.282-.006-.422A6.685 6.685 0 0 0 16 3.542a6.658 6.658 0 0 1-1.889.518 3.301 3.301 0 0 0 1.447-1.817 6.533 6.533 0 0 1-2.087.793A3.286 3.286 0 0 0 7.875 6.03a9.325 9.325 0 0 1-6.767-3.429 3.289 3.289 0 0 0 1.018 4.382A3.323 3.323 0 0 1 .64 6.575v.045a3.288 3.288 0 0 0 2.632 3.218 3.203 3.203 0 0 1-.865.115 3.23 3.23 0 0 1-.614-.057 3.283 3.283 0 0 0 3.067 2.277A6.588 6.588 0 0 1 .78 13.58a6.32 6.32 0 0 1-.78-.045A9.344 9.344 0 0 0 5.026 15z"></path>
                     </svg>
                  </a>
               </div>
            </div>
         </div>
      </div>
   </div>
</footer>
 <div class="btn-scroll-top">
   <svg class="progress-square svg-content" width="100%" height="100%" viewBox="0 0 40 40">
      <path d="M8 1H32C35.866 1 39 4.13401 39 8V32C39 35.866 35.866 39 32 39H8C4.13401 39 1 35.866 1 32V8C1 4.13401 4.13401 1 8 1Z" />
   </svg>
</div>
 <!-- Libs JS -->
<script src="./assets/libs/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
<script src="./assets/libs/simplebar/dist/simplebar.min.js"></script>
<script src="./assets/libs/headhesive/dist/headhesive.min.js"></script>

<!-- Theme JS -->
<script src="./assets/js/theme.min.js"></script>

      <script src="./assets/libs/jarallax/dist/jarallax.min.js"></script>
      <script src="./assets/js/vendors/jarallax.js"></script>
      <script src="./assets/libs/parallax-js/dist/parallax.min.js"></script>
      <script src="./assets/js/vendors/parallax.js"></script>
      <!-- Swiper JS -->
      <script src="./assets/libs/swiper/swiper-bundle.min.js"></script>
      <script src="./assets/js/vendors/swiper.js"></script>
      <script src="./assets/libs/glightbox/dist/js/glightbox.min.js"></script>
      <script src="./assets/js/vendors/glight.js"></script>
      <script src="./assets/libs/scrollcue/scrollCue.min.js"></script>
      <script src="./assets/js/vendors/scrollcue.js"></script>
   </body>
</html>
