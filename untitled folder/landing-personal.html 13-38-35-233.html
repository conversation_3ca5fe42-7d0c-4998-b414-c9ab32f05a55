<!doctype html>
<html lang="en">
   <head>
      @@include("partials/head/meta.html")
      <link rel="stylesheet" href="@@webRoot/node_modules/swiper/swiper-bundle.min.css" />
      @@include("partials/head/head-links.html")
      <title>Personal - Responsive Website Template | Block</title>
   </head>
   <body>
      @@include("partials/navbar.html",{ "classList": " navbar-light w-100 transparent" })
      <main>
         <!--hero section start-->
         <section class="right-slant-shape bg-primary pb-10 pt-4" data-cue="fadeIn">
            <div class="container">
               <div class="row">
                  <div class="col-lg-8 offset-lg-2 col-md-12 position-relaive">
                     <div class="text-white-stable text-center position-relaive my-lg-8 my-6" data-cue="zoomIn">
                        <span class="fw-medium fs-4">Hi there, my name is</span>
                        <h1 class="text-white-stable display-3 mt-3 mb-3"><PERSON></h1>
                        <p class="mb-0 pb-8 px-lg-7 text-opacity-75 lead">
                           I'm the VP of Developer Experience at Vercel where my team helps developers build a faster web. I'm an advisor and investor in early stage startups.
                        </p>
                     </div>
                  </div>
               </div>
            </div>
            <div class="position-absolute z-1 w-100 text-center top-25 mt-n8" data-cue="zoomIn">
               <img src="./assets/images/perosnal-portfolio/personal-profile-img.jpg" alt="avatar" class="rounded-circle shadow-sm border border-white border-4" />
            </div>
         </section>
         <!--hero section end-->

         <!--Portfolio start-->
         <section class="my-xl-10 py-xl-9 py-10" data-cue="fadeIn">
            <div class="container">
               <div class="row">
                  <div class="col-xl-6 offset-xl-3 col-md-12" data-cue="fadeIn">
                     <div class="text-center mb-lg-7 mb-5">
                        <small class="text-uppercase ls-md fw-semibold text-body-tertiary">Portfolio</small>
                        <h2 class="mt-5 px-lg-10 px-6">
                           Handful of items from
                           <span class="text-primary">my portfolio.</span>
                        </h2>
                        <p class="mb-0">
                           Here are a handful of items from my portfolio over the
                           <span class="text-dark">last 13 years.</span>
                        </p>
                     </div>
                  </div>
               </div>
               <div class="table-responsive-xl pb-5">
                  <div class="row flex-nowrap">
                     <div class="col-lg-4 col-md-6" data-cue="fadeIn">
                        <a href="#!">
                           <div class="card card-lift">
                              <div class="card-body pb-0">
                                 <div class="mb-6">
                                    <h3 class="h4 mb-4">JAMstack & Headless web development agency</h3>
                                    <span class="badge bg-light-subtle border border-light-subtle text-light-emphasis rounded-2">Figma</span>
                                    <span class="badge bg-light-subtle border border-light-subtle text-light-emphasis rounded-2">Bootstrap</span>
                                 </div>
                                 <img src="./assets/images/perosnal-portfolio/personal-portfolio-img-3.jpg" alt="portfolio" class="img-fluid rounded-top shadow-sm" />
                              </div>
                           </div>
                        </a>
                     </div>
                     <div class="col-lg-4 col-md-6" data-cue="fadeIn">
                        <a href="#!">
                           <div class="card card-lift">
                              <div class="card-body pb-0">
                                 <div class="mb-6">
                                    <h3 class="h4 mb-4">Banking and Finance landing page design</h3>
                                    <span class="badge bg-light-subtle border border-light-subtle text-light-emphasis rounded-2">UI Design</span>
                                    <span class="badge bg-light-subtle border border-light-subtle text-light-emphasis rounded-2">Next.js</span>
                                 </div>

                                 <img src="./assets/images/perosnal-portfolio/personal-portfolio-img-2.jpg" alt="portfolio" class="img-fluid rounded-top shadow-sm" />
                              </div>
                           </div>
                        </a>
                     </div>
                     <div class="col-lg-4 col-md-6" data-cue="fadeIn">
                        <a href="#!">
                           <div class="card card-lift">
                              <div class="card-body pb-0">
                                 <div class="mb-6">
                                    <h3 class="h4 mb-4">Accounting software web app development</h3>
                                    <span class="badge bg-light-subtle border border-light-subtle text-light-emphasis rounded-2">Web Development</span>
                                    <span class="badge bg-light-subtle border border-light-subtle text-light-emphasis rounded-2">React JS</span>
                                 </div>
                                 <img src="./assets/images/perosnal-portfolio/personal-portfolio-img-1.jpg" alt="portfolio" class="img-fluid rounded-top shadow-sm" />
                              </div>
                           </div>
                        </a>
                     </div>
                  </div>
               </div>
            </div>
         </section>
         <!--Portfolio end-->

         <!--My skill start-->
         <section class="my-xl-10 my-5" data-cue="fadeIn">
            <div class="container">
               <div class="row align-items-center">
                  <div class="col-lg-4 col-md-8 col-12">
                     <div class="mb-6 mb-lg-0" data-cue="zoomIn">
                        <small class="text-uppercase fw-bold ls-md text-body-tertiary">MY SKILLS</small>

                        <h2 class="my-3">
                           My areas of
                           <span class="text-primary">expertise</span>
                        </h2>
                        <p class="mb-5">Nullam quis risus eget urna mollis ornare vel eu leo. Maecenas faucibus mollis elit interdum. Duis mollis, ligula magna mollis.</p>
                        <a href="#!" class="btn btn-primary">View More Details</a>
                     </div>
                  </div>
                  <div class="col-lg-7 offset-lg-1 col-12">
                     <div class="row">
                        <div class="col-md-6">
                           <div class="card bg-danger bg-opacity-10 border-0 mb-5" data-cue="fadeIn" data-delay="500">
                              <div class="card-body">
                                 <div class="mb-4">
                                    <img src="./assets/images/personal-svg/html5.svg" alt="html" />
                                 </div>
                                 <div class="mb-4">
                                    <h3>HTML & CSS</h3>
                                    <p class="mb-0">Nulla vitae elit libero, a pharetra auguc id elit non mi porta gravida.</p>
                                 </div>

                                 <a href="#!" class="icon-link icon-link-hover link-danger">
                                    Link Text
                                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" class="bi bi-arrow-right" viewBox="0 0 16 16">
                                       <path
                                          fill-rule="evenodd"
                                          d="M1 8a.5.5 0 0 1 .5-.5h11.793l-3.147-3.146a.5.5 0 0 1 .708-.708l4 4a.5.5 0 0 1 0 .708l-4 4a.5.5 0 0 1-.708-.708L13.293 8.5H1.5A.5.5 0 0 1 1 8z"></path>
                                    </svg>
                                 </a>
                              </div>
                           </div>
                           <div class="card bg-warning bg-opacity-10 border-0 mb-5 mb-lg-0" data-cue="fadeIn" data-delay="1000">
                              <div class="card-body">
                                 <div class="mb-4">
                                    <img src="./assets/images/personal-svg/figma.svg" alt="html" />
                                 </div>
                                 <div class="mb-4">
                                    <h3>Figma UI Design</h3>
                                    <p class="mb-0">Nulla vitae elit libero, a pharetra auguc id elit non mi porta gravida.</p>
                                 </div>

                                 <a href="#!" class="icon-link icon-link-hover link-warning">
                                    Link Text
                                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" class="bi bi-arrow-right" viewBox="0 0 16 16">
                                       <path
                                          fill-rule="evenodd"
                                          d="M1 8a.5.5 0 0 1 .5-.5h11.793l-3.147-3.146a.5.5 0 0 1 .708-.708l4 4a.5.5 0 0 1 0 .708l-4 4a.5.5 0 0 1-.708-.708L13.293 8.5H1.5A.5.5 0 0 1 1 8z"></path>
                                    </svg>
                                 </a>
                              </div>
                           </div>
                        </div>
                        <div class="col-md-6 mt-md-5">
                           <div class="card bg-info bg-opacity-10 border-0 mb-5" data-cue="fadeIn" data-delay="1500">
                              <div class="card-body">
                                 <div class="mb-4">
                                    <img src="./assets/images/personal-svg/react.svg" alt="html" />
                                 </div>
                                 <div class="mb-4">
                                    <h3>React</h3>
                                    <p class="mb-0">Nulla vitae elit libero, a pharetra auguc id elit non mi porta gravida.</p>
                                 </div>

                                 <a href="#!" class="icon-link icon-link-hover link-info">
                                    Link Text
                                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" class="bi bi-arrow-right" viewBox="0 0 16 16">
                                       <path
                                          fill-rule="evenodd"
                                          d="M1 8a.5.5 0 0 1 .5-.5h11.793l-3.147-3.146a.5.5 0 0 1 .708-.708l4 4a.5.5 0 0 1 0 .708l-4 4a.5.5 0 0 1-.708-.708L13.293 8.5H1.5A.5.5 0 0 1 1 8z"></path>
                                    </svg>
                                 </a>
                              </div>
                           </div>
                           <div class="card bg-success bg-opacity-10 border-0 mb-5 mb-lg-0" data-cue="fadeIn" data-delay="2000">
                              <div class="card-body">
                                 <div class="mb-4">
                                    <img src="./assets/images/personal-svg/nodejs.svg" alt="html" />
                                 </div>
                                 <div class="mb-4">
                                    <h3>Node.js</h3>
                                    <p class="mb-0">Nulla vitae elit libero, a pharetra auguc id elit non mi porta gravida.</p>
                                 </div>

                                 <a href="#!" class="icon-link icon-link-hover link-success">
                                    Link Text
                                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" class="bi bi-arrow-right" viewBox="0 0 16 16">
                                       <path
                                          fill-rule="evenodd"
                                          d="M1 8a.5.5 0 0 1 .5-.5h11.793l-3.147-3.146a.5.5 0 0 1 .708-.708l4 4a.5.5 0 0 1 0 .708l-4 4a.5.5 0 0 1-.708-.708L13.293 8.5H1.5A.5.5 0 0 1 1 8z"></path>
                                    </svg>
                                 </a>
                              </div>
                           </div>
                        </div>
                     </div>
                  </div>
               </div>
            </div>
         </section>
         <!--My skill end-->

         <!--About me start-->
         <section class="my-lg-9 my-5" data-cue="fadeIn">
            <div class="container">
               <div class="row mb-lg-9 mb-5">
                  <div class="col-xl-10 offset-xl-1 col-md-12">
                     <div class="row">
                        <div class="col-lg-6 offset-lg-3 col-md-12" data-cue="zoomIn">
                           <div class="text-lg-center mb-5">
                              <small class="text-uppercase ls-md fw-semibold text-body-tertiary">ABOUT ME</small>
                              <h2 class="mt-3">
                                 Hey, I'm Lee. Most folks know me as
                                 <span class="text-primary">leerob</span>
                                 online.
                              </h2>
                           </div>
                        </div>
                     </div>
                     <div class="row" data-cue="zoomIn">
                        <div class="col-md-6 col-12">
                           <p class="mb-4 mb-md-0S">
                              I'm currently the VP of Developer Experience at Vercel, where I lead our Developer Relations and Documentation teams. I focus on educating and growing the Vercel and
                              Next.js communities.
                           </p>
                        </div>
                        <div class="col-md-6 col-12">
                           <p class="mb-0">I love building for the web. From something as simple as a single HTML file – all the way to large Next.js applications. The web is incredible.</p>
                        </div>
                        <div class="text-lg-center mt-5">
                           <a href="#!" class="btn btn-primary">More about me</a>
                        </div>
                     </div>
                  </div>
               </div>
               <div class="row">
                  <div class="col-lg-4 col-md-12 col-12" data-cue="zoomIn">
                     <div class="mb-5 mb-lg-0 text-center text-lg-start px-md-5">
                        <h2 class="mb-3">My process of Engagement</h2>
                        <p class="mb-0">Vivamus sagittis lacus vel augue laoreet rutrum faucibus dolor auctor. Fusce dapibus, tellus ac cursus. Aenean eu leo quam.</p>
                     </div>
                  </div>
                  <div class="col-lg-8">
                     <div class="row">
                        <div class="col-md-6 col-12" data-cue="fadeIn">
                           <div class="mb-lg-8 mb-5 text-center text-lg-start px-3 px-lg-0">
                              <div class="mb-3">
                                 <img src="./assets/images/process-icon/LightbulbFilament.svg" alt="bulb" />
                              </div>

                              <h3 class="h4">Ideation</h3>
                              <p class="mb-0 pe-lg-4">We craft a plan aligned with your business objectives, submit a comprehensive proposal.</p>
                           </div>
                        </div>
                        <div class="col-md-6 col-12" data-cue="fadeIn">
                           <div class="mb-lg-8 mb-5 text-center text-lg-start px-3 px-lg-0">
                              <div class="mb-3">
                                 <img src="./assets/images/process-icon/PencilCircle.svg" alt="bulb" />
                              </div>

                              <h3 class="h4">Design</h3>
                              <p class="mb-0">We design the Idea in Figma and seek approval. We offer 2 free design iterations.</p>
                           </div>
                        </div>
                        <div class="col-md-6 col-12" data-cue="fadeIn">
                           <div class="mb-lg-8 mb-5 text-center text-lg-start px-3 px-lg-0">
                              <div class="mb-3">
                                 <img src="./assets/images/process-icon/BracketsCurly.svg" alt="bulb" />
                              </div>

                              <h3 class="h4">Development</h3>
                              <p class="mb-0 pe-lg-4">Agile Development: Sprints, Milestones, and weekly Progress Tracking.</p>
                           </div>
                        </div>
                        <div class="col-md-6 col-12" data-cue="fadeIn">
                           <div class="mb-lg-8 mb-5 text-center text-lg-start px-3 px-lg-0">
                              <div class="mb-3">
                                 <img src="./assets/images/process-icon/Rocket.svg" alt="bulb" />
                              </div>

                              <h3 class="h4">Delivery</h3>
                              <p class="mb-0 pe-lg-4">Complete Project Delivery with Complimentary One Month Maintenance.</p>
                           </div>
                        </div>
                     </div>
                  </div>
               </div>
            </div>
         </section>
         <!--About me end-->

         <!--Testimonial start-->
         <section class="py-lg-9 py-5 bg-primary-blend-gradient text-dark" data-cue="fadeIn">
            <div class="container">
               <div class="row align-items-center mb-lg-7 mb-5" data-cue="zoomIn">
                  <div class="col-xl-6 col-lg-5 col-md-12">
                     <div class="mb-4 mb-lg-0 text-center text-lg-start">
                        <small class="text-uppercase ls-md fw-semibold">TESTIMONIAL</small>
                        <h2 class="mt-4">
                           I am proud of
                           <span class="text-primary">my results</span>
                        </h2>
                        <p class="mb-0">I bring solutions to make life easier for our customers.</p>
                     </div>
                  </div>
                  <div class="col-xl-6 col-lg-7 col-md-12">
                     <div class="row gy-4">
                        <div class="col-md-4">
                           <div class="text-center text-lg-start">
                              <h2 class="mb-1">236</h2>
                              <span>Awards Won</span>
                           </div>
                        </div>
                        <div class="col-md-4">
                           <div class="text-center text-lg-start">
                              <h2 class="mb-1">200+</h2>
                              <span>Completed Projects</span>
                           </div>
                        </div>
                        <div class="col-md-4">
                           <div class="text-center text-lg-start">
                              <h2 class="mb-1">10Y</h2>
                              <span>Customer Satisfaction</span>
                           </div>
                        </div>
                     </div>
                  </div>
               </div>
               <div class="row">
                  <div class="col-lg-3 col-md-6 mb-4 mb-lg-0" data-cue="fadeIn">
                     <div class="card shadow-sm h-100 border-0">
                        <div class="card-body">
                           <p class="mb-4">“Laborum quis quam. Dolorum et ut quod quia. Voluptas numquam delectus nihil. Aut enim doloremque et ipsam.”</p>
                           <h5 class="mb-0">Jitu Chauhan</h5>
                           <small>Creative Director - Design agency</small>
                        </div>
                     </div>
                  </div>
                  <div class="col-lg-3 col-md-6 mb-4 mb-lg-0" data-cue="fadeIn">
                     <div class="card shadow-sm h-100 border-0">
                        <div class="card-body">
                           <p class="mb-4">“Dolorum et ut quod quia laborum quis quam oluptas numquam delectus nihil ultrices diam ut enim doloremque et ipsam.”</p>
                           <h5 class="mb-0">Anita Parmar</h5>
                           <small>Sales Specialist</small>
                        </div>
                     </div>
                  </div>
                  <div class="col-lg-3 col-md-6 mb-4 mb-lg-0" data-cue="fadeIn">
                     <div class="card shadow-sm h-100 border-0">
                        <div class="card-body">
                           <p class="mb-4">“Suspendisse facilisis leo at sapien finibus rutrum. Vivamus consectetur viverra erat. Nullam pretium mauris quis ultricies ultrices.”</p>
                           <h5 class="mb-0">Sandeep Chauhan</h5>
                           <small>Marketing Specialist</small>
                        </div>
                     </div>
                  </div>
                  <div class="col-lg-3 col-md-6 mb-4 mb-lg-0" data-cue="fadeIn">
                     <div class="card shadow-sm h-100 border-0">
                        <div class="card-body">
                           <p class="mb-4">“Phasellus dapibus nibh eu libero pharetra, interdum accu msan nunc purus ferm entum sagittis vestibulum nanatibus et magnis.”</p>
                           <h5 class="mb-0">Manasvi Suthar</h5>
                           <small>Creative Art Director</small>
                        </div>
                     </div>
                  </div>
               </div>
            </div>
         </section>
         <!--Testimonial end-->

         <!--Brand help start-->
         <section class="my-lg-9 py-5">
            <div class="container">
               <div class="row">
                  <div class="col-lg-10 offset-lg-1">
                     <div class="text-center mb-4 mb-lg-7">
                        <small class="text-uppercase ls-md fw-bold text-body-tertiary">Brands I helped</small>
                     </div>
                     <div
                        class="swiper-container swiper"
                        id="swiper-1"
                        data-pagination-type=""
                        data-speed="400"
                        data-space-between="100"
                        data-pagination="true"
                        data-navigation="false"
                        data-autoplay="true"
                        data-autoplay-delay="3000"
                        data-breakpoints='{"480": {"slidesPerView": 2}, "768": {"slidesPerView": 3}, "1024": {"slidesPerView": 5}}'>
                        <div class="swiper-wrapper pb-6">
                           <div class="swiper-slide">
                              <figure class="text-center">
                                 <img src="@@webRoot/assets/images/client-logo/clients-logo-1.svg" alt="logo" />
                              </figure>
                           </div>
                           <div class="swiper-slide">
                              <figure class="text-center">
                                 <img src="@@webRoot/assets/images/client-logo/clients-logo-2.svg" alt="logo" />
                              </figure>
                           </div>
                           <div class="swiper-slide">
                              <figure class="text-center">
                                 <img src="@@webRoot/assets/images/client-logo/clients-logo-3.svg" alt="logo" />
                              </figure>
                           </div>
                           <div class="swiper-slide">
                              <figure class="text-center">
                                 <img src="@@webRoot/assets/images/client-logo/clients-logo-4.svg" alt="logo" />
                              </figure>
                           </div>
                           <div class="swiper-slide">
                              <figure class="text-center">
                                 <img src="@@webRoot/assets/images/client-logo/clients-logo-5.svg" alt="logo" />
                              </figure>
                           </div>
                           <!-- Add more slides as needed -->
                        </div>
                        <!-- Add Pagination -->
                        <div class="swiper-pagination"></div>
                        <!-- Add Navigation -->
                        <div class="swiper-navigation">
                           <div class="swiper-button-next"></div>
                           <div class="swiper-button-prev"></div>
                        </div>
                     </div>
                  </div>
               </div>
            </div>
         </section>
         <!--Brand help end-->

         <!--Working together start-->
         <section class="my-lg-9 my-5">
            <div class="container">
               <div class="row">
                  <div class="col-lg-10 offset-lg-1 col-md-12">
                     <div class="row g-lg-10 gy-5 align-items-center">
                        <div class="col-md-6" data-cue="zoomIn">
                           <div class="card bg-light">
                              <div class="card-body p-5">
                                 <h3 class="mb-4">Interested in working together? Let’s talk. Just Say Hi!</h3>

                                 <form class="needs-validation" novalidate>
                                    <div class="mb-3">
                                       <label for="YourNameInput" class="form-label">What’s your name</label>
                                       <input type="text" class="form-control" id="YourNameInput" placeholder="Name" required="" />
                                       <div class="invalid-feedback">Please enter yourname.</div>
                                    </div>
                                    <div class="mb-3">
                                       <label for="yourEmailInput" class="form-label">Email Address</label>
                                       <input type="email" class="form-control" id="yourEmailInput" placeholder="Email" required="" />
                                       <div class="invalid-feedback">Please enter email.</div>
                                    </div>
                                    <div class="mb-3">
                                       <label for="messageTextarea" class="form-label">Describe your project</label>
                                       <textarea class="form-control" id="messageTextarea" placeholder="Write the information about your project." rows="4" required></textarea>
                                       <div class="invalid-feedback">Please enter a message.</div>
                                    </div>
                                    <button class="btn btn-primary" type="submit">Contact me</button>
                                 </form>
                              </div>
                           </div>
                        </div>
                        <div class="col-md-6" data-cue="zoomIn">
                           <div class="mb-7 text-center text-lg-start">
                              <div class="mb-3">
                                 <img src="./assets/images/avatar/avatar-1.jpg" alt="avatar" class="avatar avatar-xl rounded-circle" />
                              </div>

                              <h3 class="mb-0">John Carter</h3>
                              <small>Head of Engineering at Google</small>
                           </div>
                           <div class="d-flex mb-4">
                              <div>
                                 <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-geo-alt-fill" viewBox="0 0 16 16">
                                    <path d="M8 16s6-5.686 6-10A6 6 0 0 0 2 6c0 4.314 6 10 6 10zm0-7a3 3 0 1 1 0-6 3 3 0 0 1 0 6z" />
                                 </svg>
                              </div>
                              <div class="ms-2">
                                 <h5 class="mb-0">Address:</h5>
                                 <small>1802 Ruckman RoadOklahoma City, OK 73116</small>
                              </div>
                           </div>
                           <div class="d-flex mb-4">
                              <div>
                                 <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-telephone" viewBox="0 0 16 16">
                                    <path
                                       d="M3.654 1.328a.678.678 0 0 0-1.015-.063L1.605 2.3c-.483.484-.661 1.169-.45 1.77a17.568 17.568 0 0 0 4.168 6.608 17.569 17.569 0 0 0 6.608 4.168c.601.211 1.286.033 1.77-.45l1.034-1.034a.678.678 0 0 0-.063-1.015l-2.307-1.794a.678.678 0 0 0-.58-.122l-2.19.547a1.745 1.745 0 0 1-1.657-.459L5.482 8.062a1.745 1.745 0 0 1-.46-1.657l.548-2.19a.678.678 0 0 0-.122-.58L3.654 1.328zM1.884.511a1.745 1.745 0 0 1 2.612.163L6.29 2.98c.329.423.445.974.315 1.494l-.547 2.19a.678.678 0 0 0 .178.643l2.457 2.457a.678.678 0 0 0 .644.178l2.189-.547a1.745 1.745 0 0 1 1.494.315l2.306 1.794c.829.645.905 1.87.163 2.611l-1.034 1.034c-.74.74-1.846 1.065-2.877.702a18.634 18.634 0 0 1-7.01-4.42 18.634 18.634 0 0 1-4.42-7.009c-.362-1.03-.037-2.137.703-2.877L1.885.511z" />
                                 </svg>
                              </div>
                              <div class="ms-2">
                                 <h5 class="mb-0">Phone:</h5>
                                 <small>
                                    00 (123) 456 78 90
                                    <span class="ms-3">00 (987) 654 32 10</span>
                                 </small>
                              </div>
                           </div>
                           <div class="d-flex mb-4">
                              <div>
                                 <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-envelope-check" viewBox="0 0 16 16">
                                    <path
                                       d="M2 2a2 2 0 0 0-2 2v8.01A2 2 0 0 0 2 14h5.5a.5.5 0 0 0 0-1H2a1 1 0 0 1-.966-.741l5.64-3.471L8 9.583l7-4.2V8.5a.5.5 0 0 0 1 0V4a2 2 0 0 0-2-2H2Zm3.708 6.208L1 11.105V5.383l4.708 2.825ZM1 4.217V4a1 1 0 0 1 1-1h12a1 1 0 0 1 1 1v.217l-7 4.2-7-4.2Z" />
                                    <path
                                       d="M16 12.5a3.5 3.5 0 1 1-7 0 3.5 3.5 0 0 1 7 0Zm-1.993-1.679a.5.5 0 0 0-.686.172l-1.17 1.95-.547-.547a.5.5 0 0 0-.708.708l.774.773a.75.75 0 0 0 1.174-.144l1.335-2.226a.5.5 0 0 0-.172-.686Z" />
                                 </svg>
                              </div>
                              <div class="ms-2">
                                 <h5 class="mb-0">E-mail:</h5>

                                 <small>
                                    <a href="#!" class="text-reset"><EMAIL></a>
                                    <span class="ms-lg-3"><a href="#!" class="text-reset"><EMAIL></a></span>
                                 </small>
                              </div>
                           </div>
                           <div>
                              <a href="#!" class="text-reset btn btn-instagram btn-icon">
                                 <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-instagram" viewBox="0 0 16 16">
                                    <path
                                       d="M8 0C5.829 0 5.556.01 4.703.048 3.85.088 3.269.222 2.76.42a3.917 3.917 0 0 0-1.417.923A3.927 3.927 0 0 0 .42 2.76C.222 3.268.087 3.85.048 4.7.01 5.555 0 5.827 0 8.001c0 2.172.01 2.444.048 3.297.04.852.174 1.433.372 1.942.205.526.478.972.923 1.417.444.445.89.719 1.416.923.51.198 1.09.333 1.942.372C5.555 15.99 5.827 16 8 16s2.444-.01 3.298-.048c.851-.04 1.434-.174 1.943-.372a3.916 3.916 0 0 0 1.416-.923c.445-.445.718-.891.923-1.417.197-.509.332-1.09.372-1.942C15.99 10.445 16 10.173 16 8s-.01-2.445-.048-3.299c-.04-.851-.175-1.433-.372-1.941a3.926 3.926 0 0 0-.923-1.417A3.911 3.911 0 0 0 13.24.42c-.51-.198-1.092-.333-1.943-.372C10.443.01 10.172 0 7.998 0h.003zm-.717 1.442h.718c2.136 0 2.389.007 3.232.046.78.035 1.204.166 1.486.275.373.145.64.319.92.599.28.28.453.546.598.92.11.281.24.705.275 1.485.039.843.047 1.096.047 3.231s-.008 2.389-.047 3.232c-.035.78-.166 1.203-.275 1.485a2.47 2.47 0 0 1-.599.919c-.28.28-.546.453-.92.598-.28.11-.704.24-1.485.276-.843.038-1.096.047-3.232.047s-2.39-.009-3.233-.047c-.78-.036-1.203-.166-1.485-.276a2.478 2.478 0 0 1-.92-.598 2.48 2.48 0 0 1-.6-.92c-.109-.281-.24-.705-.275-1.485-.038-.843-.046-1.096-.046-3.233 0-2.136.008-2.388.046-3.231.036-.78.166-1.204.276-1.486.145-.373.319-.64.599-.92.28-.28.546-.453.92-.598.282-.11.705-.24 1.485-.276.738-.034 1.024-.044 2.515-.045v.002zm4.988 1.328a.96.96 0 1 0 0 1.92.96.96 0 0 0 0-1.92zm-4.27 1.122a4.109 4.109 0 1 0 0 8.217 4.109 4.109 0 0 0 0-8.217zm0 1.441a2.667 2.667 0 1 1 0 5.334 2.667 2.667 0 0 1 0-5.334z" />
                                 </svg>
                              </a>
                              <a href="#!" class="text-reset btn btn-twitter btn-icon">
                                 <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-twitter" viewBox="0 0 16 16">
                                    <path
                                       d="M5.026 15c6.038 0 9.341-5.003 9.341-9.334 0-.14 0-.282-.006-.422A6.685 6.685 0 0 0 16 3.542a6.658 6.658 0 0 1-1.889.518 3.301 3.301 0 0 0 1.447-1.817 6.533 6.533 0 0 1-2.087.793A3.286 3.286 0 0 0 7.875 6.03a9.325 9.325 0 0 1-6.767-3.429 3.289 3.289 0 0 0 1.018 4.382A3.323 3.323 0 0 1 .64 6.575v.045a3.288 3.288 0 0 0 2.632 3.218 3.203 3.203 0 0 1-.865.115 3.23 3.23 0 0 1-.614-.057 3.283 3.283 0 0 0 3.067 2.277A6.588 6.588 0 0 1 .78 13.58a6.32 6.32 0 0 1-.78-.045A9.344 9.344 0 0 0 5.026 15z" />
                                 </svg>
                              </a>
                              <a href="#!" class="text-reset btn btn-youtube btn-icon">
                                 <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-youtube" viewBox="0 0 16 16">
                                    <path
                                       d="M8.051 1.999h.089c.822.003 4.987.033 6.11.335a2.01 2.01 0 0 1 1.415 1.42c.101.38.172.883.22 1.402l.01.104.022.26.008.104c.065.914.073 1.77.074 1.957v.075c-.001.194-.01 1.108-.082 2.06l-.008.105-.009.104c-.05.572-.124 1.14-.235 1.558a2.007 2.007 0 0 1-1.415 1.42c-1.16.312-5.569.334-6.18.335h-.142c-.309 0-1.587-.006-2.927-.052l-.17-.006-.087-.004-.171-.007-.171-.007c-1.11-.049-2.167-.128-2.654-.26a2.007 2.007 0 0 1-1.415-1.419c-.111-.417-.185-.986-.235-1.558L.09 9.82l-.008-.104A31.4 31.4 0 0 1 0 7.68v-.123c.002-.215.01-.958.064-1.778l.007-.103.003-.052.008-.104.022-.26.01-.104c.048-.519.119-1.023.22-1.402a2.007 2.007 0 0 1 1.415-1.42c.487-.13 1.544-.21 2.654-.26l.17-.007.172-.006.086-.003.171-.007A99.788 99.788 0 0 1 7.858 2h.193zM6.4 5.209v4.818l4.157-2.408L6.4 5.209z" />
                                 </svg>
                              </a>
                              <a href="#!" class="text-reset btn btn-linkedin btn-icon">
                                 <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-linkedin" viewBox="0 0 16 16">
                                    <path
                                       d="M0 1.146C0 .513.526 0 1.175 0h13.65C15.474 0 16 .513 16 1.146v13.708c0 .633-.526 1.146-1.175 1.146H1.175C.526 16 0 15.487 0 14.854V1.146zm4.943 12.248V6.169H2.542v7.225h2.401zm-1.2-8.212c.837 0 1.358-.554 1.358-1.248-.015-.709-.52-1.248-1.342-1.248-.822 0-1.359.54-1.359 1.248 0 .694.521 1.248 1.327 1.248h.016zm4.908 8.212V9.359c0-.216.016-.432.08-.586.173-.431.568-.878 1.232-.878.869 0 1.216.662 1.216 1.634v3.865h2.401V9.25c0-2.22-1.184-3.252-2.764-3.252-1.274 0-1.845.7-2.165 1.193v.025h-.016a5.54 5.54 0 0 1 .016-.025V6.169h-2.4c.03.678 0 7.225 0 7.225h2.4z" />
                                 </svg>
                              </a>
                              <a href="#!" class="text-reset btn btn-instagram btn-icon">
                                 <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-instagram" viewBox="0 0 16 16">
                                    <path
                                       d="M8 0C5.829 0 5.556.01 4.703.048 3.85.088 3.269.222 2.76.42a3.917 3.917 0 0 0-1.417.923A3.927 3.927 0 0 0 .42 2.76C.222 3.268.087 3.85.048 4.7.01 5.555 0 5.827 0 8.001c0 2.172.01 2.444.048 3.297.04.852.174 1.433.372 1.942.205.526.478.972.923 1.417.444.445.89.719 1.416.923.51.198 1.09.333 1.942.372C5.555 15.99 5.827 16 8 16s2.444-.01 3.298-.048c.851-.04 1.434-.174 1.943-.372a3.916 3.916 0 0 0 1.416-.923c.445-.445.718-.891.923-1.417.197-.509.332-1.09.372-1.942C15.99 10.445 16 10.173 16 8s-.01-2.445-.048-3.299c-.04-.851-.175-1.433-.372-1.941a3.926 3.926 0 0 0-.923-1.417A3.911 3.911 0 0 0 13.24.42c-.51-.198-1.092-.333-1.943-.372C10.443.01 10.172 0 7.998 0h.003zm-.717 1.442h.718c2.136 0 2.389.007 3.232.046.78.035 1.204.166 1.486.275.373.145.64.319.92.599.28.28.453.546.598.92.11.281.24.705.275 1.485.039.843.047 1.096.047 3.231s-.008 2.389-.047 3.232c-.035.78-.166 1.203-.275 1.485a2.47 2.47 0 0 1-.599.919c-.28.28-.546.453-.92.598-.28.11-.704.24-1.485.276-.843.038-1.096.047-3.232.047s-2.39-.009-3.233-.047c-.78-.036-1.203-.166-1.485-.276a2.478 2.478 0 0 1-.92-.598 2.48 2.48 0 0 1-.6-.92c-.109-.281-.24-.705-.275-1.485-.038-.843-.046-1.096-.046-3.233 0-2.136.008-2.388.046-3.231.036-.78.166-1.204.276-1.486.145-.373.319-.64.599-.92.28-.28.546-.453.92-.598.282-.11.705-.24 1.485-.276.738-.034 1.024-.044 2.515-.045v.002zm4.988 1.328a.96.96 0 1 0 0 1.92.96.96 0 0 0 0-1.92zm-4.27 1.122a4.109 4.109 0 1 0 0 8.217 4.109 4.109 0 0 0 0-8.217zm0 1.441a2.667 2.667 0 1 1 0 5.334 2.667 2.667 0 0 1 0-5.334z" />
                                 </svg>
                              </a>
                           </div>
                        </div>
                     </div>
                  </div>
               </div>
            </div>
         </section>
         <!--Working together end-->
      </main>

      @@include("partials/footer.html") @@include("partials/btn-scroll-top.html") @@include("partials/scripts.html")
      <script src="@@webRoot/node_modules/swiper/swiper-bundle.min.js"></script>
      <script src="@@webRoot/assets/js/vendors/swiper.js"></script>
      <script src="@@webRoot/node_modules/scrollcue/scrollCue.min.js"></script>
      <script src="@@webRoot/assets/js/vendors/scrollcue.js"></script>
   </body>
</html>
