<!doctype html>
<html lang="en">
   <head>
      <link rel="stylesheet" href="./assets/libs/swiper/swiper-bundle.min.css" />
      <!-- Required meta tags -->
<meta charset="utf-8" />
<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
 <!-- Favicon icon-->
<link rel="apple-touch-icon" sizes="180x180" href="./assets/images/favicon/apple-touch-icon.png" />
<link rel="icon" type="image/png" sizes="32x32" href="./assets/images/favicon/favicon-32x32.png" />
<link rel="icon" type="image/png" sizes="16x16" href="./assets/images/favicon/favicon-16x16.png" />
<link rel="manifest" href="./assets/images/favicon/site.webmanifest" />
<link rel="mask-icon" href="./assets/images/favicon/block-safari-pinned-tab.svg" color="#8b3dff" />
<link rel="shortcut icon" href="./assets/images/favicon/favicon.ico" />
<meta name="msapplication-TileColor" content="#8b3dff" />
<meta name="msapplication-config" content="./assets/images/favicon/tile.xml" />

<!-- Color modes -->
<script src="./assets/js/vendors/color-modes.js"></script>

<!-- Libs CSS -->
<link href="./assets/libs/simplebar/dist/simplebar.min.css" rel="stylesheet" />
<link href="./assets/libs/bootstrap-icons/font/bootstrap-icons.min.css" rel="stylesheet" />

<!-- Scroll Cue -->
<link rel="stylesheet" href="./assets/libs/scrollcue/scrollCue.css" />

<!-- Box icons -->
<link rel="stylesheet" href="./assets/fonts/css/boxicons.min.css" />

<!-- Theme CSS -->
<link rel="stylesheet" href="./assets/css/theme.min.css">


      <title>Mobile App Showcase - Responsive Website Template | Block</title>
   </head>

   <body>
      <!-- Navbar -->
<header>
   <div class="container">
      <nav class="navbar navbar-expand-lg navbar-boxed mx-auto mt-lg-4">
         <a class="navbar-brand" href="./index.html"><img src="./assets/images/logo/logo.svg" alt /></a>
         <button
            class="navbar-toggler"
            type="button"
            data-bs-toggle="collapse"
            data-bs-target="#navbarSupportedContent"
            aria-controls="navbarSupportedContent"
            aria-expanded="false"
            aria-label="Toggle navigation">
            <i class="bi bi-list"></i>
         </button>

         <div class="collapse navbar-collapse" id="navbarSupportedContent">
            <ul class="navbar-nav mx-auto align-items-lg-center">
               <li class="nav-item">
                  <a class="nav-link" href="./home.html">Home</a>
               </li>
               <li class="nav-item">
                  <a class="nav-link" href="./scan-pay.html">Scan & Pay</a>
               </li>
               <li class="nav-item">
                  <a class="nav-link" href="./service.html">Services</a>
               </li>
               <li class="nav-item">
                  <a class="nav-link" href="./contact.html">Contact</a>
               </li>
               <li class="nav-item">
                  <a class="nav-link" href="./become-an-agent.html">Become An Agent</a>
               </li>
            </ul>
            <div class="mt-3 mt-lg-0 d-flex align-items-center">
               <a href="./signin.html" class="btn btn-light mx-2">Login</a>
               <a href="https://play.google.com/store/apps/details?id=com.qsoft.aidapay&hl=en&pli=1" class="btn btn-primary">Create account</a>
            </div>
         </div>
      </nav>
   </div>
</header>

      <main>
         <!--Hero section-->
         <section
            class="pt-lg-10"
            style="
               background: url(./assets/images/mobile-app/curvlines.svg), linear-gradient(111.42deg, #4a00b7 0.42%, #c13dff 81.76%, #c13dff 96.06%);
               background-position: center;
               background-repeat: no-repeat;
               background-size: cover;
            ">
            <div class="container">
               <div class="pt-5">
                  <div class="row">
                     <div class="col-12">
                        <div class="row align-items-center gy-5 px-lg-6 position-relative">
                           <div class="col-md-6 col-12" data-cue="slideInLeft">
                              <div class="d-flex flex-column gap-7">
                                 <div class="d-flex flex-column gap-3 mb-lg-10">
                                    <div class="d-flex flex-row align-items-center">
                                       <a href="#!" class="bg-opacity-50 text-bg-primary border border-primary badge px-3 py-2 fw-medium rounded-pill fs-6">
                                          <span class="">Read our $6M Series A announcement</span>
                                          <span class="ms-1">
                                             <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-arrow-right" viewBox="0 0 16 16">
                                                <path
                                                   fill-rule="evenodd"
                                                   d="M1 8a.5.5 0 0 1 .5-.5h11.793l-3.147-3.146a.5.5 0 0 1 .708-.708l4 4a.5.5 0 0 1 0 .708l-4 4a.5.5 0 0 1-.708-.708L13.293 8.5H1.5A.5.5 0 0 1 1 8"></path>
                                             </svg>
                                          </span>
                                       </a>
                                    </div>
                                    <div class="d-flex flex-column gap-5">
                                       <div class="d-flex flex-column gap-3">
                                          <h1 class="mb-0 text-white-stable display-4">Master your money with confidence</h1>
                                          <p class="mb-0 text-white-50 lead">Navigate your finances with confidence. Track spending, budgets, investments, net worth.</p>
                                       </div>
                                       <div class="d-flex flex-row align-items-center gap-2">
                                          <a href="#!"><img src="./assets/images/mobile-app/playstore.svg" alt="playstore" /></a>
                                          <a href="#!"><img src="./assets/images/mobile-app/appstore.svg" alt="appstore" /></a>
                                          <img src="./assets/images/mobile-app/qr-code.svg" alt="qr-code" class="icon-md icon-shape" />
                                       </div>
                                    </div>
                                 </div>
                                 <div class="d-flex flex-row flex-wrap gap-2 align-items-center justify-content-center justify-content-md-start lh-1 position-lg-absolute bottom-0 mb-lg-6">
                                    <span class="text-white-stable fs-6">Great</span>
                                    <div>
                                       <img src="./assets/images/mobile-app/star.svg" alt="star" />
                                       <img src="./assets/images/mobile-app/star.svg" alt="star" />
                                       <img src="./assets/images/mobile-app/star.svg" alt="star" />
                                       <img src="./assets/images/mobile-app/star.svg" alt="star" />
                                       <img src="./assets/images/mobile-app/star-light.svg" alt="star" />
                                    </div>
                                    <span class="text-white-stable">6,427&nbsp;reviews on</span>
                                    <span class="lh-1">
                                       <span>
                                          <svg width="82" height="20" viewBox="0 0 82 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                             <g clip-path="url(#clip0_2350_3858)">
                                                <path
                                                   d="M21.3386 7.14197H29.5601V8.66726H26.3274V17.2417H24.5498V8.66726H21.3314V7.14197H21.3386ZM29.2088 9.92883H30.7284V11.3401H30.7571C30.8073 11.1405 30.9004 10.9481 31.0366 10.7627C31.1728 10.5774 31.3377 10.3992 31.5312 10.2496C31.7247 10.0928 31.9398 9.97159 32.1763 9.87181C32.4129 9.77915 32.6566 9.72926 32.9003 9.72926C33.0866 9.72926 33.2228 9.73638 33.2945 9.74351C33.3662 9.75064 33.4378 9.76489 33.5167 9.77202V11.3258C33.402 11.3044 33.2873 11.2902 33.1655 11.2759C33.0436 11.2617 32.9289 11.2545 32.8142 11.2545C32.5419 11.2545 32.2838 11.3116 32.0401 11.4185C31.7964 11.5254 31.5886 11.6893 31.4094 11.896C31.2302 12.1098 31.0868 12.3664 30.9793 12.68C30.8718 12.9937 30.8216 13.35 30.8216 13.7563V17.2345H29.2017V9.92883L29.2088 9.92883ZM40.964 17.2417H39.3728V16.2224H39.3441C39.1434 16.5931 38.8495 16.8853 38.4553 17.1062C38.0611 17.3272 37.6597 17.4412 37.2511 17.4412C36.2835 17.4412 35.581 17.206 35.151 16.7285C34.7209 16.2509 34.5059 15.5311 34.5059 14.5688V9.92883H36.1258V14.412C36.1258 15.0535 36.2476 15.5097 36.4985 15.7734C36.7422 16.0371 37.0934 16.1725 37.5378 16.1725C37.8819 16.1725 38.1614 16.1226 38.3908 16.0157C38.6202 15.9088 38.8065 15.7734 38.9427 15.5952C39.0861 15.4241 39.1864 15.2103 39.2509 14.968C39.3155 14.7256 39.3441 14.4619 39.3441 14.1768V9.93595H40.964V17.2417ZM43.7237 14.8967C43.7738 15.3671 43.953 15.695 44.2612 15.8874C44.5766 16.0727 44.9493 16.1725 45.3866 16.1725C45.5371 16.1725 45.7091 16.1583 45.9027 16.1369C46.0962 16.1155 46.2826 16.0656 46.4474 16.0015C46.6194 15.9373 46.7556 15.8375 46.8703 15.7092C46.9778 15.5809 47.028 15.417 47.0208 15.2103C47.0137 15.0036 46.9348 14.8326 46.7915 14.7043C46.6481 14.5688 46.4689 14.4691 46.2467 14.3835C46.0245 14.3051 45.7736 14.2338 45.4869 14.1768C45.2002 14.1198 44.9135 14.0557 44.6196 13.9915C44.3186 13.9274 44.0247 13.8418 43.7452 13.7492C43.4656 13.6565 43.2147 13.5282 42.9925 13.3643C42.7703 13.2075 42.5911 13.0008 42.4621 12.7513C42.3259 12.5019 42.2614 12.1954 42.2614 11.8247C42.2614 11.4256 42.3618 11.0977 42.5553 10.8269C42.7488 10.556 42.9997 10.3422 43.2936 10.1783C43.5946 10.0144 43.9244 9.90032 44.2899 9.82904C44.6555 9.76489 45.0067 9.72926 45.3364 9.72926C45.7163 9.72926 46.0819 9.77202 46.4259 9.85042C46.77 9.92883 47.0854 10.0571 47.3649 10.2424C47.6444 10.4206 47.8738 10.6558 48.0602 10.9409C48.2465 11.226 48.3612 11.5753 48.4114 11.9815H46.7198C46.6409 11.5967 46.4689 11.3329 46.1894 11.2046C45.9098 11.0692 45.5873 11.0051 45.2289 11.0051C45.1142 11.0051 44.978 11.0122 44.8203 11.0336C44.6626 11.055 44.5193 11.0906 44.3759 11.1405C44.2397 11.1904 44.1251 11.2688 44.0247 11.3686C43.9315 11.4684 43.8813 11.5967 43.8813 11.7606C43.8813 11.9602 43.953 12.117 44.0892 12.2381C44.2254 12.3593 44.4046 12.4591 44.6268 12.5446C44.849 12.623 45.0999 12.6943 45.3866 12.7513C45.6733 12.8083 45.9672 12.8725 46.2682 12.9366C46.5621 13.0008 46.8488 13.0863 47.1355 13.179C47.4222 13.2716 47.6731 13.3999 47.8953 13.5639C48.1175 13.7278 48.2967 13.9274 48.4329 14.1697C48.5691 14.412 48.6408 14.7185 48.6408 15.0749C48.6408 15.5097 48.5404 15.8732 48.3397 16.1797C48.139 16.479 47.881 16.7285 47.5656 16.9138C47.2502 17.0991 46.8918 17.2417 46.5048 17.3272C46.1177 17.4127 45.7306 17.4555 45.3507 17.4555C44.8848 17.4555 44.4548 17.4056 44.0605 17.2987C43.6663 17.1918 43.3223 17.035 43.0355 16.8283C42.7488 16.6144 42.5195 16.3507 42.3546 16.0371C42.1897 15.7235 42.1037 15.3457 42.0894 14.911H43.7237V14.8967ZM49.0708 9.92883H50.2965V7.73355H51.9165V9.92883H53.3787V11.1334H51.9165V15.0393C51.9165 15.2103 51.9236 15.3529 51.938 15.4812C51.9523 15.6023 51.9881 15.7092 52.0383 15.7948C52.0885 15.8803 52.1673 15.9444 52.2748 15.9872C52.3824 16.03 52.5186 16.0514 52.7049 16.0514C52.8196 16.0514 52.9343 16.0514 53.049 16.0442C53.1637 16.0371 53.2783 16.0228 53.393 15.9943V17.2417C53.2138 17.263 53.0346 17.2773 52.8698 17.2987C52.6977 17.3201 52.5257 17.3272 52.3465 17.3272C51.9165 17.3272 51.5724 17.2844 51.3144 17.206C51.0563 17.1276 50.8485 17.0064 50.7051 16.8496C50.5546 16.6928 50.4614 16.5004 50.404 16.2652C50.3539 16.03 50.318 15.7591 50.3109 15.4598V11.1476H49.0852V9.92883H49.0708ZM54.5255 9.92883H56.0594V10.9195H56.0881C56.3175 10.4919 56.6329 10.1925 57.0414 10.0072C57.45 9.82191 57.8872 9.72925 58.3675 9.72925C58.9481 9.72925 59.4498 9.82904 59.8799 10.0357C60.31 10.2353 60.6684 10.5133 60.9551 10.8697C61.2418 11.226 61.4496 11.6394 61.593 12.1098C61.7364 12.5803 61.808 13.0863 61.808 13.6209C61.808 14.1127 61.7435 14.5902 61.6145 15.0464C61.4855 15.5097 61.2919 15.9159 61.0339 16.2723C60.7759 16.6287 60.4461 16.9067 60.0448 17.1205C59.6434 17.3343 59.1774 17.4412 58.6327 17.4412C58.3962 17.4412 58.1596 17.4198 57.9231 17.3771C57.6865 17.3343 57.4572 17.263 57.2421 17.1704C57.0271 17.0777 56.8192 16.9566 56.64 16.8069C56.4537 16.6572 56.3032 16.4861 56.1741 16.2937H56.1455V19.943H54.5255V9.92883ZM60.1881 13.5924C60.1881 13.2645 60.1451 12.9438 60.0591 12.6302C59.9731 12.3165 59.8441 12.0457 59.672 11.8034C59.5 11.561 59.285 11.3686 59.0341 11.226C58.776 11.0835 58.4822 11.0051 58.1525 11.0051C57.4715 11.0051 56.9554 11.2403 56.6114 11.7107C56.2673 12.1811 56.0953 12.8083 56.0953 13.5924C56.0953 13.963 56.1383 14.3051 56.2315 14.6187C56.3247 14.9323 56.4537 15.2032 56.64 15.4313C56.8192 15.6593 57.0343 15.8375 57.2851 15.9658C57.536 16.1013 57.8299 16.1654 58.1596 16.1654C58.5323 16.1654 58.8406 16.087 59.0986 15.9373C59.3566 15.7876 59.5645 15.5881 59.7294 15.3529C59.8942 15.1105 60.0161 14.8397 60.0878 14.5332C60.1523 14.2267 60.1881 13.9131 60.1881 13.5924ZM63.0481 7.14197H64.668V8.66726H63.0481V7.14197ZM63.0481 9.92883H64.668V17.2417H63.0481V9.92883ZM66.1159 7.14197H67.7358V17.2417H66.1159V7.14197ZM72.7031 17.4412C72.1153 17.4412 71.5921 17.3414 71.1334 17.149C70.6746 16.9566 70.2876 16.6857 69.965 16.3507C69.6496 16.0086 69.4059 15.6023 69.241 15.1319C69.0762 14.6615 68.9902 14.1412 68.9902 13.5781C68.9902 13.0222 69.0762 12.509 69.241 12.0386C69.4059 11.5682 69.6496 11.1619 69.965 10.8198C70.2804 10.4776 70.6746 10.2139 71.1334 10.0215C71.5921 9.82904 72.1153 9.72926 72.7031 9.72926C73.2909 9.72926 73.8141 9.82904 74.2729 10.0215C74.7316 10.2139 75.1186 10.4848 75.4412 10.8198C75.7566 11.1619 76.0003 11.5682 76.1651 12.0386C76.33 12.509 76.416 13.0222 76.416 13.5781C76.416 14.1412 76.33 14.6615 76.1651 15.1319C76.0003 15.6023 75.7566 16.0086 75.4412 16.3507C75.1258 16.6928 74.7316 16.9566 74.2729 17.149C73.8141 17.3414 73.2909 17.4412 72.7031 17.4412ZM72.7031 16.1654C73.0615 16.1654 73.3769 16.087 73.6421 15.9373C73.9073 15.7876 74.1223 15.5881 74.2943 15.3457C74.4664 15.1034 74.5882 14.8254 74.6742 14.5189C74.7531 14.2125 74.7961 13.8989 74.7961 13.5781C74.7961 13.2645 74.7531 12.958 74.6742 12.6444C74.5954 12.3308 74.4664 12.06 74.2943 11.8176C74.1223 11.5753 73.9073 11.3828 73.6421 11.2332C73.3769 11.0835 73.0615 11.0051 72.7031 11.0051C72.3447 11.0051 72.0293 11.0835 71.7641 11.2332C71.4989 11.3828 71.2839 11.5824 71.1118 11.8176C70.9398 12.06 70.818 12.3308 70.7319 12.6444C70.6531 12.958 70.6101 13.2645 70.6101 13.5781C70.6101 13.8989 70.6531 14.2125 70.7319 14.5189C70.8108 14.8254 70.9398 15.1034 71.1118 15.3457C71.2839 15.5881 71.4989 15.7876 71.7641 15.9373C72.0293 16.0941 72.3447 16.1654 72.7031 16.1654ZM76.8891 9.92883H78.1148V7.73355H79.7347V9.92883H81.1969V11.1334H79.7347V15.0393C79.7347 15.2103 79.7419 15.3529 79.7562 15.4812C79.7706 15.6023 79.8064 15.7092 79.8566 15.7948C79.9067 15.8803 79.9856 15.9444 80.0931 15.9872C80.2006 16.03 80.3368 16.0514 80.5232 16.0514C80.6379 16.0514 80.7525 16.0514 80.8672 16.0442C80.9819 16.0371 81.0966 16.0228 81.2113 15.9943V17.2417C81.0321 17.263 80.8529 17.2773 80.688 17.2987C80.516 17.3201 80.344 17.3272 80.1648 17.3272C79.7347 17.3272 79.3907 17.2844 79.1326 17.206C78.8746 17.1276 78.6667 17.0064 78.5234 16.8496C78.3728 16.6928 78.2797 16.5004 78.2223 16.2652C78.1721 16.03 78.1363 15.7591 78.1291 15.4598V11.1476H76.9034V9.92883L76.8891 9.92883Z"
                                                   fill="white" />
                                                <path
                                                   d="M19.4463 7.14199H12.0204L9.72672 0.114258L7.42585 7.14198L0 7.13486L6.01379 11.4826L3.71292 18.5032L9.72672 14.1626L15.7333 18.5032L13.4396 11.4826L19.4463 7.14199Z"
                                                   fill="#00B67A" />
                                                <path d="M13.9557 13.072L13.4396 11.4826L9.72668 14.1625L13.9557 13.072Z" fill="#005128" />
                                             </g>
                                             <defs>
                                                <clipPath id="clip0_2350_3858">
                                                   <rect width="81.2903" height="20" fill="white" />
                                                </clipPath>
                                             </defs>
                                          </svg>
                                       </span>
                                    </span>
                                 </div>
                              </div>
                           </div>
                           <div class="col-xl-6 col-xxl-5 offset-xxl-1 col-md-6" data-cue="slideInRight">
                              <div class="text-center mt-lg-8 d-flex justify-content-center">
                                 <img src="./assets/images/mobile-app/mobile-hero-img-light.png" alt="hero img" class="img-fluid dark-mode-none" />
                                 <img src="./assets/images/mobile-app/mobile-hero-img-dark.png" alt="hero img" class="img-fluid d-none dark-mode-block" />
                              </div>
                           </div>
                        </div>
                     </div>
                  </div>
               </div>
            </div>
         </section>
         <!--Hero section-->
         <!--Marquee-->
         <div class="py-xl-9 py-6" data-cue="fadeIn">
            <div class="container">
               <div class="row">
                  <div class="col-12">
                     <div class="marquee">
                        <div class="text-track h3 text-body-secondary mb-0">
                           Navigate your finances with confidence.

                           <svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" fill="currentColor" class="bi bi-dot text-body-secondary" viewBox="0 0 16 16">
                              <path d="M8 9.5a1.5 1.5 0 1 0 0-3 1.5 1.5 0 0 0 0 3" />
                           </svg>

                           Track spending, budgets.

                           <svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" fill="currentColor" class="bi bi-dot text-body-secondary" viewBox="0 0 16 16">
                              <path d="M8 9.5a1.5 1.5 0 1 0 0-3 1.5 1.5 0 0 0 0 3" />
                           </svg>

                           Master your money with confidence.

                           <svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" fill="currentColor" class="bi bi-dot text-body-secondary" viewBox="0 0 16 16">
                              <path d="M8 9.5a1.5 1.5 0 1 0 0-3 1.5 1.5 0 0 0 0 3" />
                           </svg>
                           Navigate your finances with confidence.

                           <svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" fill="currentColor" class="bi bi-dot text-body-secondary" viewBox="0 0 16 16">
                              <path d="M8 9.5a1.5 1.5 0 1 0 0-3 1.5 1.5 0 0 0 0 3" />
                           </svg>

                           Track spending, budgets.

                           <svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" fill="currentColor" class="bi bi-dot text-body-secondary" viewBox="0 0 16 16">
                              <path d="M8 9.5a1.5 1.5 0 1 0 0-3 1.5 1.5 0 0 0 0 3" />
                           </svg>
                        </div>
                     </div>
                  </div>
               </div>
            </div>
         </div>
         <!--Marquee-->
         <!--Management-->
         <section class="my-5" data-cue="fadeIn">
            <div class="container">
               <div class="row">
                  <div class="col-xl-6 offset-xl-3 col-lg-8 mx-auto col-12">
                     <div class="d-flex flex-column gap-4 text-center">
                        <div class="d-flex justify-content-center">
                           <span class="badge bg-white text-muted border border-light-subtle rounded-pill text-uppercase fw-semibold py-2 px-3 small ls-md">Features</span>
                        </div>
                        <div>
                           <h2 class="mb-0 display-6">Smart features for smarter financial management</h2>
                        </div>
                     </div>
                  </div>
               </div>
            </div>
         </section>
         <!--Management-->
         <!--Expense-->
         <section class="" data-cue="fadeIn">
            <div class="container py-5">
               <div class="row align-items-center gy-5 gy-lg-0 bg-success bg-opacity-10 rounded-4 text-dark text-opacity-75">
                  <div class="col-xl-5 offset-xl-1 col-lg-6 col-12" data-cue="slideInLeft">
                     <div class="d-flex flex-column gap-4">
                        <div>
                           <span class="badge bg-success bg-opacity-10 border border-success text-success rounded-pill py-2 px-3 text-uppercase ls-md">Wallet Budget</span>
                        </div>
                        <div class="d-flex flex-column gap-2">
                           <h3 class="mb-0 fs-2">Expense Tracking</h3>
                           <p class="mb-0">Track your spending effortlessly Lorem ipsum dolor sit amet, consectetur adipiscing elit. Proin volutpat mollis egestas.</p>
                        </div>
                        <div>
                           <ul class="list-unstyled mb-0 d-flex flex-column gap-3">
                              <li class="d-flex flex-row gap-2">
                                 <span>
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-check-circle text-success" viewBox="0 0 16 16">
                                       <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14m0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16" />
                                       <path d="m10.97 4.97-.02.022-3.473 4.425-2.093-2.094a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-1.071-1.05" />
                                    </svg>
                                 </span>
                                 <span>Consectetur adipiscing elit.</span>
                              </li>
                              <li class="d-flex flex-row gap-2">
                                 <span>
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-check-circle text-success" viewBox="0 0 16 16">
                                       <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14m0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16" />
                                       <path d="m10.97 4.97-.02.022-3.473 4.425-2.093-2.094a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-1.071-1.05" />
                                    </svg>
                                 </span>
                                 <span>Fusce interdum diam eu mauris</span>
                              </li>
                              <li class="d-flex flex-row gap-2">
                                 <span>
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-check-circle text-success" viewBox="0 0 16 16">
                                       <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14m0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16" />
                                       <path d="m10.97 4.97-.02.022-3.473 4.425-2.093-2.094a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-1.071-1.05" />
                                    </svg>
                                 </span>
                                 <span>Maecenas nec arcu in libero gravida</span>
                              </li>
                           </ul>
                        </div>
                        <a href="#" class="icon-link icon-link-hover link-success">
                           <span>Learn More</span>
                           <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" class="bi bi-arrow-right" viewBox="0 0 16 16">
                              <path
                                 fill-rule="evenodd"
                                 d="M1 8a.5.5 0 0 1 .5-.5h11.793l-3.147-3.146a.5.5 0 0 1 .708-.708l4 4a.5.5 0 0 1 0 .708l-4 4a.5.5 0 0 1-.708-.708L13.293 8.5H1.5A.5.5 0 0 1 1 8z" />
                           </svg>
                        </a>
                     </div>
                  </div>
                  <div class="col-xl-5 col-lg-6 col-12" data-cue="slideInRight">
                     <div class="d-flex justify-content-center mt-6">
                        <img src="./assets/images/mobile-app/mobile-right-light.png" alt="mobile right" class="img-fluid dark-mode-none" />
                        <img src="./assets/images/mobile-app/mobile-right-dark.png" alt="mobile right" class="img-fluid d-none dark-mode-block" />
                     </div>
                  </div>
               </div>
            </div>
         </section>
         <!--Expense-->

         <!--Budget-->
         <section class="">
            <div class="container py-lg-8 py-5">
               <div class="row align-items-center gy-5 gy-lg-0 bg-warning bg-opacity-10 rounded-4 text-dark text-opacity-75">
                  <div class="col-xl-7 col-lg-6 col-12 order-2 order-lg-1" data-cue="slideInLeft">
                     <div class="d-flex justify-content-center mt-6">
                        <img src="./assets/images/mobile-app/mobile-left-light.png" alt="mobile left" class="img-fluid dark-mode-none" />
                        <img src="./assets/images/mobile-app/mobile-left-dark.png" alt="mobile left" class="img-fluid d-none dark-mode-block" />
                     </div>
                  </div>
                  <div class="col-xl-4 col-lg-6 col-12 order-1 order-lg-2" data-cue="slideInRight">
                     <div class="d-flex flex-column gap-4">
                        <div>
                           <span class="badge bg-warning bg-opacity-10 border border-warning-subtle text-warning-emphasis rounded-pill py-2 px-3 text-uppercase small ls-md">planning tool</span>
                        </div>
                        <div class="d-flex flex-column gap-2">
                           <h3 class="mb-0 fs-2">Budget Planner</h3>
                           <div class="mt-3">
                              <div class="d-flex align-items-center gap-3 mb-2">
                                 <div class="icon-shape icon-lg bg-warning-subtle rounded-3">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                       <path
                                          d="M4.50656 9.75H19.4934C19.718 9.75114 19.9399 9.70186 20.1428 9.60579C20.3458 9.50973 20.5245 9.36933 20.666 9.19496C20.8074 9.02058 20.9079 8.81668 20.9601 8.59829C21.0122 8.37989 21.0146 8.15259 20.9672 7.93313C20.2706 4.64063 16.5 2.25 12 2.25C7.5 2.25 3.72937 4.64063 3.03281 7.93313C2.98538 8.15259 2.98782 8.37989 3.03995 8.59829C3.09208 8.81668 3.19257 9.02058 3.33401 9.19496C3.47546 9.36933 3.65424 9.50973 3.85718 9.60579C4.06012 9.70186 4.28204 9.75114 4.50656 9.75ZM12 3.75C15.7331 3.75 18.9572 5.68219 19.4934 8.25H4.50656L4.5 8.24344C5.04281 5.68219 8.26688 3.75 12 3.75ZM21.4931 14.295L17.6372 15.7013L14.1562 14.3034C13.9775 14.232 13.7781 14.232 13.5994 14.3034L10.1306 15.6919L6.65625 14.3034C6.4852 14.2351 6.29499 14.2321 6.12188 14.295L1.99687 15.795C1.82253 15.8717 1.68392 16.0119 1.60918 16.1871C1.53444 16.3624 1.52916 16.5594 1.59442 16.7384C1.65968 16.9173 1.79058 17.0648 1.96057 17.1507C2.13055 17.2367 2.32687 17.2548 2.50969 17.2013L3.75 16.7522V17.25C3.75 18.2446 4.14509 19.1984 4.84835 19.9016C5.55161 20.6049 6.50544 21 7.5 21H16.5C17.4946 21 18.4484 20.6049 19.1516 19.9016C19.8549 19.1984 20.25 18.2446 20.25 17.25V16.3434L22.0059 15.705C22.1044 15.6762 22.1959 15.6275 22.2748 15.5619C22.3537 15.4963 22.4183 15.4152 22.4646 15.3237C22.5109 15.2321 22.5379 15.132 22.544 15.0296C22.55 14.9272 22.535 14.8246 22.4999 14.7282C22.4647 14.6319 22.4102 14.5437 22.3396 14.4693C22.269 14.3948 22.1839 14.3356 22.0896 14.2953C21.9952 14.2551 21.8936 14.2346 21.791 14.2352C21.6884 14.2358 21.587 14.2574 21.4931 14.2987V14.295ZM18.75 17.25C18.75 17.8467 18.5129 18.419 18.091 18.841C17.669 19.2629 17.0967 19.5 16.5 19.5H7.5C6.90326 19.5 6.33097 19.2629 5.90901 18.841C5.48705 18.419 5.25 17.8467 5.25 17.25V16.2075L6.36281 15.8025L9.84375 17.1966C10.0225 17.268 10.2219 17.268 10.4006 17.1966L13.8694 15.8081L17.3381 17.1966C17.5092 17.2649 17.6994 17.2679 17.8725 17.205L18.7416 16.8891L18.75 17.25ZM1.5 12C1.5 11.8011 1.57902 11.6103 1.71967 11.4697C1.86032 11.329 2.05109 11.25 2.25 11.25H21.75C21.9489 11.25 22.1397 11.329 22.2803 11.4697C22.421 11.6103 22.5 11.8011 22.5 12C22.5 12.1989 22.421 12.3897 22.2803 12.5303C22.1397 12.671 21.9489 12.75 21.75 12.75H2.25C2.05109 12.75 1.86032 12.671 1.71967 12.5303C1.57902 12.3897 1.5 12.1989 1.5 12Z"
                                          fill="#7A4F01" />
                                    </svg>
                                 </div>
                                 <h3 class="fs-4 mb-0">Food & Drinks</h3>
                              </div>
                              <div>
                                 <p class="mb-0">Track your spending effortlessly Lorem ipsum dolor sit amet, consectetur adipiscing elit.</p>
                              </div>
                           </div>
                           <div class="mt-3">
                              <div class="d-flex align-items-center gap-3 mb-2">
                                 <div class="icon-shape icon-lg bg-warning-subtle rounded-3">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                       <path
                                          d="M22.0856 12.0787L15 8.53687V4.5C15 3.70435 14.6839 2.94129 14.1213 2.37868C13.5587 1.81607 12.7956 1.5 12 1.5C11.2044 1.5 10.4413 1.81607 9.87868 2.37868C9.31607 2.94129 9 3.70435 9 4.5V8.53687L1.91438 12.0787C1.78977 12.1411 1.685 12.2369 1.61181 12.3555C1.53862 12.4741 1.4999 12.6107 1.5 12.75V15.75C1.50006 15.8609 1.52473 15.9705 1.57224 16.0707C1.61974 16.171 1.68889 16.2595 1.77471 16.3298C1.86053 16.4001 1.96087 16.4504 2.06851 16.4773C2.17615 16.5041 2.28841 16.5068 2.39719 16.485L9 15.165V16.9397L7.71937 18.2194C7.64975 18.2891 7.59454 18.3718 7.5569 18.4629C7.51926 18.5539 7.49992 18.6515 7.5 18.75V21.75C7.49979 21.8729 7.52979 21.994 7.58737 22.1026C7.64495 22.2112 7.72834 22.304 7.8302 22.3728C7.93206 22.4416 8.04927 22.4843 8.17152 22.4972C8.29377 22.51 8.4173 22.4927 8.53125 22.4466L12 21.0581L15.4688 22.4466C15.5827 22.4927 15.7062 22.51 15.8285 22.4972C15.9507 22.4843 16.0679 22.4416 16.1698 22.3728C16.2717 22.304 16.3551 22.2112 16.4126 22.1026C16.4702 21.994 16.5002 21.8729 16.5 21.75V18.75C16.5001 18.6515 16.4807 18.5539 16.4431 18.4629C16.4055 18.3718 16.3503 18.2891 16.2806 18.2194L15 16.9397V15.165L21.6028 16.485C21.7116 16.5068 21.8238 16.5041 21.9315 16.4773C22.0391 16.4504 22.1395 16.4001 22.2253 16.3298C22.3111 16.2595 22.3803 16.171 22.4278 16.0707C22.4753 15.9705 22.4999 15.8609 22.5 15.75V12.75C22.5001 12.6107 22.4614 12.4741 22.3882 12.3555C22.315 12.2369 22.2102 12.1411 22.0856 12.0787ZM21 14.835L14.3972 13.515C14.2884 13.4932 14.1762 13.4959 14.0685 13.5227C13.9609 13.5496 13.8605 13.5999 13.7747 13.6702C13.6889 13.7405 13.6197 13.829 13.5722 13.9293C13.5247 14.0295 13.5001 14.1391 13.5 14.25V17.25C13.4999 17.3485 13.5193 17.4461 13.5569 17.5371C13.5945 17.6282 13.6497 17.7109 13.7194 17.7806L15 19.0603V20.6419L12.2812 19.5534C12.1025 19.482 11.9031 19.482 11.7244 19.5534L9 20.6419V19.0603L10.2806 17.7806C10.3503 17.7109 10.4055 17.6282 10.4431 17.5371C10.4807 17.4461 10.5001 17.3485 10.5 17.25V14.25C10.4999 14.1391 10.4753 14.0295 10.4278 13.9293C10.3803 13.829 10.3111 13.7405 10.2253 13.6702C10.1395 13.5999 10.0391 13.5496 9.93149 13.5227C9.82385 13.4959 9.71159 13.4932 9.60281 13.515L3 14.835V13.2131L10.0856 9.67125C10.2102 9.6089 10.315 9.51307 10.3882 9.39451C10.4614 9.27594 10.5001 9.13933 10.5 9V4.5C10.5 4.10218 10.658 3.72064 10.9393 3.43934C11.2206 3.15804 11.6022 3 12 3C12.3978 3 12.7794 3.15804 13.0607 3.43934C13.342 3.72064 13.5 4.10218 13.5 4.5V9C13.4999 9.13933 13.5386 9.27594 13.6118 9.39451C13.685 9.51307 13.7898 9.6089 13.9144 9.67125L21 13.2131V14.835Z"
                                          fill="#7A4F01" />
                                    </svg>
                                 </div>
                                 <h3 class="fs-4 mb-0">Travels</h3>
                              </div>
                              <div>
                                 <p class="mb-0">Track your spending effortlessly Lorem ipsum dolor sit amet, consectetur adipiscing elit.</p>
                              </div>
                           </div>
                        </div>
                     </div>
                  </div>
               </div>
            </div>
         </section>
         <!--Budget-->
         <!--Investment-->
         <section class="">
            <div class="container py-5">
               <div class="row align-items-center gy-5 gy-lg-0 bg-primary bg-opacity-10 rounded-4 text-dark text-opacity-75">
                  <div class="col-xl-6 offset-xl-1 col-lg-6 col-12" data-cue="slideInLeft">
                     <div class="d-flex flex-column gap-4">
                        <div>
                           <span class="badge bg-primary bg-opacity-10 border border-primary-subtle text-primary-emphasis rounded-pill py-2 px-3 text-uppercase small ls-md">manage your money</span>
                        </div>
                        <div class="d-flex flex-column gap-2">
                           <h3 class="mb-0 fs-2">Investment Insights</h3>
                        </div>
                        <div class="row g-4">
                           <div class="col-md-6">
                              <div class="icon-shape icon-lg bg-primary bg-opacity-10 rounded-2 mb-3">
                                 <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path
                                       d="M21.75 9C21.7504 8.93027 21.7409 8.86083 21.7219 8.79375L20.3766 4.0875C20.2861 3.77523 20.0971 3.50059 19.8378 3.30459C19.5784 3.10858 19.2626 3.00174 18.9375 3H5.0625C4.73741 3.00174 4.4216 3.10858 4.16223 3.30459C3.90287 3.50059 3.71386 3.77523 3.62344 4.0875L2.27906 8.79375C2.2597 8.86079 2.24991 8.93022 2.25 9V10.5C2.25 11.0822 2.38554 11.6563 2.6459 12.1771C2.90625 12.6978 3.28427 13.1507 3.75 13.5V19.5C3.75 19.8978 3.90804 20.2794 4.18934 20.5607C4.47064 20.842 4.85218 21 5.25 21H18.75C19.1478 21 19.5294 20.842 19.8107 20.5607C20.092 20.2794 20.25 19.8978 20.25 19.5V13.5C20.7157 13.1507 21.0937 12.6978 21.3541 12.1771C21.6145 11.6563 21.75 11.0822 21.75 10.5V9ZM5.0625 4.5H18.9375L20.0081 8.25H3.99469L5.0625 4.5ZM9.75 9.75H14.25V10.5C14.25 11.0967 14.0129 11.669 13.591 12.091C13.169 12.5129 12.5967 12.75 12 12.75C11.4033 12.75 10.831 12.5129 10.409 12.091C9.98705 11.669 9.75 11.0967 9.75 10.5V9.75ZM8.25 9.75V10.5C8.25 11.0967 8.01295 11.669 7.59099 12.091C7.16903 12.5129 6.59674 12.75 6 12.75C5.40326 12.75 4.83097 12.5129 4.40901 12.091C3.98705 11.669 3.75 11.0967 3.75 10.5V9.75H8.25ZM18.75 19.5H5.25V14.175C5.4969 14.2248 5.74813 14.2499 6 14.25C6.58217 14.25 7.15634 14.1145 7.67705 13.8541C8.19776 13.5937 8.6507 13.2157 9 12.75C9.3493 13.2157 9.80224 13.5937 10.3229 13.8541C10.8437 14.1145 11.4178 14.25 12 14.25C12.5822 14.25 13.1563 14.1145 13.6771 13.8541C14.1978 13.5937 14.6507 13.2157 15 12.75C15.3493 13.2157 15.8022 13.5937 16.3229 13.8541C16.8437 14.1145 17.4178 14.25 18 14.25C18.2519 14.2499 18.5031 14.2248 18.75 14.175V19.5ZM18 12.75C17.4033 12.75 16.831 12.5129 16.409 12.091C15.9871 11.669 15.75 11.0967 15.75 10.5V9.75H20.25V10.5C20.25 11.0967 20.0129 11.669 19.591 12.091C19.169 12.5129 18.5967 12.75 18 12.75Z"
                                       fill="#8B3DFF" />
                                 </svg>
                              </div>
                              <div>
                                 <h4 class="mb-1">Access to market inside</h4>
                                 <p class="mb-0">Very popular during the Renaissance. The firs Landom text</p>
                              </div>
                           </div>
                           <div class="col-md-6">
                              <div class="icon-shape icon-lg bg-primary bg-opacity-10 rounded-2 mb-3">
                                 <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path
                                       d="M16.5 21.75C16.5 21.9489 16.421 22.1396 16.2803 22.2803C16.1397 22.4209 15.9489 22.5 15.75 22.5H8.25C8.05109 22.5 7.86032 22.4209 7.71967 22.2803C7.57902 22.1396 7.5 21.9489 7.5 21.75C7.5 21.551 7.57902 21.3603 7.71967 21.2196C7.86032 21.079 8.05109 21 8.25 21H15.75C15.9489 21 16.1397 21.079 16.2803 21.2196C16.421 21.3603 16.5 21.551 16.5 21.75ZM20.25 9.74995C20.2532 11.0002 19.9708 12.2347 19.4242 13.3592C18.8776 14.4837 18.0814 15.4685 17.0962 16.2384C16.912 16.3796 16.7626 16.561 16.6592 16.7688C16.5559 16.9767 16.5014 17.2054 16.5 17.4375V18C16.5 18.3978 16.342 18.7793 16.0607 19.0606C15.7794 19.3419 15.3978 19.5 15 19.5H9C8.60218 19.5 8.22064 19.3419 7.93934 19.0606C7.65804 18.7793 7.5 18.3978 7.5 18V17.4375C7.49985 17.2081 7.44712 16.9819 7.34587 16.7762C7.24462 16.5704 7.09754 16.3906 6.91594 16.2506C5.93338 15.4853 5.13774 14.5066 4.58925 13.3885C4.04075 12.2704 3.75376 11.0422 3.75 9.79683C3.72563 5.32777 7.33688 1.60777 11.8013 1.49995C12.9013 1.47345 13.9955 1.66725 15.0195 2.06997C16.0434 2.47269 16.9765 3.07617 17.7638 3.84491C18.551 4.61365 19.1766 5.53211 19.6035 6.54622C20.0305 7.56034 20.2503 8.64962 20.25 9.74995ZM18.75 9.74995C18.7503 8.84964 18.5704 7.95836 18.221 7.1286C17.8717 6.29883 17.3598 5.54735 16.7156 4.91837C16.0715 4.28939 15.308 3.79564 14.4701 3.46618C13.6322 3.13671 12.7369 2.9782 11.8369 2.99995C8.18063 3.0862 5.23031 6.13027 5.25 9.78652C5.25336 10.8052 5.4884 11.8098 5.93733 12.7242C6.38627 13.6386 7.03733 14.439 7.84125 15.0646C8.2026 15.3456 8.49489 15.7054 8.69574 16.1167C8.89658 16.528 9.00066 16.9798 9 17.4375V18H11.25V13.8103L8.46937 11.0306C8.32864 10.8898 8.24958 10.699 8.24958 10.5C8.24958 10.3009 8.32864 10.1101 8.46937 9.96933C8.61011 9.8286 8.80098 9.74954 9 9.74954C9.19902 9.74954 9.38989 9.8286 9.53063 9.96933L12 12.4396L14.4694 9.96933C14.5391 9.89964 14.6218 9.84437 14.7128 9.80666C14.8039 9.76895 14.9015 9.74954 15 9.74954C15.0985 9.74954 15.1961 9.76895 15.2872 9.80666C15.3782 9.84437 15.4609 9.89964 15.5306 9.96933C15.6003 10.039 15.6556 10.1217 15.6933 10.2128C15.731 10.3038 15.7504 10.4014 15.7504 10.5C15.7504 10.5985 15.731 10.6961 15.6933 10.7871C15.6556 10.8782 15.6003 10.9609 15.5306 11.0306L12.75 13.8103V18H15V17.4375C15.0008 16.9784 15.1066 16.5256 15.3092 16.1137C15.5118 15.7018 15.8059 15.3417 16.1691 15.0609C16.9754 14.4307 17.6271 13.6247 18.0744 12.7042C18.5217 11.7838 18.7528 10.7733 18.75 9.74995Z"
                                       fill="#8B3DFF" />
                                 </svg>
                              </div>
                              <div>
                                 <h4 class="mb-1">Expertise and Knowledge</h4>
                                 <p class="mb-0">Very popular during the Renaissance. The firs Landom text</p>
                              </div>
                           </div>
                        </div>
                     </div>
                  </div>
                  <div class="col-xl-5 col-lg-6 col-12" data-cue="slideInRight">
                     <div class="d-flex justify-content-center mt-6">
                        <img src="./assets/images/mobile-app/mobile-front-light.png" alt="mobile front" class="img-fluid dark-mode-none" />
                        <img src="./assets/images/mobile-app/mobile-front-dark.png" alt="mobile front" class="img-fluid d-none dark-mode-block" />
                     </div>
                  </div>
               </div>
            </div>
         </section>
         <!--investment-->

         <!--Priority-->
         <section class="py-xl-9 py-6" data-cue="fadeIn">
            <div class="container">
               <div class="row mb-6">
                  <div class="col-lg-8 offset-lg-2 col-md-10 offset-md-1 col-12">
                     <div class="d-flex flex-column gap-4 text-center">
                        <div class="d-flex justify-content-center">
                           <span class="badge bg-white text-muted border border-light-subtle rounded-pill text-uppercase fw-semibold py-2 px-3 small ls-md">Security</span>
                        </div>
                        <div class="d-flex flex-column gap-2">
                           <h2 class="mb-0 display-6">Your Security is Our Priority</h2>
                           <p class="mb-0">
                              Your security and privacy at the heart of
                              <span class="text-primary">[App name]</span>
                              , because we know there’s no room for compromise when it comes to money.
                           </p>
                        </div>
                     </div>
                  </div>
               </div>
               <div class="row gy-lg-6 gy-4">
                  <div class="col-lg-4 col-md-6" data-cue="zoomIn">
                     <div class="d-flex flex-column gap-5 p-xxl-6 text-center p-3 card card-lift">
                        <div><img src="./assets/images/mobile-app/security.svg" alt="security" /></div>
                        <div class="d-flex flex-column gap-2">
                           <h3 class="h4">Bank-Level Security</h3>
                           <p class="mb-0">256-bit encryption to protect your data. We support face & fingerprint ID and use 256-bit TLS encryption.</p>
                        </div>
                     </div>
                  </div>
                  <div class="col-lg-4 col-md-6" data-cue="zoomIn">
                     <div class="d-flex flex-column gap-5 p-xxl-6 text-center p-3 card card-lift">
                        <div><img src="./assets/images/mobile-app/privacy.svg" alt="privacy" /></div>
                        <div class="d-flex flex-column gap-2">
                           <h3 class="h4">Privacy Protection</h3>
                           <p class="mb-0">Your data is private and secure. We never share your data with any other parties without your consent.</p>
                        </div>
                     </div>
                  </div>
                  <div class="col-lg-4 col-md-6" data-cue="zoomIn">
                     <div class="d-flex flex-column gap-5 p-xxl-6 text-center p-3 card card-lift">
                        <div><img src="./assets/images/mobile-app/authentication.svg" alt="authentication" /></div>
                        <div class="d-flex flex-column gap-2">
                           <h3 class="h4">Multi-Factor Authentication</h3>
                           <p class="mb-0">A multi-step account login process that requires users to provide more than just a password to access</p>
                        </div>
                     </div>
                  </div>
               </div>
            </div>
         </section>
         <!--Priority-->
         <!--Block-->
         <div class="pt-6" data-cue="fadeIn">
            <div class="container">
               <div class="border-top text-center">
                  <div class="">
                     <img src="./assets/images/mobile-app/block.svg" alt="block" class="mt-n5 bg-body p-3" />
                  </div>
               </div>
            </div>
         </div>
         <!--Block-->
         <!--Steps-->
         <section class="py-xl-9 py-6" data-cue="fadeIn">
            <div class="container">
               <div class="row justify-content-center">
                  <div class="col-lg-10 col-12" data-cue="zoomIn">
                     <div class="row align-items-center gy-5">
                        <div class="col-lg-5 col-12">
                           <div class="d-flex flex-column gap-4 mb-5 mb-lg-9 position-relative">
                              <div>
                                 <span class="badge bg-white text-primary border border-light-subtle rounded-pill text-uppercase fw-semibold py-2 px-3 small ls-md">How it works</span>
                              </div>
                              <div class="d-flex flex-column gap-2 mb-8">
                                 <h2 class="mb-0 display-6">Get Started In Just 3 simple Steps.</h2>
                                 <p class="mb-0">Three simple steps to unlock the power of Block and revolutionise your banking experience.</p>
                              </div>
                              <!-- Add Navigation -->
                              <div class="swiper-navigation position-absolute bottom-0">
                                 <div class="swiper-button-prev me-7"></div>
                                 <div class="swiper-button-next ms-7"></div>
                              </div>
                           </div>
                        </div>
                        <div class="offset-lg-1 col-lg-6 bg-light rounded-4">
                           <div
                              class="swiper-container swiper"
                              id="swiper-2"
                              data-pagination-type=""
                              data-speed="400"
                              data-space-between="100"
                              data-pagination="false"
                              data-navigation="true"
                              data-autoplay="false"
                              data-autoplay-delay="3000"
                              data-breakpoints='{"480": {"slidesPerView": 1}, "768": {"slidesPerView": 1}, "1024": {"slidesPerView": 2}, "1200": {"slidesPerView": 1}}'>
                              <div class="swiper-wrapper">
                                 <div class="swiper-slide">
                                    <div class="px-6 pt-6 text-center">
                                       <span class="badge text-bg-dark rounded-pill px-3 py-3 fs-6">Step - 1</span>
                                       <div class="mt-3 mb-4">
                                          <h3 class="mb-0">Scan & Download</h3>
                                          <p class="mb-0">Get the app and create your account.</p>
                                       </div>
                                       <div class="d-flex justify-content-center">
                                          <img src="./assets/images/mobile-app/step-img-light.png" alt="mobile front" class="img-fluid dark-mode-none" />
                                          <img src="./assets/images/mobile-app/step-img-dark.png" alt="mobile front" class="img-fluid d-none dark-mode-block" />
                                       </div>
                                    </div>
                                 </div>
                                 <div class="swiper-slide">
                                    <div class="text-center px-6 pt-6">
                                       <span class="badge text-bg-dark rounded-pill px-3 py-3 fs-6">Step - 2</span>
                                       <div class="mt-3 mb-4">
                                          <h3 class="mb-0">Connect Accounts</h3>
                                          <p class="mb-0">Securely connect your bank accounts.</p>
                                       </div>
                                       <div class="d-flex justify-content-center">
                                          <img src="./assets/images/mobile-app/step-img-light.png" alt="mobile front" class="img-fluid dark-mode-none" />
                                          <img src="./assets/images/mobile-app/step-img-dark.png" alt="mobile front" class="img-fluid d-none dark-mode-block" />
                                       </div>
                                    </div>
                                 </div>
                                 <div class="swiper-slide">
                                    <div class="text-center px-6 pt-6">
                                       <span class="badge text-bg-dark rounded-pill px-3 py-3 fs-6">Step - 3</span>
                                       <div class="mt-3 mb-4">
                                          <h3 class="mb-0">Track and Improve</h3>
                                          <p class="mb-0">Start managing your finances effortlessly.</p>
                                       </div>
                                       <div class="d-flex justify-content-center">
                                          <img src="./assets/images/mobile-app/step-img-light.png" alt="mobile front" class="img-fluid dark-mode-none" />
                                          <img src="./assets/images/mobile-app/step-img-dark.png" alt="mobile front" class="img-fluid d-none dark-mode-block" />
                                       </div>
                                    </div>
                                 </div>
                              </div>
                              <!-- Add Pagination -->
                              <div class="swiper-pagination"></div>
                           </div>
                        </div>
                     </div>
                  </div>
               </div>
            </div>
         </section>
         <!--Steps-->
         <!--Reviews-->
         <section class="py-xl-9 py-6" data-cue="fadeIn">
            <div class="container">
               <div class="bg-light-subtle rounded-4 py-xl-10 py-8 px-5">
                  <div class="row" data-cue="zoomIn">
                     <div class="col-lg-8 offset-lg-2 col-12">
                        <div class="d-flex flex-column gap-4 mb-5 mb-lg-9 text-center">
                           <div class="d-flex justify-content-center">
                              <span class="badge bg-white text-primary border border-light-subtle rounded-pill text-uppercase fw-semibold py-2 px-3 small ls-md">Reviews</span>
                           </div>
                           <div>
                              <div class="d-flex flex-column gap-2">
                                 <h2 class="mb-0 display-6">The reviews speak for themselves</h2>
                                 <p class="mb-0 px-xxl-8">
                                    Your security and privacy at the heart of
                                    <span class="text-primary">[App name]</span>
                                    , because we know there’s no room for compromise when it comes to money.
                                 </p>
                              </div>
                           </div>
                           <div class="d-flex flex-row lh-1 justify-content-center gap-3">
                              <span>4.8 / 5</span>
                              <span>
                                 <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" class="bi bi-star-fill text-warning" viewBox="0 0 16 16">
                                    <path
                                       d="M3.612 15.443c-.386.198-.824-.149-.746-.592l.83-4.73L.173 6.765c-.329-.314-.158-.888.283-.95l4.898-.696L7.538.792c.197-.39.73-.39.927 0l2.184 4.327 4.898.696c.441.062.612.636.282.95l-3.522 3.356.83 4.73c.078.443-.36.79-.746.592L8 13.187l-4.389 2.256z" />
                                 </svg>
                                 <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" class="bi bi-star-fill text-warning" viewBox="0 0 16 16">
                                    <path
                                       d="M3.612 15.443c-.386.198-.824-.149-.746-.592l.83-4.73L.173 6.765c-.329-.314-.158-.888.283-.95l4.898-.696L7.538.792c.197-.39.73-.39.927 0l2.184 4.327 4.898.696c.441.062.612.636.282.95l-3.522 3.356.83 4.73c.078.443-.36.79-.746.592L8 13.187l-4.389 2.256z" />
                                 </svg>
                                 <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" class="bi bi-star-fill text-warning" viewBox="0 0 16 16">
                                    <path
                                       d="M3.612 15.443c-.386.198-.824-.149-.746-.592l.83-4.73L.173 6.765c-.329-.314-.158-.888.283-.95l4.898-.696L7.538.792c.197-.39.73-.39.927 0l2.184 4.327 4.898.696c.441.062.612.636.282.95l-3.522 3.356.83 4.73c.078.443-.36.79-.746.592L8 13.187l-4.389 2.256z" />
                                 </svg>
                                 <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" class="bi bi-star-fill text-warning" viewBox="0 0 16 16">
                                    <path
                                       d="M3.612 15.443c-.386.198-.824-.149-.746-.592l.83-4.73L.173 6.765c-.329-.314-.158-.888.283-.95l4.898-.696L7.538.792c.197-.39.73-.39.927 0l2.184 4.327 4.898.696c.441.062.612.636.282.95l-3.522 3.356.83 4.73c.078.443-.36.79-.746.592L8 13.187l-4.389 2.256z" />
                                 </svg>
                                 <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" class="bi bi-star-half text-warning" viewBox="0 0 16 16">
                                    <path
                                       d="M5.354 5.119 7.538.792A.52.52 0 0 1 8 .5c.183 0 .366.097.465.292l2.184 4.327 4.898.696A.54.54 0 0 1 16 6.32a.55.55 0 0 1-.17.445l-3.523 3.356.83 4.73c.078.443-.36.79-.746.592L8 13.187l-4.389 2.256a.5.5 0 0 1-.146.05c-.342.06-.668-.254-.6-.642l.83-4.73L.173 6.765a.55.55 0 0 1-.172-.403.6.6 0 0 1 .085-.302.51.51 0 0 1 .37-.245zM8 12.027a.5.5 0 0 1 .232.056l3.686 1.894-.694-3.957a.56.56 0 0 1 .162-.505l2.907-2.77-4.052-.576a.53.53 0 0 1-.393-.288L8.001 2.223 8 2.226z" />
                                 </svg>
                              </span>
                              <span class="border-bottom pb-1">8,500+ reviews</span>
                           </div>
                        </div>
                     </div>
                  </div>
                  <div class="row" data-cue="zoomIn">
                     <div class="col-lg-10 offset-lg-1">
                        <div
                           class="swiper-container swiper"
                           id="swiper-1"
                           data-pagination-type=""
                           data-speed="400"
                           data-space-between="100"
                           data-pagination="true"
                           data-navigation="false"
                           data-autoplay="false"
                           data-autoplay-delay="3000"
                           data-breakpoints='{"480": {"slidesPerView": 1}, "768": {"slidesPerView": 2}, "1400": {"slidesPerView": 3}}'>
                           <div class="swiper-wrapper mb-6 pb-6">
                              <div class="swiper-slide pt-4">
                                 <div class="card shadow-sm border-0">
                                    <div class="icon-shape icon-xxl rounded-circle bg-light-subtle mx-auto mt-n4">
                                       <div class="bg-white icon-shape icon-lg rounded-circle shadow-lg">
                                          <svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" fill="currentColor" class="bi bi-quote text-info" viewBox="0 0 16 16">
                                             <path
                                                d="M12 12a1 1 0 0 0 1-1V8.558a1 1 0 0 0-1-1h-1.388q0-.527.062-1.054.093-.558.31-.992t.559-.683q.34-.279.868-.279V3q-.868 0-1.52.372a3.3 3.3 0 0 0-1.085.992 4.9 4.9 0 0 0-.62 1.458A7.7 7.7 0 0 0 9 7.558V11a1 1 0 0 0 1 1zm-6 0a1 1 0 0 0 1-1V8.558a1 1 0 0 0-1-1H4.612q0-.527.062-1.054.094-.558.31-.992.217-.434.559-.683.34-.279.868-.279V3q-.868 0-1.52.372a3.3 3.3 0 0 0-1.085.992 4.9 4.9 0 0 0-.62 1.458A7.7 7.7 0 0 0 3 7.558V11a1 1 0 0 0 1 1z" />
                                          </svg>
                                       </div>
                                    </div>

                                    <div class="card-body p-5 d-flex flex-column gap-5 position-relative">
                                       <div class="d-flex flex-column gap-5">
                                          <p class="mb-0">
                                             "This app has completely transformed how I manage my money. I've
                                             <span class="text-primary">saved over $500</span>
                                             in just two months. It's intuitive and easy to use, and I love the budgeting feature!"
                                          </p>
                                       </div>
                                       <div class="d-flex flex-column gap-1">
                                          <span>
                                             <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" class="bi bi-star-fill text-warning" viewBox="0 0 16 16">
                                                <path
                                                   d="M3.612 15.443c-.386.198-.824-.149-.746-.592l.83-4.73L.173 6.765c-.329-.314-.158-.888.283-.95l4.898-.696L7.538.792c.197-.39.73-.39.927 0l2.184 4.327 4.898.696c.441.062.612.636.282.95l-3.522 3.356.83 4.73c.078.443-.36.79-.746.592L8 13.187l-4.389 2.256z" />
                                             </svg>
                                             <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" class="bi bi-star-fill text-warning" viewBox="0 0 16 16">
                                                <path
                                                   d="M3.612 15.443c-.386.198-.824-.149-.746-.592l.83-4.73L.173 6.765c-.329-.314-.158-.888.283-.95l4.898-.696L7.538.792c.197-.39.73-.39.927 0l2.184 4.327 4.898.696c.441.062.612.636.282.95l-3.522 3.356.83 4.73c.078.443-.36.79-.746.592L8 13.187l-4.389 2.256z" />
                                             </svg>
                                             <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" class="bi bi-star-fill text-warning" viewBox="0 0 16 16">
                                                <path
                                                   d="M3.612 15.443c-.386.198-.824-.149-.746-.592l.83-4.73L.173 6.765c-.329-.314-.158-.888.283-.95l4.898-.696L7.538.792c.197-.39.73-.39.927 0l2.184 4.327 4.898.696c.441.062.612.636.282.95l-3.522 3.356.83 4.73c.078.443-.36.79-.746.592L8 13.187l-4.389 2.256z" />
                                             </svg>
                                             <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" class="bi bi-star-fill text-warning" viewBox="0 0 16 16">
                                                <path
                                                   d="M3.612 15.443c-.386.198-.824-.149-.746-.592l.83-4.73L.173 6.765c-.329-.314-.158-.888.283-.95l4.898-.696L7.538.792c.197-.39.73-.39.927 0l2.184 4.327 4.898.696c.441.062.612.636.282.95l-3.522 3.356.83 4.73c.078.443-.36.79-.746.592L8 13.187l-4.389 2.256z" />
                                             </svg>
                                             <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" class="bi bi-star-half text-warning" viewBox="0 0 16 16">
                                                <path
                                                   d="M5.354 5.119 7.538.792A.52.52 0 0 1 8 .5c.183 0 .366.097.465.292l2.184 4.327 4.898.696A.54.54 0 0 1 16 6.32a.55.55 0 0 1-.17.445l-3.523 3.356.83 4.73c.078.443-.36.79-.746.592L8 13.187l-4.389 2.256a.5.5 0 0 1-.146.05c-.342.06-.668-.254-.6-.642l.83-4.73L.173 6.765a.55.55 0 0 1-.172-.403.6.6 0 0 1 .085-.302.51.51 0 0 1 .37-.245zM8 12.027a.5.5 0 0 1 .232.056l3.686 1.894-.694-3.957a.56.56 0 0 1 .162-.505l2.907-2.77-4.052-.576a.53.53 0 0 1-.393-.288L8.001 2.223 8 2.226z" />
                                             </svg>
                                          </span>
                                          <span class="text-dark">Sarah J.</span>
                                       </div>
                                    </div>
                                 </div>
                              </div>
                              <div class="swiper-slide pt-4">
                                 <div class="card shadow-sm border-0">
                                    <div class="icon-shape icon-xxl rounded-circle bg-light-subtle mx-auto mt-n4">
                                       <div class="bg-white icon-shape icon-lg rounded-circle shadow-lg">
                                          <svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" fill="currentColor" class="bi bi-quote text-info" viewBox="0 0 16 16">
                                             <path
                                                d="M12 12a1 1 0 0 0 1-1V8.558a1 1 0 0 0-1-1h-1.388q0-.527.062-1.054.093-.558.31-.992t.559-.683q.34-.279.868-.279V3q-.868 0-1.52.372a3.3 3.3 0 0 0-1.085.992 4.9 4.9 0 0 0-.62 1.458A7.7 7.7 0 0 0 9 7.558V11a1 1 0 0 0 1 1zm-6 0a1 1 0 0 0 1-1V8.558a1 1 0 0 0-1-1H4.612q0-.527.062-1.054.094-.558.31-.992.217-.434.559-.683.34-.279.868-.279V3q-.868 0-1.52.372a3.3 3.3 0 0 0-1.085.992 4.9 4.9 0 0 0-.62 1.458A7.7 7.7 0 0 0 3 7.558V11a1 1 0 0 0 1 1z" />
                                          </svg>
                                       </div>
                                    </div>
                                    <div class="card-body p-5 d-flex flex-column gap-5 position-relative">
                                       <div class="d-flex flex-column gap-5">
                                          <p class="mb-0">
                                             "I finally feel in control of my finances. The expense
                                             <span class="text-primary">categorization and savings goals</span>
                                             have been game-changers for me. Highly recommend this app to anyone looking to get their finances in order."
                                          </p>
                                       </div>
                                       <div class="d-flex flex-column gap-1">
                                          <span>
                                             <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" class="bi bi-star-fill text-warning" viewBox="0 0 16 16">
                                                <path
                                                   d="M3.612 15.443c-.386.198-.824-.149-.746-.592l.83-4.73L.173 6.765c-.329-.314-.158-.888.283-.95l4.898-.696L7.538.792c.197-.39.73-.39.927 0l2.184 4.327 4.898.696c.441.062.612.636.282.95l-3.522 3.356.83 4.73c.078.443-.36.79-.746.592L8 13.187l-4.389 2.256z" />
                                             </svg>
                                             <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" class="bi bi-star-fill text-warning" viewBox="0 0 16 16">
                                                <path
                                                   d="M3.612 15.443c-.386.198-.824-.149-.746-.592l.83-4.73L.173 6.765c-.329-.314-.158-.888.283-.95l4.898-.696L7.538.792c.197-.39.73-.39.927 0l2.184 4.327 4.898.696c.441.062.612.636.282.95l-3.522 3.356.83 4.73c.078.443-.36.79-.746.592L8 13.187l-4.389 2.256z" />
                                             </svg>
                                             <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" class="bi bi-star-fill text-warning" viewBox="0 0 16 16">
                                                <path
                                                   d="M3.612 15.443c-.386.198-.824-.149-.746-.592l.83-4.73L.173 6.765c-.329-.314-.158-.888.283-.95l4.898-.696L7.538.792c.197-.39.73-.39.927 0l2.184 4.327 4.898.696c.441.062.612.636.282.95l-3.522 3.356.83 4.73c.078.443-.36.79-.746.592L8 13.187l-4.389 2.256z" />
                                             </svg>
                                             <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" class="bi bi-star-fill text-warning" viewBox="0 0 16 16">
                                                <path
                                                   d="M3.612 15.443c-.386.198-.824-.149-.746-.592l.83-4.73L.173 6.765c-.329-.314-.158-.888.283-.95l4.898-.696L7.538.792c.197-.39.73-.39.927 0l2.184 4.327 4.898.696c.441.062.612.636.282.95l-3.522 3.356.83 4.73c.078.443-.36.79-.746.592L8 13.187l-4.389 2.256z" />
                                             </svg>
                                             <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" class="bi bi-star-half text-warning" viewBox="0 0 16 16">
                                                <path
                                                   d="M5.354 5.119 7.538.792A.52.52 0 0 1 8 .5c.183 0 .366.097.465.292l2.184 4.327 4.898.696A.54.54 0 0 1 16 6.32a.55.55 0 0 1-.17.445l-3.523 3.356.83 4.73c.078.443-.36.79-.746.592L8 13.187l-4.389 2.256a.5.5 0 0 1-.146.05c-.342.06-.668-.254-.6-.642l.83-4.73L.173 6.765a.55.55 0 0 1-.172-.403.6.6 0 0 1 .085-.302.51.51 0 0 1 .37-.245zM8 12.027a.5.5 0 0 1 .232.056l3.686 1.894-.694-3.957a.56.56 0 0 1 .162-.505l2.907-2.77-4.052-.576a.53.53 0 0 1-.393-.288L8.001 2.223 8 2.226z" />
                                             </svg>
                                          </span>
                                          <span class="text-dark">Mark P.</span>
                                       </div>
                                    </div>
                                 </div>
                              </div>
                              <div class="swiper-slide pt-4">
                                 <div class="card shadow-sm border-0">
                                    <div class="icon-shape icon-xxl rounded-circle bg-light-subtle mx-auto mt-n4">
                                       <div class="bg-white icon-shape icon-lg rounded-circle shadow-lg">
                                          <svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" fill="currentColor" class="bi bi-quote text-info" viewBox="0 0 16 16">
                                             <path
                                                d="M12 12a1 1 0 0 0 1-1V8.558a1 1 0 0 0-1-1h-1.388q0-.527.062-1.054.093-.558.31-.992t.559-.683q.34-.279.868-.279V3q-.868 0-1.52.372a3.3 3.3 0 0 0-1.085.992 4.9 4.9 0 0 0-.62 1.458A7.7 7.7 0 0 0 9 7.558V11a1 1 0 0 0 1 1zm-6 0a1 1 0 0 0 1-1V8.558a1 1 0 0 0-1-1H4.612q0-.527.062-1.054.094-.558.31-.992.217-.434.559-.683.34-.279.868-.279V3q-.868 0-1.52.372a3.3 3.3 0 0 0-1.085.992 4.9 4.9 0 0 0-.62 1.458A7.7 7.7 0 0 0 3 7.558V11a1 1 0 0 0 1 1z" />
                                          </svg>
                                       </div>
                                    </div>
                                    <div class="card-body p-5 d-flex flex-column gap-5 position-relative">
                                       <div class="d-flex flex-column gap-5">
                                          <p class="mb-0">
                                             "As someone who was always bad with money, this app has been a lifesaver. It’s like having a
                                             <span class="text-primary">personal financial advisor</span>
                                             in my pocket. Tracking my spending has never been easier."
                                          </p>
                                       </div>
                                       <div class="d-flex flex-column gap-1">
                                          <span>
                                             <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" class="bi bi-star-fill text-warning" viewBox="0 0 16 16">
                                                <path
                                                   d="M3.612 15.443c-.386.198-.824-.149-.746-.592l.83-4.73L.173 6.765c-.329-.314-.158-.888.283-.95l4.898-.696L7.538.792c.197-.39.73-.39.927 0l2.184 4.327 4.898.696c.441.062.612.636.282.95l-3.522 3.356.83 4.73c.078.443-.36.79-.746.592L8 13.187l-4.389 2.256z" />
                                             </svg>
                                             <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" class="bi bi-star-fill text-warning" viewBox="0 0 16 16">
                                                <path
                                                   d="M3.612 15.443c-.386.198-.824-.149-.746-.592l.83-4.73L.173 6.765c-.329-.314-.158-.888.283-.95l4.898-.696L7.538.792c.197-.39.73-.39.927 0l2.184 4.327 4.898.696c.441.062.612.636.282.95l-3.522 3.356.83 4.73c.078.443-.36.79-.746.592L8 13.187l-4.389 2.256z" />
                                             </svg>
                                             <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" class="bi bi-star-fill text-warning" viewBox="0 0 16 16">
                                                <path
                                                   d="M3.612 15.443c-.386.198-.824-.149-.746-.592l.83-4.73L.173 6.765c-.329-.314-.158-.888.283-.95l4.898-.696L7.538.792c.197-.39.73-.39.927 0l2.184 4.327 4.898.696c.441.062.612.636.282.95l-3.522 3.356.83 4.73c.078.443-.36.79-.746.592L8 13.187l-4.389 2.256z" />
                                             </svg>
                                             <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" class="bi bi-star-fill text-warning" viewBox="0 0 16 16">
                                                <path
                                                   d="M3.612 15.443c-.386.198-.824-.149-.746-.592l.83-4.73L.173 6.765c-.329-.314-.158-.888.283-.95l4.898-.696L7.538.792c.197-.39.73-.39.927 0l2.184 4.327 4.898.696c.441.062.612.636.282.95l-3.522 3.356.83 4.73c.078.443-.36.79-.746.592L8 13.187l-4.389 2.256z" />
                                             </svg>
                                             <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" class="bi bi-star-half text-warning" viewBox="0 0 16 16">
                                                <path
                                                   d="M5.354 5.119 7.538.792A.52.52 0 0 1 8 .5c.183 0 .366.097.465.292l2.184 4.327 4.898.696A.54.54 0 0 1 16 6.32a.55.55 0 0 1-.17.445l-3.523 3.356.83 4.73c.078.443-.36.79-.746.592L8 13.187l-4.389 2.256a.5.5 0 0 1-.146.05c-.342.06-.668-.254-.6-.642l.83-4.73L.173 6.765a.55.55 0 0 1-.172-.403.6.6 0 0 1 .085-.302.51.51 0 0 1 .37-.245zM8 12.027a.5.5 0 0 1 .232.056l3.686 1.894-.694-3.957a.56.56 0 0 1 .162-.505l2.907-2.77-4.052-.576a.53.53 0 0 1-.393-.288L8.001 2.223 8 2.226z" />
                                             </svg>
                                          </span>
                                          <span class="text-dark">Emily R.</span>
                                       </div>
                                    </div>
                                 </div>
                              </div>
                              <div class="swiper-slide pt-4">
                                 <div class="card shadow-sm border-0">
                                    <div class="icon-shape icon-xxl rounded-circle bg-light-subtle mx-auto mt-n4">
                                       <div class="bg-white icon-shape icon-lg rounded-circle shadow-lg">
                                          <svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" fill="currentColor" class="bi bi-quote text-info" viewBox="0 0 16 16">
                                             <path
                                                d="M12 12a1 1 0 0 0 1-1V8.558a1 1 0 0 0-1-1h-1.388q0-.527.062-1.054.093-.558.31-.992t.559-.683q.34-.279.868-.279V3q-.868 0-1.52.372a3.3 3.3 0 0 0-1.085.992 4.9 4.9 0 0 0-.62 1.458A7.7 7.7 0 0 0 9 7.558V11a1 1 0 0 0 1 1zm-6 0a1 1 0 0 0 1-1V8.558a1 1 0 0 0-1-1H4.612q0-.527.062-1.054.094-.558.31-.992.217-.434.559-.683.34-.279.868-.279V3q-.868 0-1.52.372a3.3 3.3 0 0 0-1.085.992 4.9 4.9 0 0 0-.62 1.458A7.7 7.7 0 0 0 3 7.558V11a1 1 0 0 0 1 1z" />
                                          </svg>
                                       </div>
                                    </div>
                                    <div class="card-body p-5 d-flex flex-column gap-5 position-relative">
                                       <div class="d-flex flex-column gap-5">
                                          <p class="mb-0">
                                             "As someone who was always bad with money, this app has been a lifesaver. It’s like having a
                                             <span class="text-primary">personal financial advisor</span>
                                             in my pocket. Tracking my spending has never been easier."
                                          </p>
                                       </div>
                                       <div class="d-flex flex-column gap-1">
                                          <span>
                                             <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" class="bi bi-star-fill text-warning" viewBox="0 0 16 16">
                                                <path
                                                   d="M3.612 15.443c-.386.198-.824-.149-.746-.592l.83-4.73L.173 6.765c-.329-.314-.158-.888.283-.95l4.898-.696L7.538.792c.197-.39.73-.39.927 0l2.184 4.327 4.898.696c.441.062.612.636.282.95l-3.522 3.356.83 4.73c.078.443-.36.79-.746.592L8 13.187l-4.389 2.256z" />
                                             </svg>
                                             <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" class="bi bi-star-fill text-warning" viewBox="0 0 16 16">
                                                <path
                                                   d="M3.612 15.443c-.386.198-.824-.149-.746-.592l.83-4.73L.173 6.765c-.329-.314-.158-.888.283-.95l4.898-.696L7.538.792c.197-.39.73-.39.927 0l2.184 4.327 4.898.696c.441.062.612.636.282.95l-3.522 3.356.83 4.73c.078.443-.36.79-.746.592L8 13.187l-4.389 2.256z" />
                                             </svg>
                                             <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" class="bi bi-star-fill text-warning" viewBox="0 0 16 16">
                                                <path
                                                   d="M3.612 15.443c-.386.198-.824-.149-.746-.592l.83-4.73L.173 6.765c-.329-.314-.158-.888.283-.95l4.898-.696L7.538.792c.197-.39.73-.39.927 0l2.184 4.327 4.898.696c.441.062.612.636.282.95l-3.522 3.356.83 4.73c.078.443-.36.79-.746.592L8 13.187l-4.389 2.256z" />
                                             </svg>
                                             <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" class="bi bi-star-fill text-warning" viewBox="0 0 16 16">
                                                <path
                                                   d="M3.612 15.443c-.386.198-.824-.149-.746-.592l.83-4.73L.173 6.765c-.329-.314-.158-.888.283-.95l4.898-.696L7.538.792c.197-.39.73-.39.927 0l2.184 4.327 4.898.696c.441.062.612.636.282.95l-3.522 3.356.83 4.73c.078.443-.36.79-.746.592L8 13.187l-4.389 2.256z" />
                                             </svg>
                                             <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" class="bi bi-star-half text-warning" viewBox="0 0 16 16">
                                                <path
                                                   d="M5.354 5.119 7.538.792A.52.52 0 0 1 8 .5c.183 0 .366.097.465.292l2.184 4.327 4.898.696A.54.54 0 0 1 16 6.32a.55.55 0 0 1-.17.445l-3.523 3.356.83 4.73c.078.443-.36.79-.746.592L8 13.187l-4.389 2.256a.5.5 0 0 1-.146.05c-.342.06-.668-.254-.6-.642l.83-4.73L.173 6.765a.55.55 0 0 1-.172-.403.6.6 0 0 1 .085-.302.51.51 0 0 1 .37-.245zM8 12.027a.5.5 0 0 1 .232.056l3.686 1.894-.694-3.957a.56.56 0 0 1 .162-.505l2.907-2.77-4.052-.576a.53.53 0 0 1-.393-.288L8.001 2.223 8 2.226z" />
                                             </svg>
                                          </span>
                                          <span class="text-dark">Emily R.</span>
                                       </div>
                                    </div>
                                 </div>
                              </div>
                              <div class="swiper-slide pt-4">
                                 <div class="card shadow-sm border-0">
                                    <div class="icon-shape icon-xxl rounded-circle bg-light-subtle mx-auto mt-n4">
                                       <div class="bg-white icon-shape icon-lg rounded-circle shadow-lg">
                                          <svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" fill="currentColor" class="bi bi-quote text-info" viewBox="0 0 16 16">
                                             <path
                                                d="M12 12a1 1 0 0 0 1-1V8.558a1 1 0 0 0-1-1h-1.388q0-.527.062-1.054.093-.558.31-.992t.559-.683q.34-.279.868-.279V3q-.868 0-1.52.372a3.3 3.3 0 0 0-1.085.992 4.9 4.9 0 0 0-.62 1.458A7.7 7.7 0 0 0 9 7.558V11a1 1 0 0 0 1 1zm-6 0a1 1 0 0 0 1-1V8.558a1 1 0 0 0-1-1H4.612q0-.527.062-1.054.094-.558.31-.992.217-.434.559-.683.34-.279.868-.279V3q-.868 0-1.52.372a3.3 3.3 0 0 0-1.085.992 4.9 4.9 0 0 0-.62 1.458A7.7 7.7 0 0 0 3 7.558V11a1 1 0 0 0 1 1z" />
                                          </svg>
                                       </div>
                                    </div>
                                    <div class="card-body p-5 d-flex flex-column gap-5 position-relative">
                                       <div class="d-flex flex-column gap-5">
                                          <p class="mb-0">
                                             "As someone who was always bad with money, this app has been a lifesaver. It’s like having a
                                             <span class="text-primary">personal financial advisor</span>
                                             in my pocket. Tracking my spending has never been easier."
                                          </p>
                                       </div>
                                       <div class="d-flex flex-column gap-1">
                                          <span>
                                             <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" class="bi bi-star-fill text-warning" viewBox="0 0 16 16">
                                                <path
                                                   d="M3.612 15.443c-.386.198-.824-.149-.746-.592l.83-4.73L.173 6.765c-.329-.314-.158-.888.283-.95l4.898-.696L7.538.792c.197-.39.73-.39.927 0l2.184 4.327 4.898.696c.441.062.612.636.282.95l-3.522 3.356.83 4.73c.078.443-.36.79-.746.592L8 13.187l-4.389 2.256z" />
                                             </svg>
                                             <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" class="bi bi-star-fill text-warning" viewBox="0 0 16 16">
                                                <path
                                                   d="M3.612 15.443c-.386.198-.824-.149-.746-.592l.83-4.73L.173 6.765c-.329-.314-.158-.888.283-.95l4.898-.696L7.538.792c.197-.39.73-.39.927 0l2.184 4.327 4.898.696c.441.062.612.636.282.95l-3.522 3.356.83 4.73c.078.443-.36.79-.746.592L8 13.187l-4.389 2.256z" />
                                             </svg>
                                             <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" class="bi bi-star-fill text-warning" viewBox="0 0 16 16">
                                                <path
                                                   d="M3.612 15.443c-.386.198-.824-.149-.746-.592l.83-4.73L.173 6.765c-.329-.314-.158-.888.283-.95l4.898-.696L7.538.792c.197-.39.73-.39.927 0l2.184 4.327 4.898.696c.441.062.612.636.282.95l-3.522 3.356.83 4.73c.078.443-.36.79-.746.592L8 13.187l-4.389 2.256z" />
                                             </svg>
                                             <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" class="bi bi-star-fill text-warning" viewBox="0 0 16 16">
                                                <path
                                                   d="M3.612 15.443c-.386.198-.824-.149-.746-.592l.83-4.73L.173 6.765c-.329-.314-.158-.888.283-.95l4.898-.696L7.538.792c.197-.39.73-.39.927 0l2.184 4.327 4.898.696c.441.062.612.636.282.95l-3.522 3.356.83 4.73c.078.443-.36.79-.746.592L8 13.187l-4.389 2.256z" />
                                             </svg>
                                             <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" class="bi bi-star-half text-warning" viewBox="0 0 16 16">
                                                <path
                                                   d="M5.354 5.119 7.538.792A.52.52 0 0 1 8 .5c.183 0 .366.097.465.292l2.184 4.327 4.898.696A.54.54 0 0 1 16 6.32a.55.55 0 0 1-.17.445l-3.523 3.356.83 4.73c.078.443-.36.79-.746.592L8 13.187l-4.389 2.256a.5.5 0 0 1-.146.05c-.342.06-.668-.254-.6-.642l.83-4.73L.173 6.765a.55.55 0 0 1-.172-.403.6.6 0 0 1 .085-.302.51.51 0 0 1 .37-.245zM8 12.027a.5.5 0 0 1 .232.056l3.686 1.894-.694-3.957a.56.56 0 0 1 .162-.505l2.907-2.77-4.052-.576a.53.53 0 0 1-.393-.288L8.001 2.223 8 2.226z" />
                                             </svg>
                                          </span>
                                          <span class="text-dark">Emily R.</span>
                                       </div>
                                    </div>
                                 </div>
                              </div>
                              <!-- Add more slides as needed -->
                           </div>
                           <!-- Add Pagination -->
                           <div class="swiper-pagination"></div>
                           <!-- Add Navigation -->
                           <div class="swiper-navigation">
                              <div class="swiper-button-next"></div>
                              <div class="swiper-button-prev"></div>
                           </div>
                        </div>
                     </div>
                     <div class="col-12">
                        <div class="text-center">
                           <span>
                              Read more reviews on the
                              <a href="#!" class="border-bottom pb-1">App Store</a>
                              or
                              <a href="#!" class="border-bottom pb-1">Play Store</a>
                           </span>
                        </div>
                     </div>
                  </div>
               </div>
            </div>
         </section>
         <!--Reviews-->
         <!--Awards-->
         <div class="pb-xl-9 py-6" data-cue="zoomIn">
            <div class="container">
               <div class="row g-6">
                  <div class="col-md-4">
                     <div class="text-center">
                        <img src="./assets/images/mobile-app/award-apple.svg" alt="award apple" class="img-fluid" />
                     </div>
                  </div>
                  <div class="col-md-4">
                     <div class="text-center">
                        <img src="./assets/images/mobile-app/google-play-award.svg" alt="google play" class="img-fluid" />
                     </div>
                  </div>
                  <div class="col-md-4">
                     <div class="text-center">
                        <img src="./assets/images/mobile-app/apple-store-award.svg" alt="apple Store" class="img-fluid" />
                     </div>
                  </div>
               </div>
            </div>
         </div>
         <!--Awards-->
         <!--Call to action-->
         <section class="py-6" data-cue="fadeIn">
            <div class="container">
               <div
                  class="py-10 px-4 rounded-4"
                  style="
                     background: url(./assets/images/mobile-app/curvlines.svg), linear-gradient(180deg, #9b58ff 0%, #8837ff 47.92%, #7a20ff 100%);
                     background-position: center;
                     background-repeat: no-repeat;
                     background-size: cover;
                  ">
                  <div class="row mb-7">
                     <div class="col-xxl-6 offset-xxl-3 col-lg-8 offset-lg-2 col-12">
                        <div class="text-center d-flex flex-column gap-5">
                           <h2 class="mb-0 display-4 text-white-stable px-xl-5">Download Block App to get started</h2>
                        </div>
                     </div>
                  </div>
                  <div class="row align-items-center justify-content-center">
                     <div class="col-xxl-3 col-xl-4 col-lg-4 col-md-5 col-10">
                        <div class="align-items-center d-flex flex-row bg-body p-2 rounded-2 shadow-lg">
                           <div class="text-center">
                              <p class="mb-0 text-dark fw-semibold px-xxl-4 px-xl-7 px-lg-5">Download Block App</p>
                           </div>
                           <div class="">
                              <img src="./assets/images/mobile-app/qr-code.svg" alt="qr coed" class="icon-shape icon-xxxl" />
                           </div>
                        </div>
                     </div>
                  </div>
               </div>
            </div>
         </section>
         <!--Call to action-->
      </main>
      <footer class="pt-7">
   <div class="container">
      <!-- Footer 4 column -->
      <div class="row">
         <div class="col-lg-9 col-12">
            <div class="row" id="ft-links">
               <div class="col-lg-3 col-12">
                  <div class="position-relative">
                     <div class="mb-3 pb-2 d-flex justify-content-between border-bottom border-bottom-lg-0">
                        <h4>Service</h4>
                        <a class="d-block d-lg-none stretched-link text-body" data-bs-toggle="collapse" href="#collapseLanding" role="button" aria-expanded="true" aria-controls="collapseLanding">
                           <i class="bi bi-chevron-down"></i>
                        </a>
                     </div>
                     <div class="d-lg-block collapse show" id="collapseLanding" data-bs-parent="#ft-links" style="">
                        <ul class="list-unstyled mb-0 py-3 py-lg-0">
                           <li class="mb-2">
                              <a href="./index.html" class="text-decoration-none text-reset">Web App Development</a>
                           </li>
                           <li class="mb-2">
                              <a href="#!" class="text-decoration-none text-reset">Front End Development</a>
                           </li>
                           <li class="mb-2">
                              <a href="#!" class="text-decoration-none text-reset">MVP Development</a>
                           </li>
                           <li class="mb-2">
                              <a href="#!" class="text-decoration-none text-reset">Digital Marketing</a>
                           </li>
                           <li class="mb-2">
                              <a href="#!" class="text-decoration-none text-reset">Content Writing</a>
                           </li>
                        </ul>
                     </div>
                  </div>
               </div>
               <div class="col-lg-3 col-12">
                  <div>
                     <div class="mb-3 pb-2 d-flex justify-content-between border-bottom border-bottom-lg-0 position-relative">
                        <h4>About us</h4>
                        <a
                           class="d-block d-lg-none stretched-link text-body collapsed"
                           data-bs-toggle="collapse"
                           href="#collapseAccounts"
                           role="button"
                           aria-expanded="false"
                           aria-controls="collapseAccounts">
                           <i class="bi bi-chevron-down"></i>
                        </a>
                     </div>
                     <div class="collapse d-lg-block" id="collapseAccounts" data-bs-parent="#ft-links">
                        <ul class="list-unstyled mb-0 py-3 py-lg-0">
                           <li class="mb-2">
                              <a href="#!" class="text-decoration-none text-reset">Case Studies</a>
                           </li>
                           <li class="mb-2">
                              <a href="#!" class="text-decoration-none text-reset">Blog</a>
                           </li>
                           <li class="mb-2">
                              <a href="#!" class="text-decoration-none text-reset">Services</a>
                           </li>
                           <li class="mb-2">
                              <a href="#!" class="text-decoration-none text-reset">About</a>
                           </li>
                           <li class="mb-2">
                              <a href="#!" class="text-decoration-none text-reset">Career</a>
                           </li>
                        </ul>
                     </div>
                  </div>
               </div>
               <div class="col-lg-3 col-12">
                  <div class="mb-3 pb-2 d-flex justify-content-between border-bottom border-bottom-lg-0 position-relative">
                     <h4>Technology</h4>
                     <a
                        class="d-block d-lg-none stretched-link text-body collapsed"
                        data-bs-toggle="collapse"
                        href="#collapseResources"
                        role="button"
                        aria-expanded="false"
                        aria-controls="collapseResources">
                        <i class="bi bi-chevron-down"></i>
                     </a>
                  </div>
                  <div class="collapse d-lg-block" id="collapseResources" data-bs-parent="#ft-links">
                     <ul class="list-unstyled mb-0 py-3 py-lg-0">
                        <li class="mb-2">
                           <a href="./docs/index.html" class="text-decoration-none text-reset">Next.js</a>
                        </li>
                        <li class="mb-2">
                           <a href="#!" class="text-decoration-none text-reset">Sanity</a>
                        </li>
                        <li class="mb-2">
                           <a href="./changelog.html" class="text-decoration-none text-reset">Content ful</a>
                        </li>
                        <li class="mb-2">
                           <a href="#!" class="text-decoration-none text-reset">Vercel</a>
                        </li>
                        <li class="mb-2">
                           <a href="#!" class="text-decoration-none text-reset">Netlify</a>
                        </li>
                     </ul>
                  </div>
               </div>
               <div class="col-lg-3 col-12">
                  <div class="mb-3 pb-2 d-flex justify-content-between border-bottom border-bottom-lg-0 position-relative">
                     <h4>Locations</h4>
                     <a
                        class="d-block d-lg-none stretched-link text-body collapsed"
                        data-bs-toggle="collapse"
                        href="#collapseLocations"
                        role="button"
                        aria-expanded="false"
                        aria-controls="collapseLocations">
                        <i class="bi bi-chevron-down"></i>
                     </a>
                  </div>
                  <div class="collapse d-lg-block" id="collapseLocations" data-bs-parent="#ft-links">
                     <ul class="list-unstyled mb-0 py-3 py-lg-0">
                        <li class="mb-2">
                           <a href="./docs/index.html" class="text-decoration-none text-reset">India</a>
                        </li>
                        <li class="mb-2">
                           <a href="#!" class="text-decoration-none text-reset">Australia</a>
                        </li>
                        <li class="mb-2">
                           <a href="./changelog.html" class="text-decoration-none text-reset">Brazil</a>
                        </li>
                        <li class="mb-2">
                           <a href="#!" class="text-decoration-none text-reset">Canada</a>
                        </li>
                     </ul>
                  </div>
               </div>
            </div>
         </div>
         <div class="col-lg-3 col-12">
            <div class="me-7">
               <h4 class="mb-4">Headquarters</h4>
               <p class="text-body-secondary">Codescandy, 412, Residency Rd, Shanthala Nagar, Ashok Nagar, Bengaluru, Karnataka, India 560025</p>
            </div>
         </div>
      </div>
   </div>
   <div class="container mt-7 pt-lg-7 pb-4">
      <div class="row align-items-center">
         <div class="col-md-3">
            <a class="mb-4 mb-lg-0 d-block text-inverse" href="../index.html"><img src="./assets/images/logo/logo.svg" alt="" /></a>
         </div>
         <div class="col-md-9 col-lg-6">
            <div class="small mb-3 mb-lg-0 text-lg-center">
               Copyright © 2024

               <span class="text-primary"><a href="#">Block Bootstrap 5 Theme</a></span>
               | Designed by
               <span class="text-primary"><a href="#">CodesCandy</a></span>
            </div>
         </div>
         <div class="col-lg-3">
            <div class="text-lg-end d-flex align-items-center justify-content-lg-end">
               <div class="dropdown">
                  <button class="btn btn-light btn-icon rounded-circle d-flex align-items-center" type="button" aria-expanded="false" data-bs-toggle="dropdown" aria-label="Toggle theme (auto)">
                     <i class="bi theme-icon-active lh-1"><i class="bi theme-icon bi-sun-fill"></i></i>
                     <span class="visually-hidden bs-theme-text">Toggle theme</span>
                  </button>
                  <ul class="dropdown-menu dropdown-menu-end shadow" aria-labelledby="bs-theme-text">
                     <li>
                        <button type="button" class="dropdown-item d-flex align-items-center active" data-bs-theme-value="light" aria-pressed="true">
                           <i class="bi theme-icon bi-sun-fill"></i>
                           <span class="ms-2">Light</span>
                        </button>
                     </li>
                     <li>
                        <button type="button" class="dropdown-item d-flex align-items-center" data-bs-theme-value="dark" aria-pressed="false">
                           <i class="bi theme-icon bi-moon-stars-fill"></i>
                           <span class="ms-2">Dark</span>
                        </button>
                     </li>
                     <li>
                        <button type="button" class="dropdown-item d-flex align-items-center" data-bs-theme-value="auto" aria-pressed="false">
                           <i class="bi theme-icon bi-circle-half"></i>
                           <span class="ms-2">Auto</span>
                        </button>
                     </li>
                  </ul>
               </div>
               <div class="ms-3 d-flex gap-2">
                  <a href="#!" class="btn btn-instagram btn-light btn-icon">
                     <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-instagram" viewBox="0 0 16 16">
                        <path
                           d="M8 0C5.829 0 5.556.01 4.703.048 3.85.088 3.269.222 2.76.42a3.917 3.917 0 0 0-1.417.923A3.927 3.927 0 0 0 .42 2.76C.222 3.268.087 3.85.048 4.7.01 5.555 0 5.827 0 8.001c0 2.172.01 2.444.048 3.297.04.852.174 1.433.372 1.942.205.526.478.972.923 1.417.444.445.89.719 1.416.923.51.198 1.09.333 1.942.372C5.555 15.99 5.827 16 8 16s2.444-.01 3.298-.048c.851-.04 1.434-.174 1.943-.372a3.916 3.916 0 0 0 1.416-.923c.445-.445.718-.891.923-1.417.197-.509.332-1.09.372-1.942C15.99 10.445 16 10.173 16 8s-.01-2.445-.048-3.299c-.04-.851-.175-1.433-.372-1.941a3.926 3.926 0 0 0-.923-1.417A3.911 3.911 0 0 0 13.24.42c-.51-.198-1.092-.333-1.943-.372C10.443.01 10.172 0 7.998 0h.003zm-.717 1.442h.718c2.136 0 2.389.007 3.232.046.78.035 1.204.166 1.486.275.373.145.64.319.92.599.28.28.453.546.598.92.11.281.24.705.275 1.485.039.843.047 1.096.047 3.231s-.008 2.389-.047 3.232c-.035.78-.166 1.203-.275 1.485a2.47 2.47 0 0 1-.599.919c-.28.28-.546.453-.92.598-.28.11-.704.24-1.485.276-.843.038-1.096.047-3.232.047s-2.39-.009-3.233-.047c-.78-.036-1.203-.166-1.485-.276a2.478 2.478 0 0 1-.92-.598 2.48 2.48 0 0 1-.6-.92c-.109-.281-.24-.705-.275-1.485-.038-.843-.046-1.096-.046-3.233 0-2.136.008-2.388.046-3.231.036-.78.166-1.204.276-1.486.145-.373.319-.64.599-.92.28-.28.546-.453.92-.598.282-.11.705-.24 1.485-.276.738-.034 1.024-.044 2.515-.045v.002zm4.988 1.328a.96.96 0 1 0 0 1.92.96.96 0 0 0 0-1.92zm-4.27 1.122a4.109 4.109 0 1 0 0 8.217 4.109 4.109 0 0 0 0-8.217zm0 1.441a2.667 2.667 0 1 1 0 5.334 2.667 2.667 0 0 1 0-5.334z"></path>
                     </svg>
                  </a>
                  <a href="#!" class="btn btn-facebook btn-icon">
                     <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-facebook" viewBox="0 0 16 16">
                        <path
                           d="M16 8.049c0-4.446-3.582-8.05-8-8.05C3.58 0-.002 3.603-.002 8.05c0 4.017 2.926 7.347 6.75 7.951v-5.625h-2.03V8.05H6.75V6.275c0-2.017 1.195-3.131 3.022-3.131.876 0 1.791.157 1.791.157v1.98h-1.009c-.993 0-1.303.621-1.303 1.258v1.51h2.218l-.354 2.326H9.25V16c3.824-.604 6.75-3.934 6.75-7.951z"></path>
                     </svg>
                  </a>
                  <a href="#!" class="btn btn-twitter btn-icon">
                     <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-twitter" viewBox="0 0 16 16">
                        <path
                           d="M5.026 15c6.038 0 9.341-5.003 9.341-9.334 0-.14 0-.282-.006-.422A6.685 6.685 0 0 0 16 3.542a6.658 6.658 0 0 1-1.889.518 3.301 3.301 0 0 0 1.447-1.817 6.533 6.533 0 0 1-2.087.793A3.286 3.286 0 0 0 7.875 6.03a9.325 9.325 0 0 1-6.767-3.429 3.289 3.289 0 0 0 1.018 4.382A3.323 3.323 0 0 1 .64 6.575v.045a3.288 3.288 0 0 0 2.632 3.218 3.203 3.203 0 0 1-.865.115 3.23 3.23 0 0 1-.614-.057 3.283 3.283 0 0 0 3.067 2.277A6.588 6.588 0 0 1 .78 13.58a6.32 6.32 0 0 1-.78-.045A9.344 9.344 0 0 0 5.026 15z"></path>
                     </svg>
                  </a>
               </div>
            </div>
         </div>
      </div>
   </div>
</footer>
<div class="btn-scroll-top">
   <svg class="progress-square svg-content" width="100%" height="100%" viewBox="0 0 40 40">
      <path d="M8 1H32C35.866 1 39 4.13401 39 8V32C39 35.866 35.866 39 32 39H8C4.13401 39 1 35.866 1 32V8C1 4.13401 4.13401 1 8 1Z" />
   </svg>
</div>
 <!-- Libs JS -->
<script src="./assets/libs/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
<script src="./assets/libs/simplebar/dist/simplebar.min.js"></script>
<script src="./assets/libs/headhesive/dist/headhesive.min.js"></script>

<!-- Theme JS -->
<script src="./assets/js/theme.min.js"></script>


      <!--Swiper JS -->
      <script src="./assets/libs/swiper/swiper-bundle.min.js"></script>
      <script src="./assets/js/vendors/swiper.js"></script>
      <script src="./assets/libs/scrollcue/scrollCue.min.js"></script>
      <script src="./assets/js/vendors/scrollcue.js"></script>
   </body>
</html>
