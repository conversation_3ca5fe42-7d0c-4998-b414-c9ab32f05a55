<!doctype html>
<html lang="en">
   <head>
      <!-- Required meta tags -->
<meta charset="utf-8" />
<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />

      <link rel="stylesheet" href="./assets/libs/swiper/swiper-bundle.min.css" />
      <!-- Favicon icon-->
<link rel="apple-touch-icon" sizes="180x180" href="./assets/images/favicon/apple-touch-icon.png" />
<link rel="icon" type="image/png" sizes="32x32" href="./assets/images/favicon/favicon-32x32.png" />
<link rel="icon" type="image/png" sizes="16x16" href="./assets/images/favicon/favicon-16x16.png" />
<link rel="manifest" href="./assets/images/favicon/site.webmanifest" />
<link rel="mask-icon" href="./assets/images/favicon/block-safari-pinned-tab.svg" color="#8b3dff" />
<link rel="shortcut icon" href="./assets/images/favicon/favicon.ico" />
<meta name="msapplication-TileColor" content="#8b3dff" />
<meta name="msapplication-config" content="./assets/images/favicon/tile.xml" />

<!-- Color modes -->
<script src="./assets/js/vendors/color-modes.js"></script>

<!-- Libs CSS -->
<link href="./assets/libs/simplebar/dist/simplebar.min.css" rel="stylesheet" />
<link href="./assets/libs/bootstrap-icons/font/bootstrap-icons.min.css" rel="stylesheet" />

<!-- Scroll Cue -->
<link rel="stylesheet" href="./assets/libs/scrollcue/scrollCue.css" />

<!-- Box icons -->
<link rel="stylesheet" href="./assets/fonts/css/boxicons.min.css" />

<!-- Theme CSS -->
<link rel="stylesheet" href="./assets/css/theme.min.css">

      <title>Events - Responsive Website Template | Block</title>
   </head>

   <body>
      <!-- Navbar -->
<header>
   <nav class="navbar navbar-expand-lg navbar-light w-100">
      <div class="container px-3">
         <a class="navbar-brand" href="./index.html"><img src="./assets/images/logo/logo.svg" alt /></a>
         <button class="navbar-toggler offcanvas-nav-btn" type="button">
            <i class="bi bi-list"></i>
         </button>
         <div class="offcanvas offcanvas-start offcanvas-nav" style="width: 20rem">
            <div class="offcanvas-header">
               <a href="./index.html" class="text-inverse"><img src="./assets/images/logo/logo.svg" alt /></a>
               <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close"></button>
            </div>
            <div class="offcanvas-body pt-0 align-items-center">
               <ul class="navbar-nav mx-auto align-items-lg-center">
                  <li class="nav-item">
                     <a class="nav-link" href="./home.html">Home</a>
                  </li>
                  <li class="nav-item">
                     <a class="nav-link" href="./scan-pay.html">Scan & Pay</a>
                  </li>
                  <li class="nav-item">
                     <a class="nav-link" href="./service.html">Services</a>
                  </li>
                  <li class="nav-item">
                     <a class="nav-link" href="./contact.html">Contact</a>
                  </li>
                  <li class="nav-item">
                     <a class="nav-link" href="./become-an-agent.html">Become An Agent</a>
                  </li>
               </ul>
               <div class="mt-3 mt-lg-0 d-flex align-items-center">
                  <a href="./signin.html" class="btn btn-light mx-2">Login</a>
                  <a href="https://play.google.com/store/apps/details?id=com.qsoft.aidapay&hl=en&pli=1" class="btn btn-primary">Create account</a>
               </div>
            </div>
         </div>
      </div>
   </nav>
</header>

      <main>
         <div class="pattern-square"></div>
         <!--Pageheader start-->
         <section class="py-5 py-lg-8">
            <div class="container">
               <div class="row">
                  <div class="col-lg-6 offset-lg-3 col-12">
                     <div class="text-center">
                        <h1 class="mb-3">Worldwide conferences, meetups, events, and webinars</h1>
                        <p class="mb-0">Meet our tech or business teams to talk about the freedom to build infinitely composable content.</p>
                     </div>
                  </div>
               </div>
            </div>
         </section>
         <!--Pageheader end-->
         <!--Online start-->

         <section class="mb-xl-9 my-5">
            <div class="container">
               <div class="row">
                  <div class="col-lg-12">
                     <div
                        class="swiper-container swiper"
                        id="swiper-1"
                        data-pagination-type=""
                        data-speed="400"
                        data-space-between="100"
                        data-pagination="true"
                        data-navigation="false"
                        data-autoplay="true"
                        data-autoplay-delay="3000"
                        data-breakpoints='{"480": {"slidesPerView": 1}, "768": {"slidesPerView": 1}, "1024": {"slidesPerView": 1}}'>
                        <div class="swiper-wrapper pb-6">
                           <div class="swiper-slide">
                              <div class="card shadow-sm overflow-hidden">
                                 <div class="row g-0">
                                    <div class="col-xl-6 col-md-6">
                                       <div class="card-body h-100 d-flex align-items-start flex-column p-lg-7">
                                          <div class="mb-3">
                                             <small class="text-uppercase fw-semibold ls-md">Online</small>
                                             <h2 class="mb-0 mt-3"><a href="#" class="text-reset">React - Next.js developers events and meetup</a></h2>
                                          </div>
                                          <div class="mb-5">
                                             <small class="me-2">June 22, 2024</small>
                                             <small>1:00PM EDT</small>
                                          </div>
                                          <div class="mt-auto">
                                             <a href="./event-single.html" class="icon-link icon-link-hover card-link">
                                                Mode Details
                                                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" class="bi bi-arrow-right" viewBox="0 0 16 16">
                                                   <path
                                                      fill-rule="evenodd"
                                                      d="M1 8a.5.5 0 0 1 .5-.5h11.793l-3.147-3.146a.5.5 0 0 1 .708-.708l4 4a.5.5 0 0 1 0 .708l-4 4a.5.5 0 0 1-.708-.708L13.293 8.5H1.5A.5.5 0 0 1 1 8z"></path>
                                                </svg>
                                             </a>
                                          </div>
                                       </div>
                                    </div>
                                    <div
                                       class="col-md-6"
                                       style="
                                          background-image: url(./assets/images/event/event-img-1.jpg);
                                          background-size: cover;
                                          background-repeat: no-repeat;
                                          background-position: center;
                                          min-height: 15rem;
                                       ">
                                       <!-- for mobile img-->
                                    </div>
                                 </div>
                              </div>
                           </div>
                           <div class="swiper-slide">
                              <div class="card shadow-sm overflow-hidden">
                                 <div class="row g-0">
                                    <div class="col-xl-6 col-md-6">
                                       <div class="card-body h-100 d-flex align-items-start flex-column p-lg-7">
                                          <div class="mb-4">
                                             <small class="text-uppercase fw-semibold ls-md">Online</small>
                                             <h2 class="mb-0 mt-3"><a href="#" class="text-reset">React - Next.js developers events and meetup</a></h2>
                                          </div>
                                          <div class="mb-5">
                                             <small class="me-2">June 22, 2024</small>
                                             <small>1:00PM EDT</small>
                                          </div>
                                          <div class="mt-auto">
                                             <a href="./event-single.html" class="icon-link icon-link-hover card-link">
                                                Mode Details
                                                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" class="bi bi-arrow-right" viewBox="0 0 16 16">
                                                   <path
                                                      fill-rule="evenodd"
                                                      d="M1 8a.5.5 0 0 1 .5-.5h11.793l-3.147-3.146a.5.5 0 0 1 .708-.708l4 4a.5.5 0 0 1 0 .708l-4 4a.5.5 0 0 1-.708-.708L13.293 8.5H1.5A.5.5 0 0 1 1 8z"></path>
                                                </svg>
                                             </a>
                                          </div>
                                       </div>
                                    </div>
                                    <div
                                       class="col-md-6"
                                       style="
                                          background-image: url(./assets/images/event/event-img-2.jpg);
                                          background-size: cover;
                                          background-repeat: no-repeat;
                                          background-position: center;
                                          min-height: 15rem;
                                       ">
                                       <!-- for mobile img-->
                                    </div>
                                 </div>
                              </div>
                           </div>
                           <div class="swiper-slide">
                              <div class="card shadow-sm overflow-hidden">
                                 <div class="row g-0">
                                    <div class="col-xl-6 col-md-6">
                                       <div class="card-body h-100 d-flex align-items-start flex-column p-lg-7">
                                          <div class="mb-4">
                                             <small class="text-uppercase fw-semibold ls-md">Online</small>
                                             <h2 class="mb-0 mt-3"><a href="#" class="text-reset">React - Next.js developers events and meetup</a></h2>
                                          </div>
                                          <div class="mb-5">
                                             <small class="me-2">June 22, 2024</small>
                                             <small>1:00PM EDT</small>
                                          </div>
                                          <div class="mt-auto">
                                             <a href="./event-single.html" class="icon-link icon-link-hover card-link">
                                                Mode Details
                                                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" class="bi bi-arrow-right" viewBox="0 0 16 16">
                                                   <path
                                                      fill-rule="evenodd"
                                                      d="M1 8a.5.5 0 0 1 .5-.5h11.793l-3.147-3.146a.5.5 0 0 1 .708-.708l4 4a.5.5 0 0 1 0 .708l-4 4a.5.5 0 0 1-.708-.708L13.293 8.5H1.5A.5.5 0 0 1 1 8z"></path>
                                                </svg>
                                             </a>
                                          </div>
                                       </div>
                                    </div>
                                    <div
                                       class="col-md-6"
                                       style="
                                          background-image: url(./assets/images/event/event-img-3.jpg);
                                          background-size: cover;
                                          background-repeat: no-repeat;
                                          background-position: center;
                                          min-height: 15rem;
                                       ">
                                       <!-- for mobile img-->
                                    </div>
                                 </div>
                              </div>
                           </div>
                           <!-- Add more slides as needed -->
                        </div>
                        <!-- Add Pagination -->
                        <div class="swiper-pagination"></div>
                        <!-- Add Navigation -->
                        <div class="swiper-navigation">
                           <div class="swiper-button-next"></div>
                           <div class="swiper-button-prev"></div>
                        </div>
                     </div>
                  </div>
               </div>
            </div>
         </section>
         <!--Online end-->

         <!--All events start-->
         <section class="mb-xl-9 my-5">
            <div class="container">
               <div class="row mb-4">
                  <div class="col-lg-12">
                     <div class="mb-4">
                        <h3 class="mb-4">All Events</h3>
                     </div>
                  </div>
                  <div class="col-lg-6 col-md-10 col-12">
                     <div class="row g-3 align-items-center">
                        <div class="col-lg-6 col-md-6 col-12">
                           <label for="eventList" class="form-label visually-hidden">Search Category</label>
                           <select class="form-select" id="eventList">
                              <option selected disabled value="">Type of event</option>
                              <option value="Conferences">Conferences</option>
                              <option value="Online">Online</option>
                              <option value="Livestream">Livestream</option>
                              <option value="Video">Video</option>
                           </select>
                        </div>
                     </div>
                  </div>
               </div>

               <div class="row g-5">
                  <div class="col-md-6">
                     <div class="card shadow-sm h-100 border-0 card-lift overflow-hidden">
                        <div class="row h-100 g-0">
                           <a
                              href="./event-single.html"
                              class="col-lg-5 col-md-12"
                              style="
                                 background-image: url(./assets/images/event/event-img-2.jpg);
                                 background-size: cover;
                                 background-repeat: no-repeat;
                                 background-position: center;
                                 min-height: 13rem;
                              "></a>
                           <div class="col-lg-7 col-md-12">
                              <div
                                 class="card-body h-100 d-flex align-items-start flex-column border rounded-end-lg-3 rounded-bottom-3 rounded-top-0 rounded-start-lg-0 border-start-lg-0 border-top-0 border-top-lg">
                                 <div class="mb-5">
                                    <small class="text-uppercase fw-semibold ls-md">Conference</small>
                                    <h4 class="my-2"><a href="./event-single.html" class="text-reset">How to build a blog with Astro and Contentful</a></h4>
                                    <small>July 2, 2024</small>
                                 </div>
                                 <div class="mt-auto">
                                    <small class="me-2">9:00AM EDT</small>
                                    <small>Germany</small>
                                 </div>
                              </div>
                           </div>
                        </div>
                     </div>
                  </div>
                  <div class="col-md-6">
                     <div class="card shadow-sm h-100 border-0 card-lift overflow-hidden">
                        <div class="row g-0 h-100">
                           <a
                              href="./event-single.html"
                              class="col-lg-5 col-md-12"
                              style="
                                 background-image: url(./assets/images/event/event-img-3.jpg);
                                 background-size: cover;
                                 background-repeat: no-repeat;
                                 background-position: center;
                                 min-height: 13rem;
                              "></a>
                           <div class="col-lg-7 col-md-12">
                              <div
                                 class="card-body h-100 d-flex align-items-start flex-column border rounded-end-lg-3 rounded-bottom-3 rounded-top-0 rounded-start-lg-0 border-start-lg-0 border-top-0 border-top-lg">
                                 <div class="mb-5">
                                    <small class="text-uppercase fw-semibold ls-md">Conference</small>
                                    <h4 class="my-2"><a href="event-single.html" class="text-reset">A look at building with Astro template</a></h4>
                                    <small>June 28, 2024</small>
                                 </div>
                                 <div class="mt-auto">
                                    <small class="me-2">9:00AM EDT</small>
                                    <small>India</small>
                                 </div>
                              </div>
                           </div>
                        </div>
                     </div>
                  </div>
                  <div class="col-md-6">
                     <div class="card shadow-sm h-100 border-0 card-lift overflow-hidden">
                        <div class="row h-100 g-0">
                           <a
                              href="./event-single.html"
                              class="col-lg-5 col-md-12"
                              style="
                                 background-image: url(./assets/images/event/event-img-4.jpg);
                                 background-size: cover;
                                 background-repeat: no-repeat;
                                 background-position: center;
                                 min-height: 13rem;
                              "></a>
                           <div class="col-lg-7 col-md-12">
                              <div
                                 class="card-body h-100 d-flex align-items-start flex-column border rounded-end-lg-3 rounded-bottom-3 rounded-top-0 rounded-start-lg-0 border-start-lg-0 border-top-0 border-top-lg">
                                 <div class="mb-5">
                                    <small class="text-uppercase fw-semibold ls-md">Online</small>
                                    <h4 class="my-2"><a href="event-single.html" class="text-reset">Make a blog with Next.js, React, and Sanity</a></h4>
                                    <small>June 26, 2024</small>
                                 </div>
                                 <div class="mt-auto">
                                    <small class="me-2">9:00AM EDT</small>
                                    <small>Dubai</small>
                                 </div>
                              </div>
                           </div>
                        </div>
                     </div>
                  </div>
                  <div class="col-md-6">
                     <div class="card shadow-sm h-100 border-0 card-lift overflow-hidden">
                        <div class="row g-0 h-100">
                           <a
                              href="./event-single.html"
                              class="col-lg-5 col-md-12"
                              style="
                                 background-image: url(./assets/images/event/event-img-5.jpg);
                                 background-size: cover;
                                 background-repeat: no-repeat;
                                 background-position: center;
                                 min-height: 13rem;
                              "></a>
                           <div class="col-lg-7 col-md-12">
                              <div
                                 class="card-body h-100 d-flex align-items-start flex-column border rounded-end-lg-3 rounded-bottom-3 rounded-top-0 rounded-start-lg-0 border-start-lg-0 border-top-0 border-top-lg">
                                 <div class="mb-5">
                                    <small class="text-uppercase fw-semibold ls-md">Livestream</small>
                                    <h4 class="my-2"><a href="./event-single.html" class="text-reset">Using Contentful CMS with Next.js</a></h4>
                                    <small>June 22, 2024</small>
                                 </div>
                                 <div class="mt-auto">
                                    <small class="me-2">9:00AM EDT</small>
                                    <small>London</small>
                                 </div>
                              </div>
                           </div>
                        </div>
                     </div>
                  </div>
               </div>
            </div>
         </section>
         <!--All events end-->
         <!--Webinar start-->
         <section class="mb-xl-9 my-5">
            <div class="container">
               <div class="row">
                  <div class="col-lg-6">
                     <h3 class="mb-4">Webinar Video on-demand</h3>
                  </div>
               </div>
               <div class="row g-5">
                  <div class="col-lg-4 col-md-6">
                     <div class="card border-0 shadow-sm h-100 card-lift">
                        <figure>
                           <a href="./event-single.html">
                              <img src="./assets/images/event/event-img-2.jpg" alt="event" class="card-img-top" />
                           </a>
                        </figure>

                        <div class="card-body h-100 d-flex align-items-start flex-column border rounded-bottom-3 border-top-0">
                           <div class="mb-5">
                              <small class="text-uppercase fw-semibold ls-md">Webinar</small>
                              <h4 class="my-2"><a href="./event-single.html" class="text-reset">How to build a blog with Astro and Contentful</a></h4>
                              <small>June 22, 2024</small>
                           </div>
                           <div class="d-flex justify-content-between w-100 mt-auto">
                              <small>9:00AM EDT</small>
                              <small>Germany</small>
                           </div>
                        </div>
                     </div>
                  </div>
                  <div class="col-lg-4 col-md-6">
                     <div class="card border-0 shadow-sm h-100 card-lift">
                        <figure>
                           <a href="./event-single.html">
                              <img src="./assets/images/event/event-img-5.jpg" alt="event" class="card-img-top" />
                           </a>
                        </figure>

                        <div class="card-body h-100 d-flex align-items-start flex-column border rounded-bottom-3 border-top-0">
                           <div class="mb-5">
                              <small class="text-uppercase fw-semibold ls-md">Webinar</small>
                              <h4 class="my-2"><a href="./event-single.html" class="text-reset">Astro components & the basic building blocks</a></h4>
                              <small>June 22, 2024</small>
                           </div>
                           <div class="d-flex justify-content-between w-100 mt-auto">
                              <small>9:00AM EDT</small>
                              <small>Germany</small>
                           </div>
                        </div>
                     </div>
                  </div>
                  <div class="col-lg-4 col-md-6">
                     <div class="card border-0 shadow-sm h-100 card-lift">
                        <figure>
                           <a href="./event-single.html">
                              <img src="./assets/images/event/event-img-4.jpg" alt="event" class="card-img-top" />
                           </a>
                        </figure>

                        <div class="card-body h-100 d-flex align-items-start flex-column border rounded-bottom-3 border-top-0">
                           <div class="mb-5">
                              <small class="text-uppercase fw-semibold ls-md">Webinar</small>
                              <h4 class="my-2"><a href="./event-single.html" class="text-reset">Using Contentful CMS with Next.js</a></h4>
                              <small>June 22, 2024</small>
                           </div>
                           <div class="d-flex justify-content-between w-100 mt-auto">
                              <small>9:00AM EDT</small>
                              <small>Germany</small>
                           </div>
                        </div>
                     </div>
                  </div>
               </div>
            </div>
         </section>
         <!--Webinar end-->
      </main>
      <footer class="pt-7">
   <div class="container">
      <!-- Footer 4 column -->
      <div class="row">
         <div class="col-lg-9 col-12">
            <div class="row" id="ft-links">
               <div class="col-lg-3 col-12">
                  <div class="position-relative">
                     <div class="mb-3 pb-2 d-flex justify-content-between border-bottom border-bottom-lg-0">
                        <h4>Service</h4>
                        <a class="d-block d-lg-none stretched-link text-body" data-bs-toggle="collapse" href="#collapseLanding" role="button" aria-expanded="true" aria-controls="collapseLanding">
                           <i class="bi bi-chevron-down"></i>
                        </a>
                     </div>
                     <div class="d-lg-block collapse show" id="collapseLanding" data-bs-parent="#ft-links" style="">
                        <ul class="list-unstyled mb-0 py-3 py-lg-0">
                           <li class="mb-2">
                              <a href="./index.html" class="text-decoration-none text-reset">Web App Development</a>
                           </li>
                           <li class="mb-2">
                              <a href="#!" class="text-decoration-none text-reset">Front End Development</a>
                           </li>
                           <li class="mb-2">
                              <a href="#!" class="text-decoration-none text-reset">MVP Development</a>
                           </li>
                           <li class="mb-2">
                              <a href="#!" class="text-decoration-none text-reset">Digital Marketing</a>
                           </li>
                           <li class="mb-2">
                              <a href="#!" class="text-decoration-none text-reset">Content Writing</a>
                           </li>
                        </ul>
                     </div>
                  </div>
               </div>
               <div class="col-lg-3 col-12">
                  <div>
                     <div class="mb-3 pb-2 d-flex justify-content-between border-bottom border-bottom-lg-0 position-relative">
                        <h4>About us</h4>
                        <a
                           class="d-block d-lg-none stretched-link text-body collapsed"
                           data-bs-toggle="collapse"
                           href="#collapseAccounts"
                           role="button"
                           aria-expanded="false"
                           aria-controls="collapseAccounts">
                           <i class="bi bi-chevron-down"></i>
                        </a>
                     </div>
                     <div class="collapse d-lg-block" id="collapseAccounts" data-bs-parent="#ft-links">
                        <ul class="list-unstyled mb-0 py-3 py-lg-0">
                           <li class="mb-2">
                              <a href="#!" class="text-decoration-none text-reset">Case Studies</a>
                           </li>
                           <li class="mb-2">
                              <a href="#!" class="text-decoration-none text-reset">Blog</a>
                           </li>
                           <li class="mb-2">
                              <a href="#!" class="text-decoration-none text-reset">Services</a>
                           </li>
                           <li class="mb-2">
                              <a href="#!" class="text-decoration-none text-reset">About</a>
                           </li>
                           <li class="mb-2">
                              <a href="#!" class="text-decoration-none text-reset">Career</a>
                           </li>
                        </ul>
                     </div>
                  </div>
               </div>
               <div class="col-lg-3 col-12">
                  <div class="mb-3 pb-2 d-flex justify-content-between border-bottom border-bottom-lg-0 position-relative">
                     <h4>Technology</h4>
                     <a
                        class="d-block d-lg-none stretched-link text-body collapsed"
                        data-bs-toggle="collapse"
                        href="#collapseResources"
                        role="button"
                        aria-expanded="false"
                        aria-controls="collapseResources">
                        <i class="bi bi-chevron-down"></i>
                     </a>
                  </div>
                  <div class="collapse d-lg-block" id="collapseResources" data-bs-parent="#ft-links">
                     <ul class="list-unstyled mb-0 py-3 py-lg-0">
                        <li class="mb-2">
                           <a href="./docs/index.html" class="text-decoration-none text-reset">Next.js</a>
                        </li>
                        <li class="mb-2">
                           <a href="#!" class="text-decoration-none text-reset">Sanity</a>
                        </li>
                        <li class="mb-2">
                           <a href="./changelog.html" class="text-decoration-none text-reset">Content ful</a>
                        </li>
                        <li class="mb-2">
                           <a href="#!" class="text-decoration-none text-reset">Vercel</a>
                        </li>
                        <li class="mb-2">
                           <a href="#!" class="text-decoration-none text-reset">Netlify</a>
                        </li>
                     </ul>
                  </div>
               </div>
               <div class="col-lg-3 col-12">
                  <div class="mb-3 pb-2 d-flex justify-content-between border-bottom border-bottom-lg-0 position-relative">
                     <h4>Locations</h4>
                     <a
                        class="d-block d-lg-none stretched-link text-body collapsed"
                        data-bs-toggle="collapse"
                        href="#collapseLocations"
                        role="button"
                        aria-expanded="false"
                        aria-controls="collapseLocations">
                        <i class="bi bi-chevron-down"></i>
                     </a>
                  </div>
                  <div class="collapse d-lg-block" id="collapseLocations" data-bs-parent="#ft-links">
                     <ul class="list-unstyled mb-0 py-3 py-lg-0">
                        <li class="mb-2">
                           <a href="./docs/index.html" class="text-decoration-none text-reset">India</a>
                        </li>
                        <li class="mb-2">
                           <a href="#!" class="text-decoration-none text-reset">Australia</a>
                        </li>
                        <li class="mb-2">
                           <a href="./changelog.html" class="text-decoration-none text-reset">Brazil</a>
                        </li>
                        <li class="mb-2">
                           <a href="#!" class="text-decoration-none text-reset">Canada</a>
                        </li>
                     </ul>
                  </div>
               </div>
            </div>
         </div>
         <div class="col-lg-3 col-12">
            <div class="me-7">
               <h4 class="mb-4">Headquarters</h4>
               <p class="text-body-secondary">Codescandy, 412, Residency Rd, Shanthala Nagar, Ashok Nagar, Bengaluru, Karnataka, India 560025</p>
            </div>
         </div>
      </div>
   </div>
   <div class="container mt-7 pt-lg-7 pb-4">
      <div class="row align-items-center">
         <div class="col-md-3">
            <a class="mb-4 mb-lg-0 d-block text-inverse" href="../index.html"><img src="./assets/images/logo/logo.svg" alt="" /></a>
         </div>
         <div class="col-md-9 col-lg-6">
            <div class="small mb-3 mb-lg-0 text-lg-center">
               Copyright © 2024

               <span class="text-primary"><a href="#">Block Bootstrap 5 Theme</a></span>
               | Designed by
               <span class="text-primary"><a href="#">CodesCandy</a></span>
            </div>
         </div>
         <div class="col-lg-3">
            <div class="text-lg-end d-flex align-items-center justify-content-lg-end">
               <div class="dropdown">
                  <button class="btn btn-light btn-icon rounded-circle d-flex align-items-center" type="button" aria-expanded="false" data-bs-toggle="dropdown" aria-label="Toggle theme (auto)">
                     <i class="bi theme-icon-active lh-1"><i class="bi theme-icon bi-sun-fill"></i></i>
                     <span class="visually-hidden bs-theme-text">Toggle theme</span>
                  </button>
                  <ul class="dropdown-menu dropdown-menu-end shadow" aria-labelledby="bs-theme-text">
                     <li>
                        <button type="button" class="dropdown-item d-flex align-items-center active" data-bs-theme-value="light" aria-pressed="true">
                           <i class="bi theme-icon bi-sun-fill"></i>
                           <span class="ms-2">Light</span>
                        </button>
                     </li>
                     <li>
                        <button type="button" class="dropdown-item d-flex align-items-center" data-bs-theme-value="dark" aria-pressed="false">
                           <i class="bi theme-icon bi-moon-stars-fill"></i>
                           <span class="ms-2">Dark</span>
                        </button>
                     </li>
                     <li>
                        <button type="button" class="dropdown-item d-flex align-items-center" data-bs-theme-value="auto" aria-pressed="false">
                           <i class="bi theme-icon bi-circle-half"></i>
                           <span class="ms-2">Auto</span>
                        </button>
                     </li>
                  </ul>
               </div>
               <div class="ms-3 d-flex gap-2">
                  <a href="#!" class="btn btn-instagram btn-light btn-icon">
                     <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-instagram" viewBox="0 0 16 16">
                        <path
                           d="M8 0C5.829 0 5.556.01 4.703.048 3.85.088 3.269.222 2.76.42a3.917 3.917 0 0 0-1.417.923A3.927 3.927 0 0 0 .42 2.76C.222 3.268.087 3.85.048 4.7.01 5.555 0 5.827 0 8.001c0 2.172.01 2.444.048 3.297.04.852.174 1.433.372 1.942.205.526.478.972.923 1.417.444.445.89.719 1.416.923.51.198 1.09.333 1.942.372C5.555 15.99 5.827 16 8 16s2.444-.01 3.298-.048c.851-.04 1.434-.174 1.943-.372a3.916 3.916 0 0 0 1.416-.923c.445-.445.718-.891.923-1.417.197-.509.332-1.09.372-1.942C15.99 10.445 16 10.173 16 8s-.01-2.445-.048-3.299c-.04-.851-.175-1.433-.372-1.941a3.926 3.926 0 0 0-.923-1.417A3.911 3.911 0 0 0 13.24.42c-.51-.198-1.092-.333-1.943-.372C10.443.01 10.172 0 7.998 0h.003zm-.717 1.442h.718c2.136 0 2.389.007 3.232.046.78.035 1.204.166 1.486.275.373.145.64.319.92.599.28.28.453.546.598.92.11.281.24.705.275 1.485.039.843.047 1.096.047 3.231s-.008 2.389-.047 3.232c-.035.78-.166 1.203-.275 1.485a2.47 2.47 0 0 1-.599.919c-.28.28-.546.453-.92.598-.28.11-.704.24-1.485.276-.843.038-1.096.047-3.232.047s-2.39-.009-3.233-.047c-.78-.036-1.203-.166-1.485-.276a2.478 2.478 0 0 1-.92-.598 2.48 2.48 0 0 1-.6-.92c-.109-.281-.24-.705-.275-1.485-.038-.843-.046-1.096-.046-3.233 0-2.136.008-2.388.046-3.231.036-.78.166-1.204.276-1.486.145-.373.319-.64.599-.92.28-.28.546-.453.92-.598.282-.11.705-.24 1.485-.276.738-.034 1.024-.044 2.515-.045v.002zm4.988 1.328a.96.96 0 1 0 0 1.92.96.96 0 0 0 0-1.92zm-4.27 1.122a4.109 4.109 0 1 0 0 8.217 4.109 4.109 0 0 0 0-8.217zm0 1.441a2.667 2.667 0 1 1 0 5.334 2.667 2.667 0 0 1 0-5.334z"></path>
                     </svg>
                  </a>
                  <a href="#!" class="btn btn-facebook btn-icon">
                     <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-facebook" viewBox="0 0 16 16">
                        <path
                           d="M16 8.049c0-4.446-3.582-8.05-8-8.05C3.58 0-.002 3.603-.002 8.05c0 4.017 2.926 7.347 6.75 7.951v-5.625h-2.03V8.05H6.75V6.275c0-2.017 1.195-3.131 3.022-3.131.876 0 1.791.157 1.791.157v1.98h-1.009c-.993 0-1.303.621-1.303 1.258v1.51h2.218l-.354 2.326H9.25V16c3.824-.604 6.75-3.934 6.75-7.951z"></path>
                     </svg>
                  </a>
                  <a href="#!" class="btn btn-twitter btn-icon">
                     <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-twitter" viewBox="0 0 16 16">
                        <path
                           d="M5.026 15c6.038 0 9.341-5.003 9.341-9.334 0-.14 0-.282-.006-.422A6.685 6.685 0 0 0 16 3.542a6.658 6.658 0 0 1-1.889.518 3.301 3.301 0 0 0 1.447-1.817 6.533 6.533 0 0 1-2.087.793A3.286 3.286 0 0 0 7.875 6.03a9.325 9.325 0 0 1-6.767-3.429 3.289 3.289 0 0 0 1.018 4.382A3.323 3.323 0 0 1 .64 6.575v.045a3.288 3.288 0 0 0 2.632 3.218 3.203 3.203 0 0 1-.865.115 3.23 3.23 0 0 1-.614-.057 3.283 3.283 0 0 0 3.067 2.277A6.588 6.588 0 0 1 .78 13.58a6.32 6.32 0 0 1-.78-.045A9.344 9.344 0 0 0 5.026 15z"></path>
                     </svg>
                  </a>
               </div>
            </div>
         </div>
      </div>
   </div>
</footer>
 <!-- Libs JS -->
<script src="./assets/libs/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
<script src="./assets/libs/simplebar/dist/simplebar.min.js"></script>
<script src="./assets/libs/headhesive/dist/headhesive.min.js"></script>

<!-- Theme JS -->
<script src="./assets/js/theme.min.js"></script>

      <!-- Swiper JS -->
      <script src="./assets/libs/swiper/swiper-bundle.min.js"></script>
      <script src="./assets/js/vendors/swiper.js"></script>
   </body>
</html>
