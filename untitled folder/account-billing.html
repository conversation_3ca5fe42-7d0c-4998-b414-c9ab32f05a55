<!doctype html>
<html lang="en">
   <head>
      <!-- Required meta tags -->
<meta charset="utf-8" />
<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
 <!-- Favicon icon-->
<link rel="apple-touch-icon" sizes="180x180" href="./assets/images/favicon/apple-touch-icon.png" />
<link rel="icon" type="image/png" sizes="32x32" href="./assets/images/favicon/favicon-32x32.png" />
<link rel="icon" type="image/png" sizes="16x16" href="./assets/images/favicon/favicon-16x16.png" />
<link rel="manifest" href="./assets/images/favicon/site.webmanifest" />
<link rel="mask-icon" href="./assets/images/favicon/block-safari-pinned-tab.svg" color="#8b3dff" />
<link rel="shortcut icon" href="./assets/images/favicon/favicon.ico" />
<meta name="msapplication-TileColor" content="#8b3dff" />
<meta name="msapplication-config" content="./assets/images/favicon/tile.xml" />

<!-- Color modes -->
<script src="./assets/js/vendors/color-modes.js"></script>

<!-- Libs CSS -->
<link href="./assets/libs/simplebar/dist/simplebar.min.css" rel="stylesheet" />
<link href="./assets/libs/bootstrap-icons/font/bootstrap-icons.min.css" rel="stylesheet" />

<!-- Scroll Cue -->
<link rel="stylesheet" href="./assets/libs/scrollcue/scrollCue.css" />

<!-- Box icons -->
<link rel="stylesheet" href="./assets/fonts/css/boxicons.min.css" />

<!-- Theme CSS -->
<link rel="stylesheet" href="./assets/css/theme.min.css">

      <title>Account Billing - Responsive Website Template | Block</title>
   </head>

   <body>
      <!-- Navbar -->
<header>
   <nav class="navbar navbar-expand-lg  navbar-light w-100">
      <div class="container px-3">
         <a class="navbar-brand" href="./index.html"><img src="./assets/images/logo/logo.svg" alt /></a>
         <button class="navbar-toggler offcanvas-nav-btn" type="button">
            <i class="bi bi-list"></i>
         </button>
         <div class="offcanvas offcanvas-start offcanvas-nav" style="width: 20rem">
            <div class="offcanvas-header">
               <a href="./index.html" class="text-inverse"><img src="./assets/images/logo/logo.svg" alt /></a>
               <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close"></button>
            </div>
            <div class="offcanvas-body pt-0 align-items-center">
               <ul class="navbar-nav mx-auto align-items-lg-center">
                  <li class="nav-item">
                     <a class="nav-link" href="./home.html">Home</a>
                  </li>
                  <li class="nav-item">
                     <a class="nav-link" href="./scan-pay.html">Scan & Pay</a>
                  </li>
                  <li class="nav-item">
                     <a class="nav-link" href="./service.html">Services</a>
                  </li>
                  <li class="nav-item">
                     <a class="nav-link" href="./contact.html">Contact</a>
                  </li>
                  <li class="nav-item">
                     <a class="nav-link" href="./become-an-agent.html">Become An Agent</a>
                  </li>
               </ul>
               <div class="mt-3 mt-lg-0 d-flex align-items-center">
                  <a href="./signin.html" class="btn btn-light mx-2">Login</a>
                  <a href="https://play.google.com/store/apps/details?id=com.qsoft.aidapay&hl=en&pli=1" class="btn btn-primary">Create account</a>
               </div>
            </div>
         </div>
      </div>
   </nav>
</header>

      <main>
         <!--Billing start-->
         <section class="py-lg-7 py-5 bg-light-subtle">
            <div class="container">
               <div class="row">
                  <div class="col-lg-3 col-md-4">
                     <div class="d-flex align-items-center mb-4 justify-content-center justify-content-md-start">
                        <img src="./assets/images/avatar/avatar-1.jpg" alt="avatar" class="avatar avatar-lg rounded-circle" />
                        <div class="ms-3">
                           <h5 class="mb-0">Jitu Chauhan</h5>
                           <small>Personal account</small>
                        </div>
                     </div>
                     <!-- Navbar -->
<div class="mb-4 text-center text-md-start">
    <a href="#" class="text-reset">
        <span>
            <span>View site / profile</span>
        </span>
        <span class="ms-2">
            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" class="bi bi-box-arrow-up-right" viewBox="0 0 16 16">
                <path
                    fill-rule="evenodd"
                    d="M8.636 3.5a.5.5 0 0 0-.5-.5H1.5A1.5 1.5 0 0 0 0 4.5v10A1.5 1.5 0 0 0 1.5 16h10a1.5 1.5 0 0 0 1.5-1.5V7.864a.5.5 0 0 0-1 0V14.5a.5.5 0 0 1-.5.5h-10a.5.5 0 0 1-.5-.5v-10a.5.5 0 0 1 .5-.5h6.636a.5.5 0 0 0 .5-.5z" />
                <path fill-rule="evenodd" d="M16 .5a.5.5 0 0 0-.5-.5h-5a.5.5 0 0 0 0 1h3.793L6.146 9.146a.5.5 0 1 0 .708.708L15 1.707V5.5a.5.5 0 0 0 1 0v-5z" />
            </svg>
        </span>
    </a>
</div>
<div class="d-md-none text-center d-grid">
    <button
        class="btn btn-light mb-3 d-flex align-items-center justify-content-between"
        type="button"
        data-bs-toggle="collapse"
        data-bs-target="#collapseAccountMenu"
        aria-expanded="false"
        aria-controls="collapseAccountMenu">
        Account Menu
        <i class="bi bi-chevron-down ms-2"></i>
    </button>
</div>
<div class="collapse d-md-block" id="collapseAccountMenu">
    <ul class="nav flex-column nav-account">
        <li class="nav-item">
            <a class="nav-link" href="./account-home.html">
                <i class="align-bottom bx bx-home"></i>
                <span class="ms-2">Home</span>
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link" href="./account-profile.html">
                <i class="align-bottom bx bx-user"></i>
                <span class="ms-2">Profile</span>
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link" aria-current="page" href="./account-security.html">
                <i class="align-bottom bx bx-lock-alt"></i>
                <span class="ms-2">Security</span>
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link" href="./account-billing.html">
                <i class="align-bottom bx bx-credit-card-front"></i>
                <span class="ms-2">Billing</span>
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link" href="./account-team.html">
                <i class="align-bottom bx bx-user-plus"></i>
                <span class="ms-2">Team</span>
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link" href="./account-notification.html">
                <i class="align-bottom bx bx-bell"></i>
                <span class="ms-2">Notifications</span>
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link" href="./account-app-integration.html">
                <i class="align-bottom bx bx-box"></i>
                <span class="ms-2">Integration</span>
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link" href="./account-device-session.html">
                <i class="align-bottom bx bx-time"></i>
                <span class="ms-2">Session</span>
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link" href="./account-social-links.html">
                <i class="align-bottom bx bx-link"></i>
                <span class="ms-2">Social</span>
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link" href="./account-appearance.html">
                <i class="align-bottom bx bx-palette"></i>
                <span class="ms-2">Appearance</span>
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link" href="./index.html">
                <i class="align-bottom bx bx-log-out"></i>
                <span class="ms-2">Sign Out</span>
            </a>
        </li>
    </ul>
</div>

                  </div>
                  <div class="col-lg-9 col-md-8">
                     <div class="mb-4">
                        <h1 class="mb-0 h3">Billing</h1>
                     </div>

                     <div class="card border-0 mb-4 shadow-sm">
                        <div class="card-body p-lg-5">
                           <div class="mb-6 d-lg-flex align-items-center justify-content-between">
                              <h4 class="mb-0">Current plan</h4>
                              <div class="mt-3 mt-lg-0 d-md-flex">
                                 <a href="#" class="btn btn-light me-2">Cancel plan</a>
                                 <a href="#" class="btn btn-outline-primary">
                                    Upgrade plan
                                    <span class="ms-2">
                                       <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" class="bi bi-arrow-up-right-circle-fill" viewBox="0 0 16 16">
                                          <path
                                             d="M0 8a8 8 0 1 0 16 0A8 8 0 0 0 0 8zm5.904 2.803a.5.5 0 1 1-.707-.707L9.293 6H6.525a.5.5 0 1 1 0-1H10.5a.5.5 0 0 1 .5.5v3.975a.5.5 0 0 1-1 0V6.707l-4.096 4.096z" />
                                       </svg>
                                    </span>
                                 </a>
                              </div>
                           </div>
                           <div class="row gx-4">
                              <div class="col-lg-6">
                                 <div class="card border-0 mb-4 mb-lg-0 bg-light-subtle">
                                    <div class="card-body py-lg-3 px-lg-4">
                                       <div class="mb-5">
                                          <h6>Current monthly bill</h6>
                                          <h4>$19.00</h4>
                                       </div>

                                       <a href="#" class="icon-link icon-link-hover text-primary">
                                          Switch to yearly billing
                                          <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" class="bi bi-arrow-right" viewBox="0 0 16 16">
                                             <path
                                                fill-rule="evenodd"
                                                d="M1 8a.5.5 0 0 1 .5-.5h11.793l-3.147-3.146a.5.5 0 0 1 .708-.708l4 4a.5.5 0 0 1 0 .708l-4 4a.5.5 0 0 1-.708-.708L13.293 8.5H1.5A.5.5 0 0 1 1 8z"></path>
                                          </svg>
                                       </a>
                                    </div>
                                 </div>
                              </div>
                              <div class="col-lg-6">
                                 <div class="card border-0 mb-4 mb-lg-0 bg-light-subtle">
                                    <div class="card-body py-lg-3 px-lg-4">
                                       <div class="mb-5">
                                          <h6>Next payment due</h6>
                                          <h4>30 Jan, 2024</h4>
                                       </div>

                                       <a href="#" class="icon-link icon-link-hover text-primary">
                                          View payment history
                                          <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" class="bi bi-arrow-right" viewBox="0 0 16 16">
                                             <path
                                                fill-rule="evenodd"
                                                d="M1 8a.5.5 0 0 1 .5-.5h11.793l-3.147-3.146a.5.5 0 0 1 .708-.708l4 4a.5.5 0 0 1 0 .708l-4 4a.5.5 0 0 1-.708-.708L13.293 8.5H1.5A.5.5 0 0 1 1 8z"></path>
                                          </svg>
                                       </a>
                                    </div>
                                 </div>
                              </div>
                           </div>
                        </div>
                     </div>
                     <div class="card border-0 mb-4 shadow-sm">
                        <div class="card-body p-lg-5">
                           <div class="mb-5">
                              <h4 class="mb-1">Payment Methods</h4>
                              <p class="fs-6 mb-0">The block theme notification system notifies you of important events such as replies, mentions, updates, etc.</p>
                           </div>
                           <div class="border py-3 px-4 rounded-3 mb-3">
                              <div class="d-flex justify-content-between">
                                 <div class="d-flex">
                                    <img src="./assets/images/landings/account/visa.svg" alt="card" class="me-3" />
                                    <div>
                                       <h6 class="mb-0">Visa ending in 1234</h6>
                                       <small>Expires in 03/2025</small>
                                    </div>
                                 </div>
                                 <div class="d-flex align-items-center">
                                    <span class="dropdown dropstart">
                                       <a class="text-reset" href="#" role="button" id="paymentDropdown1" data-bs-toggle="dropdown" data-bs-offset="-20,20" aria-expanded="false">
                                          <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" class="bi bi-three-dots-vertical" viewBox="0 0 16 16">
                                             <path d="M9.5 13a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0zm0-5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0zm0-5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0z" />
                                          </svg>
                                       </a>
                                       <span class="dropdown-menu" aria-labelledby="paymentDropdown1">
                                          <span class="dropdown-header">Setting</span>
                                          <a class="dropdown-item" href="#">
                                             <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" class="bi bi-pencil-square" viewBox="0 0 16 16">
                                                <path
                                                   d="M15.502 1.94a.5.5 0 0 1 0 .706L14.459 3.69l-2-2L13.502.646a.5.5 0 0 1 .707 0l1.293 1.293zm-1.75 2.456-2-2L4.939 9.21a.5.5 0 0 0-.121.196l-.805 2.414a.25.25 0 0 0 .316.316l2.414-.805a.5.5 0 0 0 .196-.12l6.813-6.814z" />
                                                <path
                                                   fill-rule="evenodd"
                                                   d="M1 13.5A1.5 1.5 0 0 0 2.5 15h11a1.5 1.5 0 0 0 1.5-1.5v-6a.5.5 0 0 0-1 0v6a.5.5 0 0 1-.5.5h-11a.5.5 0 0 1-.5-.5v-11a.5.5 0 0 1 .5-.5H9a.5.5 0 0 0 0-1H2.5A1.5 1.5 0 0 0 1 2.5v11z" />
                                             </svg>
                                             <span class="ms-2">Edit</span>
                                          </a>
                                          <a class="dropdown-item" href="#">
                                             <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" class="bi bi-trash" viewBox="0 0 16 16">
                                                <path
                                                   d="M5.5 5.5A.5.5 0 0 1 6 6v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5Zm2.5 0a.5.5 0 0 1 .5.5v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5Zm3 .5a.5.5 0 0 0-1 0v6a.5.5 0 0 0 1 0V6Z" />
                                                <path
                                                   d="M14.5 3a1 1 0 0 1-1 1H13v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V4h-.5a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1H6a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1h3.5a1 1 0 0 1 1 1v1ZM4.118 4 4 4.059V13a1 1 0 0 0 1 1h6a1 1 0 0 0 1-1V4.059L11.882 4H4.118ZM2.5 3h11V2h-11v1Z" />
                                             </svg>
                                             <span class="ms-2">Remove</span>
                                          </a>
                                          <a class="dropdown-item" href="#">
                                             <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" class="bi bi-credit-card" viewBox="0 0 16 16">
                                                <path
                                                   d="M0 4a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V4zm2-1a1 1 0 0 0-1 1v1h14V4a1 1 0 0 0-1-1H2zm13 4H1v5a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1V7z" />
                                                <path d="M2 10a1 1 0 0 1 1-1h1a1 1 0 0 1 1 1v1a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1v-1z" />
                                             </svg>
                                             <span class="ms-2">Make it Primary</span>
                                          </a>
                                       </span>
                                    </span>
                                 </div>
                              </div>
                           </div>
                           <div class="border py-3 px-4 rounded-3">
                              <div class="d-flex justify-content-between">
                                 <div class="d-flex">
                                    <img src="./assets/images/landings/account/mastercard.svg" alt="card" class="me-3" />
                                    <div>
                                       <h6 class="mb-0">Master ending in 1234</h6>
                                       <small>Expires in 03/2026</small>
                                    </div>
                                 </div>
                                 <div class="d-flex align-items-center">
                                    <span class="dropdown dropstart">
                                       <a class="text-reset" href="#" role="button" id="paymentDropdown2" data-bs-toggle="dropdown" data-bs-offset="-20,20" aria-expanded="false">
                                          <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" class="bi bi-three-dots-vertical" viewBox="0 0 16 16">
                                             <path d="M9.5 13a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0zm0-5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0zm0-5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0z" />
                                          </svg>
                                       </a>
                                       <span class="dropdown-menu" aria-labelledby="paymentDropdown2">
                                          <span class="dropdown-header">Setting</span>
                                          <a class="dropdown-item" href="#">
                                             <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" class="bi bi-pencil-square" viewBox="0 0 16 16">
                                                <path
                                                   d="M15.502 1.94a.5.5 0 0 1 0 .706L14.459 3.69l-2-2L13.502.646a.5.5 0 0 1 .707 0l1.293 1.293zm-1.75 2.456-2-2L4.939 9.21a.5.5 0 0 0-.121.196l-.805 2.414a.25.25 0 0 0 .316.316l2.414-.805a.5.5 0 0 0 .196-.12l6.813-6.814z" />
                                                <path
                                                   fill-rule="evenodd"
                                                   d="M1 13.5A1.5 1.5 0 0 0 2.5 15h11a1.5 1.5 0 0 0 1.5-1.5v-6a.5.5 0 0 0-1 0v6a.5.5 0 0 1-.5.5h-11a.5.5 0 0 1-.5-.5v-11a.5.5 0 0 1 .5-.5H9a.5.5 0 0 0 0-1H2.5A1.5 1.5 0 0 0 1 2.5v11z" />
                                             </svg>
                                             <span class="ms-2">Edit</span>
                                          </a>
                                          <a class="dropdown-item" href="#">
                                             <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" class="bi bi-trash" viewBox="0 0 16 16">
                                                <path
                                                   d="M5.5 5.5A.5.5 0 0 1 6 6v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5Zm2.5 0a.5.5 0 0 1 .5.5v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5Zm3 .5a.5.5 0 0 0-1 0v6a.5.5 0 0 0 1 0V6Z" />
                                                <path
                                                   d="M14.5 3a1 1 0 0 1-1 1H13v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V4h-.5a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1H6a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1h3.5a1 1 0 0 1 1 1v1ZM4.118 4 4 4.059V13a1 1 0 0 0 1 1h6a1 1 0 0 0 1-1V4.059L11.882 4H4.118ZM2.5 3h11V2h-11v1Z" />
                                             </svg>
                                             <span class="ms-2">Remove</span>
                                          </a>
                                          <a class="dropdown-item" href="#">
                                             <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" class="bi bi-credit-card" viewBox="0 0 16 16">
                                                <path
                                                   d="M0 4a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V4zm2-1a1 1 0 0 0-1 1v1h14V4a1 1 0 0 0-1-1H2zm13 4H1v5a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1V7z" />
                                                <path d="M2 10a1 1 0 0 1 1-1h1a1 1 0 0 1 1 1v1a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1v-1z" />
                                             </svg>
                                             <span class="ms-2">Make it Primary</span>
                                          </a>
                                       </span>
                                    </span>
                                 </div>
                              </div>
                           </div>
                           <a href="#" class="btn btn-outline-primary mt-4" data-bs-toggle="modal" data-bs-target="#paymentModal">Add Payment Method</a>
                        </div>
                     </div>
                     <div class="card border-0 mb-4 shadow-sm">
                        <div class="card-body p-lg-5">
                           <div class="mb-5">
                              <h4 class="mb-1">Invoice History</h4>
                              <p class="fs-6 mb-0">The Invoice History Report is needed to justify the balance for a given range of invoices.</p>
                           </div>

                           <ul class="list-group list-group-flush mb-0">
                              <li class="list-group-item px-0 py-3 border-top">
                                 <div class="d-flex justify-content-between text-dark fw-semibold fs-6">
                                    <span>01 Feb 2023</span>
                                    <span>$26.96</span>
                                    <a href="#">PDF</a>
                                 </div>
                              </li>
                              <li class="list-group-item px-0 py-3">
                                 <div class="d-flex justify-content-between text-dark fw-semibold fs-6">
                                    <span>31 Jan 2023</span>
                                    <span>$34.30</span>
                                    <a href="#">PDF</a>
                                 </div>
                              </li>
                              <li class="list-group-item px-0 py-3">
                                 <div class="d-flex justify-content-between text-dark fw-semibold fs-6">
                                    <span>30 Jan 2023</span>
                                    <span>$93.10</span>
                                    <a href="#">PDF</a>
                                 </div>
                              </li>
                              <li class="list-group-item px-0 py-3">
                                 <div class="d-flex justify-content-between text-dark fw-semibold fs-6">
                                    <span>29 Jan 2023</span>
                                    <span>$44.39</span>
                                    <a href="#">PDF</a>
                                 </div>
                              </li>
                              <li class="list-group-item px-0 py-3 border-bottom">
                                 <div class="d-flex justify-content-between text-dark fw-semibold fs-6">
                                    <span>23 Jan 2023</span>
                                    <span>$26.96</span>
                                    <a href="#">PDF</a>
                                 </div>
                              </li>
                           </ul>

                           <div class="mt-4">
                              <a href="#" class="icon-link icon-link-hover">
                                 View All Invoices
                                 <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" class="bi bi-arrow-right" viewBox="0 0 16 16">
                                    <path
                                       fill-rule="evenodd"
                                       d="M1 8a.5.5 0 0 1 .5-.5h11.793l-3.147-3.146a.5.5 0 0 1 .708-.708l4 4a.5.5 0 0 1 0 .708l-4 4a.5.5 0 0 1-.708-.708L13.293 8.5H1.5A.5.5 0 0 1 1 8z"></path>
                                 </svg>
                              </a>
                           </div>
                        </div>
                     </div>
                     <div class="card border-primary mb-4 border-1 shadow-sm">
                        <div class="card-header border-0 bg-primary bg-opacity-10 py-3">
                           <h3 class="mb-1 text-primary-emphasis">Free Plan - 30 Days Trial</h3>
                           <p class="mb-0 fs-6 text-primary-emphasis">For new web developers ready to create their first site.</p>
                        </div>
                        <div class="card-body">
                           <div class="row">
                              <div class="col-lg-6">
                                 <div class="mb-4 mb-lg-0">
                                    <h6 class="mb-3">Included:</h6>
                                    <ul class="list-unstyled mb-0">
                                       <li class="mb-2">
                                          <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" class="bi bi-check-circle-fill text-success" viewBox="0 0 16 16">
                                             <path
                                                d="M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zm-3.97-3.03a.75.75 0 0 0-1.08.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-.01-1.05z" />
                                          </svg>
                                          <span class="ms-2">Browse Website with ads</span>
                                       </li>
                                       <li class="mb-2">
                                          <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" class="bi bi-check-circle-fill text-success" viewBox="0 0 16 16">
                                             <path
                                                d="M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zm-3.97-3.03a.75.75 0 0 0-1.08.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-.01-1.05z" />
                                          </svg>
                                          <span class="ms-2">Build static sites</span>
                                       </li>
                                       <li class="mb-2">
                                          <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" class="bi bi-check-circle-fill text-success" viewBox="0 0 16 16">
                                             <path
                                                d="M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zm-3.97-3.03a.75.75 0 0 0-1.08.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-.01-1.05z" />
                                          </svg>
                                          <span class="ms-2">1 spaces</span>
                                       </li>
                                       <li class="mb-2">
                                          <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" class="bi bi-check-circle-fill text-success" viewBox="0 0 16 16">
                                             <path
                                                d="M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zm-3.97-3.03a.75.75 0 0 0-1.08.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-.01-1.05z" />
                                          </svg>
                                          <span class="ms-2">10 MB storage</span>
                                       </li>
                                       <li class="mb-2">
                                          <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" class="bi bi-check-circle-fill text-success" viewBox="0 0 16 16">
                                             <path
                                                d="M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zm-3.97-3.03a.75.75 0 0 0-1.08.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-.01-1.05z" />
                                          </svg>
                                          <span class="ms-2">75 MB data / month</span>
                                       </li>
                                       <li class="mb-2">
                                          <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" class="bi bi-check-circle-fill text-success" viewBox="0 0 16 16">
                                             <path
                                                d="M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zm-3.97-3.03a.75.75 0 0 0-1.08.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-.01-1.05z" />
                                          </svg>
                                          <span class="ms-2">500 requests / month</span>
                                       </li>
                                       <li class="mb-2">
                                          <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" class="bi bi-check-circle-fill text-success" viewBox="0 0 16 16">
                                             <path
                                                d="M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zm-3.97-3.03a.75.75 0 0 0-1.08.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-.01-1.05z" />
                                          </svg>
                                          <span class="ms-2">Subdomain</span>
                                       </li>
                                       <li class="mb-2">
                                          <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" class="bi bi-check-circle-fill text-success" viewBox="0 0 16 16">
                                             <path
                                                d="M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zm-3.97-3.03a.75.75 0 0 0-1.08.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-.01-1.05z" />
                                          </svg>
                                          <span class="ms-2">SSL certificate</span>
                                       </li>
                                    </ul>
                                 </div>
                              </div>
                              <div class="col-lg-6">
                                 <div class="mb-4 mb-lg-0">
                                    <h6 class="mb-3">Not included:</h6>
                                    <ul class="list-unstyled mb-0">
                                       <li class="mb-2">
                                          <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" class="bi bi-x-circle text-danger" viewBox="0 0 16 16">
                                             <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z" />
                                             <path
                                                d="M4.646 4.646a.5.5 0 0 1 .708 0L8 7.293l2.646-2.647a.5.5 0 0 1 .708.708L8.707 8l2.647 2.646a.5.5 0 0 1-.708.708L8 8.707l-2.646 2.647a.5.5 0 0 1-.708-.708L7.293 8 4.646 5.354a.5.5 0 0 1 0-.708z" />
                                          </svg>
                                          <span class="ms-2">Access to HTML video</span>
                                       </li>
                                       <li class="mb-2">
                                          <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" class="bi bi-x-circle text-danger" viewBox="0 0 16 16">
                                             <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z" />
                                             <path
                                                d="M4.646 4.646a.5.5 0 0 1 .708 0L8 7.293l2.646-2.647a.5.5 0 0 1 .708.708L8.707 8l2.647 2.646a.5.5 0 0 1-.708.708L8 8.707l-2.646 2.647a.5.5 0 0 1-.708-.708L7.293 8 4.646 5.354a.5.5 0 0 1 0-.708z" />
                                          </svg>
                                          <span class="ms-2">Priority support</span>
                                       </li>
                                       <li class="mb-2">
                                          <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" class="bi bi-x-circle text-danger" viewBox="0 0 16 16">
                                             <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z" />
                                             <path
                                                d="M4.646 4.646a.5.5 0 0 1 .708 0L8 7.293l2.646-2.647a.5.5 0 0 1 .708.708L8.707 8l2.647 2.646a.5.5 0 0 1-.708.708L8 8.707l-2.646 2.647a.5.5 0 0 1-.708-.708L7.293 8 4.646 5.354a.5.5 0 0 1 0-.708z" />
                                          </svg>
                                          <span class="ms-2">Advance static sites</span>
                                       </li>
                                       <li class="mb-2">
                                          <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" class="bi bi-x-circle text-danger" viewBox="0 0 16 16">
                                             <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z" />
                                             <path
                                                d="M4.646 4.646a.5.5 0 0 1 .708 0L8 7.293l2.646-2.647a.5.5 0 0 1 .708.708L8.707 8l2.647 2.646a.5.5 0 0 1-.708.708L8 8.707l-2.646 2.647a.5.5 0 0 1-.708-.708L7.293 8 4.646 5.354a.5.5 0 0 1 0-.708z" />
                                          </svg>
                                          <span class="ms-2">Analytics</span>
                                       </li>
                                       <li class="mb-2">
                                          <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" class="bi bi-x-circle text-danger" viewBox="0 0 16 16">
                                             <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z" />
                                             <path
                                                d="M4.646 4.646a.5.5 0 0 1 .708 0L8 7.293l2.646-2.647a.5.5 0 0 1 .708.708L8.707 8l2.647 2.646a.5.5 0 0 1-.708.708L8 8.707l-2.646 2.647a.5.5 0 0 1-.708-.708L7.293 8 4.646 5.354a.5.5 0 0 1 0-.708z" />
                                          </svg>
                                          <span class="ms-2">SEO</span>
                                       </li>
                                       <li class="mb-2">
                                          <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" class="bi bi-x-circle text-danger" viewBox="0 0 16 16">
                                             <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z" />
                                             <path
                                                d="M4.646 4.646a.5.5 0 0 1 .708 0L8 7.293l2.646-2.647a.5.5 0 0 1 .708.708L8.707 8l2.647 2.646a.5.5 0 0 1-.708.708L8 8.707l-2.646 2.647a.5.5 0 0 1-.708-.708L7.293 8 4.646 5.354a.5.5 0 0 1 0-.708z" />
                                          </svg>
                                          <span class="ms-2">Easy Integration Add ons</span>
                                       </li>
                                       <li class="mb-2">
                                          <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" class="bi bi-x-circle text-danger" viewBox="0 0 16 16">
                                             <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z" />
                                             <path
                                                d="M4.646 4.646a.5.5 0 0 1 .708 0L8 7.293l2.646-2.647a.5.5 0 0 1 .708.708L8.707 8l2.647 2.646a.5.5 0 0 1-.708.708L8 8.707l-2.646 2.647a.5.5 0 0 1-.708-.708L7.293 8 4.646 5.354a.5.5 0 0 1 0-.708z" />
                                          </svg>
                                          <span class="ms-2">Pages for static website hosting</span>
                                       </li>
                                       <li class="mb-2">
                                          <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" class="bi bi-x-circle text-danger" viewBox="0 0 16 16">
                                             <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z" />
                                             <path
                                                d="M4.646 4.646a.5.5 0 0 1 .708 0L8 7.293l2.646-2.647a.5.5 0 0 1 .708.708L8.707 8l2.647 2.646a.5.5 0 0 1-.708.708L8 8.707l-2.646 2.647a.5.5 0 0 1-.708-.708L7.293 8 4.646 5.354a.5.5 0 0 1 0-.708z" />
                                          </svg>
                                          <span class="ms-2">Web-based support</span>
                                       </li>
                                    </ul>
                                 </div>
                              </div>
                              <div class="col-lg-12">
                                 <div class="mt-4">
                                    <a href="#" class="icon-link icon-link-hover">
                                       See all features and compare plans
                                       <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" class="bi bi-arrow-right" viewBox="0 0 16 16">
                                          <path
                                             fill-rule="evenodd"
                                             d="M1 8a.5.5 0 0 1 .5-.5h11.793l-3.147-3.146a.5.5 0 0 1 .708-.708l4 4a.5.5 0 0 1 0 .708l-4 4a.5.5 0 0 1-.708-.708L13.293 8.5H1.5A.5.5 0 0 1 1 8z"></path>
                                       </svg>
                                    </a>
                                 </div>
                              </div>
                           </div>
                        </div>
                     </div>
                  </div>
               </div>
            </div>
         </section>
         <!--Billing end-->
      </main>
      <!--Modal start-->
      <div class="modal fade" id="paymentModal" tabindex="-1" aria-labelledby="paymentModalLabel" aria-hidden="true">
         <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
               <div class="modal-header align-items-center d-flex">
                  <h4 class="modal-title" id="paymentModalLabel">Add New Payment Method</h4>
                  <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
               </div>
               <!-- Modal body -->
               <div class="modal-body">
                  <!-- Form -->
                  <form class="row mb-4 needs-validation" novalidate>
                     <div class="mb-3 col-12 col-md-12 mb-4">
                        <h5 class="mb-3">Credit / Debit card</h5>
                        <!-- Radio button -->
                        <div class="d-inline-flex">
                           <div class="form-check me-2">
                              <input type="radio" id="paymentRadioOne" name="paymentRadioOne" class="form-check-input" />
                              <label class="form-check-label" for="paymentRadioOne"><img src="./assets/images/landings/account/americanexpress.svg" alt="card" /></label>
                           </div>
                           <!-- Radio button -->
                           <div class="form-check me-2">
                              <input type="radio" id="paymentRadioTwo" name="paymentRadioOne" class="form-check-input" />
                              <label class="form-check-label" for="paymentRadioTwo"><img src="./assets/images/landings/account/mastercard.svg" alt="card" /></label>
                           </div>

                           <!-- Radio button -->
                           <div class="form-check">
                              <input type="radio" id="paymentRadioFour" name="paymentRadioOne" class="form-check-input" />
                              <label class="form-check-label" for="paymentRadioFour"><img src="./assets/images/landings/account/visa.svg" alt="card" /></label>
                           </div>
                        </div>
                     </div>
                     <!-- Name on card -->
                     <div class="mb-3 col-12 col-md-4">
                        <label for="nameoncard" class="form-label">Name on card</label>
                        <input id="nameoncard" type="text" class="form-control" name="nameoncard" placeholder="Name" required="" />
                     </div>
                     <!-- Month -->

                     <div class="mb-3 col-12 col-md-4">
                        <label class="form-label" for="month">Month</label>
                        <select class="form-select" id="month" required>
                           <option selected disabled value="">Choose...</option>
                           <option value="Month">Month</option>
                           <option value="Jan">Jan</option>
                           <option value="Feb">Feb</option>
                           <option value="Mar">Mar</option>
                           <option value="Apr">Apr</option>
                           <option value="May">May</option>
                           <option value="June">June</option>
                           <option value="July">July</option>
                           <option value="Aug">Aug</option>
                           <option value="Sep">Sep</option>
                           <option value="Oct">Oct</option>
                           <option value="Nov">Nov</option>
                           <option value="Dec">Dec</option>
                        </select>
                     </div>

                     <!-- Year -->
                     <div class="mb-3 col-12 col-md-4">
                        <label class="form-label" for="year">Year</label>
                        <select class="form-select" id="year" required>
                           <option selected disabled value="">Choose...</option>
                           <option value="Year">Year</option>
                           <option value="2020">2020</option>
                           <option value="2021">2021</option>
                           <option value="2022">2022</option>
                           <option value="2023">2023</option>
                           <option value="2024">2024</option>
                        </select>
                     </div>

                     <!-- Card number -->
                     <div class="mb-3 col-md-8 col-12">
                        <label for="cc-mask" class="form-label">Card Number</label>
                        <input type="text" class="form-control input-credit-card" id="cc-mask" placeholder="xxxx-xxxx-xxxx-xxxx" required="" />
                     </div>
                     <!-- CVV -->
                     <div class="mb-3 col-md-4 col-12">
                        <div class="mb-3">
                           <label for="cvv" class="form-label">
                              CVV Code
                              <i
                                 class="bi bi-question-circle ms-1"
                                 data-bs-toggle="tooltip"
                                 data-placement="top"
                                 aria-label="A 3 - digit number, typically printed on the back of a card."
                                 data-bs-original-title="A 3 - digit number, typically printed on the back of a card."></i>
                           </label>
                           <input type="password" class="form-control" name="cvv" id="cvv" placeholder="xxx" maxlength="3" inputmode="numeric" required="" />
                        </div>
                     </div>
                     <!-- Button -->
                     <div class="col-md-6 col-12">
                        <button class="btn btn-primary" type="submit">Add New Card</button>
                        <button class="btn btn-light" type="button" data-bs-dismiss="modal">Close</button>
                     </div>
                  </form>
                  <span>
                     <span class="fw-bold">Note:</span>
                     that you can later remove your card at the account setting page.
                  </span>
               </div>
            </div>
         </div>
      </div>
      <!--Modal end-->

      <footer class="pt-7">
   <div class="container">
      <!-- Footer 4 column -->
      <div class="row">
         <div class="col-lg-9 col-12">
            <div class="row" id="ft-links">
               <div class="col-lg-3 col-12">
                  <div class="position-relative">
                     <div class="mb-3 pb-2 d-flex justify-content-between border-bottom border-bottom-lg-0">
                        <h4>Service</h4>
                        <a class="d-block d-lg-none stretched-link text-body" data-bs-toggle="collapse" href="#collapseLanding" role="button" aria-expanded="true" aria-controls="collapseLanding">
                           <i class="bi bi-chevron-down"></i>
                        </a>
                     </div>
                     <div class="d-lg-block collapse show" id="collapseLanding" data-bs-parent="#ft-links" style="">
                        <ul class="list-unstyled mb-0 py-3 py-lg-0">
                           <li class="mb-2">
                              <a href="./index.html" class="text-decoration-none text-reset">Web App Development</a>
                           </li>
                           <li class="mb-2">
                              <a href="#!" class="text-decoration-none text-reset">Front End Development</a>
                           </li>
                           <li class="mb-2">
                              <a href="#!" class="text-decoration-none text-reset">MVP Development</a>
                           </li>
                           <li class="mb-2">
                              <a href="#!" class="text-decoration-none text-reset">Digital Marketing</a>
                           </li>
                           <li class="mb-2">
                              <a href="#!" class="text-decoration-none text-reset">Content Writing</a>
                           </li>
                        </ul>
                     </div>
                  </div>
               </div>
               <div class="col-lg-3 col-12">
                  <div>
                     <div class="mb-3 pb-2 d-flex justify-content-between border-bottom border-bottom-lg-0 position-relative">
                        <h4>About us</h4>
                        <a
                           class="d-block d-lg-none stretched-link text-body collapsed"
                           data-bs-toggle="collapse"
                           href="#collapseAccounts"
                           role="button"
                           aria-expanded="false"
                           aria-controls="collapseAccounts">
                           <i class="bi bi-chevron-down"></i>
                        </a>
                     </div>
                     <div class="collapse d-lg-block" id="collapseAccounts" data-bs-parent="#ft-links">
                        <ul class="list-unstyled mb-0 py-3 py-lg-0">
                           <li class="mb-2">
                              <a href="#!" class="text-decoration-none text-reset">Case Studies</a>
                           </li>
                           <li class="mb-2">
                              <a href="#!" class="text-decoration-none text-reset">Blog</a>
                           </li>
                           <li class="mb-2">
                              <a href="#!" class="text-decoration-none text-reset">Services</a>
                           </li>
                           <li class="mb-2">
                              <a href="#!" class="text-decoration-none text-reset">About</a>
                           </li>
                           <li class="mb-2">
                              <a href="#!" class="text-decoration-none text-reset">Career</a>
                           </li>
                        </ul>
                     </div>
                  </div>
               </div>
               <div class="col-lg-3 col-12">
                  <div class="mb-3 pb-2 d-flex justify-content-between border-bottom border-bottom-lg-0 position-relative">
                     <h4>Technology</h4>
                     <a
                        class="d-block d-lg-none stretched-link text-body collapsed"
                        data-bs-toggle="collapse"
                        href="#collapseResources"
                        role="button"
                        aria-expanded="false"
                        aria-controls="collapseResources">
                        <i class="bi bi-chevron-down"></i>
                     </a>
                  </div>
                  <div class="collapse d-lg-block" id="collapseResources" data-bs-parent="#ft-links">
                     <ul class="list-unstyled mb-0 py-3 py-lg-0">
                        <li class="mb-2">
                           <a href="./docs/index.html" class="text-decoration-none text-reset">Next.js</a>
                        </li>
                        <li class="mb-2">
                           <a href="#!" class="text-decoration-none text-reset">Sanity</a>
                        </li>
                        <li class="mb-2">
                           <a href="./changelog.html" class="text-decoration-none text-reset">Content ful</a>
                        </li>
                        <li class="mb-2">
                           <a href="#!" class="text-decoration-none text-reset">Vercel</a>
                        </li>
                        <li class="mb-2">
                           <a href="#!" class="text-decoration-none text-reset">Netlify</a>
                        </li>
                     </ul>
                  </div>
               </div>
               <div class="col-lg-3 col-12">
                  <div class="mb-3 pb-2 d-flex justify-content-between border-bottom border-bottom-lg-0 position-relative">
                     <h4>Locations</h4>
                     <a
                        class="d-block d-lg-none stretched-link text-body collapsed"
                        data-bs-toggle="collapse"
                        href="#collapseLocations"
                        role="button"
                        aria-expanded="false"
                        aria-controls="collapseLocations">
                        <i class="bi bi-chevron-down"></i>
                     </a>
                  </div>
                  <div class="collapse d-lg-block" id="collapseLocations" data-bs-parent="#ft-links">
                     <ul class="list-unstyled mb-0 py-3 py-lg-0">
                        <li class="mb-2">
                           <a href="./docs/index.html" class="text-decoration-none text-reset">India</a>
                        </li>
                        <li class="mb-2">
                           <a href="#!" class="text-decoration-none text-reset">Australia</a>
                        </li>
                        <li class="mb-2">
                           <a href="./changelog.html" class="text-decoration-none text-reset">Brazil</a>
                        </li>
                        <li class="mb-2">
                           <a href="#!" class="text-decoration-none text-reset">Canada</a>
                        </li>
                     </ul>
                  </div>
               </div>
            </div>
         </div>
         <div class="col-lg-3 col-12">
            <div class="me-7">
               <h4 class="mb-4">Headquarters</h4>
               <p class="text-body-secondary">Codescandy, 412, Residency Rd, Shanthala Nagar, Ashok Nagar, Bengaluru, Karnataka, India 560025</p>
            </div>
         </div>
      </div>
   </div>
   <div class="container mt-7 pt-lg-7 pb-4">
      <div class="row align-items-center">
         <div class="col-md-3">
            <a class="mb-4 mb-lg-0 d-block text-inverse" href="../index.html"><img src="./assets/images/logo/logo.svg" alt="" /></a>
         </div>
         <div class="col-md-9 col-lg-6">
            <div class="small mb-3 mb-lg-0 text-lg-center">
               Copyright © 2024

               <span class="text-primary"><a href="#">Block Bootstrap 5 Theme</a></span>
               | Designed by
               <span class="text-primary"><a href="#">CodesCandy</a></span>
            </div>
         </div>
         <div class="col-lg-3">
            <div class="text-lg-end d-flex align-items-center justify-content-lg-end">
               <div class="dropdown">
                  <button class="btn btn-light btn-icon rounded-circle d-flex align-items-center" type="button" aria-expanded="false" data-bs-toggle="dropdown" aria-label="Toggle theme (auto)">
                     <i class="bi theme-icon-active lh-1"><i class="bi theme-icon bi-sun-fill"></i></i>
                     <span class="visually-hidden bs-theme-text">Toggle theme</span>
                  </button>
                  <ul class="dropdown-menu dropdown-menu-end shadow" aria-labelledby="bs-theme-text">
                     <li>
                        <button type="button" class="dropdown-item d-flex align-items-center active" data-bs-theme-value="light" aria-pressed="true">
                           <i class="bi theme-icon bi-sun-fill"></i>
                           <span class="ms-2">Light</span>
                        </button>
                     </li>
                     <li>
                        <button type="button" class="dropdown-item d-flex align-items-center" data-bs-theme-value="dark" aria-pressed="false">
                           <i class="bi theme-icon bi-moon-stars-fill"></i>
                           <span class="ms-2">Dark</span>
                        </button>
                     </li>
                     <li>
                        <button type="button" class="dropdown-item d-flex align-items-center" data-bs-theme-value="auto" aria-pressed="false">
                           <i class="bi theme-icon bi-circle-half"></i>
                           <span class="ms-2">Auto</span>
                        </button>
                     </li>
                  </ul>
               </div>
               <div class="ms-3 d-flex gap-2">
                  <a href="#!" class="btn btn-instagram btn-light btn-icon">
                     <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-instagram" viewBox="0 0 16 16">
                        <path
                           d="M8 0C5.829 0 5.556.01 4.703.048 3.85.088 3.269.222 2.76.42a3.917 3.917 0 0 0-1.417.923A3.927 3.927 0 0 0 .42 2.76C.222 3.268.087 3.85.048 4.7.01 5.555 0 5.827 0 8.001c0 2.172.01 2.444.048 3.297.04.852.174 1.433.372 1.942.205.526.478.972.923 1.417.444.445.89.719 1.416.923.51.198 1.09.333 1.942.372C5.555 15.99 5.827 16 8 16s2.444-.01 3.298-.048c.851-.04 1.434-.174 1.943-.372a3.916 3.916 0 0 0 1.416-.923c.445-.445.718-.891.923-1.417.197-.509.332-1.09.372-1.942C15.99 10.445 16 10.173 16 8s-.01-2.445-.048-3.299c-.04-.851-.175-1.433-.372-1.941a3.926 3.926 0 0 0-.923-1.417A3.911 3.911 0 0 0 13.24.42c-.51-.198-1.092-.333-1.943-.372C10.443.01 10.172 0 7.998 0h.003zm-.717 1.442h.718c2.136 0 2.389.007 3.232.046.78.035 1.204.166 1.486.275.373.145.64.319.92.599.28.28.453.546.598.92.11.281.24.705.275 1.485.039.843.047 1.096.047 3.231s-.008 2.389-.047 3.232c-.035.78-.166 1.203-.275 1.485a2.47 2.47 0 0 1-.599.919c-.28.28-.546.453-.92.598-.28.11-.704.24-1.485.276-.843.038-1.096.047-3.232.047s-2.39-.009-3.233-.047c-.78-.036-1.203-.166-1.485-.276a2.478 2.478 0 0 1-.92-.598 2.48 2.48 0 0 1-.6-.92c-.109-.281-.24-.705-.275-1.485-.038-.843-.046-1.096-.046-3.233 0-2.136.008-2.388.046-3.231.036-.78.166-1.204.276-1.486.145-.373.319-.64.599-.92.28-.28.546-.453.92-.598.282-.11.705-.24 1.485-.276.738-.034 1.024-.044 2.515-.045v.002zm4.988 1.328a.96.96 0 1 0 0 1.92.96.96 0 0 0 0-1.92zm-4.27 1.122a4.109 4.109 0 1 0 0 8.217 4.109 4.109 0 0 0 0-8.217zm0 1.441a2.667 2.667 0 1 1 0 5.334 2.667 2.667 0 0 1 0-5.334z"></path>
                     </svg>
                  </a>
                  <a href="#!" class="btn btn-facebook btn-icon">
                     <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-facebook" viewBox="0 0 16 16">
                        <path
                           d="M16 8.049c0-4.446-3.582-8.05-8-8.05C3.58 0-.002 3.603-.002 8.05c0 4.017 2.926 7.347 6.75 7.951v-5.625h-2.03V8.05H6.75V6.275c0-2.017 1.195-3.131 3.022-3.131.876 0 1.791.157 1.791.157v1.98h-1.009c-.993 0-1.303.621-1.303 1.258v1.51h2.218l-.354 2.326H9.25V16c3.824-.604 6.75-3.934 6.75-7.951z"></path>
                     </svg>
                  </a>
                  <a href="#!" class="btn btn-twitter btn-icon">
                     <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-twitter" viewBox="0 0 16 16">
                        <path
                           d="M5.026 15c6.038 0 9.341-5.003 9.341-9.334 0-.14 0-.282-.006-.422A6.685 6.685 0 0 0 16 3.542a6.658 6.658 0 0 1-1.889.518 3.301 3.301 0 0 0 1.447-1.817 6.533 6.533 0 0 1-2.087.793A3.286 3.286 0 0 0 7.875 6.03a9.325 9.325 0 0 1-6.767-3.429 3.289 3.289 0 0 0 1.018 4.382A3.323 3.323 0 0 1 .64 6.575v.045a3.288 3.288 0 0 0 2.632 3.218 3.203 3.203 0 0 1-.865.115 3.23 3.23 0 0 1-.614-.057 3.283 3.283 0 0 0 3.067 2.277A6.588 6.588 0 0 1 .78 13.58a6.32 6.32 0 0 1-.78-.045A9.344 9.344 0 0 0 5.026 15z"></path>
                     </svg>
                  </a>
               </div>
            </div>
         </div>
      </div>
   </div>
</footer>
 <!-- Libs JS -->
<script src="./assets/libs/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
<script src="./assets/libs/simplebar/dist/simplebar.min.js"></script>
<script src="./assets/libs/headhesive/dist/headhesive.min.js"></script>

<!-- Theme JS -->
<script src="./assets/js/theme.min.js"></script>

      <script src="./assets/js/vendors/sidenav.js"></script>
      <script src="./assets/libs/cleave.js/dist/cleave.min.js"></script>
      <script src="./assets/js/vendors/cleave-function.js"></script>
   </body>
</html>
