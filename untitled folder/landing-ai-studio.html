<!doctype html>
<html lang="en">
   <head>
      <!-- Required meta tags -->
<meta charset="utf-8" />
<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />

      <link rel="stylesheet" href="./assets/libs/swiper/swiper-bundle.min.css" />
      <!-- Favicon icon-->
<link rel="apple-touch-icon" sizes="180x180" href="./assets/images/favicon/apple-touch-icon.png" />
<link rel="icon" type="image/png" sizes="32x32" href="./assets/images/favicon/favicon-32x32.png" />
<link rel="icon" type="image/png" sizes="16x16" href="./assets/images/favicon/favicon-16x16.png" />
<link rel="manifest" href="./assets/images/favicon/site.webmanifest" />
<link rel="mask-icon" href="./assets/images/favicon/block-safari-pinned-tab.svg" color="#8b3dff" />
<link rel="shortcut icon" href="./assets/images/favicon/favicon.ico" />
<meta name="msapplication-TileColor" content="#8b3dff" />
<meta name="msapplication-config" content="./assets/images/favicon/tile.xml" />

<!-- Color modes -->
<script src="./assets/js/vendors/color-modes.js"></script>

<!-- Libs CSS -->
<link href="./assets/libs/simplebar/dist/simplebar.min.css" rel="stylesheet" />
<link href="./assets/libs/bootstrap-icons/font/bootstrap-icons.min.css" rel="stylesheet" />

<!-- Scroll Cue -->
<link rel="stylesheet" href="./assets/libs/scrollcue/scrollCue.css" />

<!-- Box icons -->
<link rel="stylesheet" href="./assets/fonts/css/boxicons.min.css" />

<!-- Theme CSS -->
<link rel="stylesheet" href="./assets/css/theme.min.css">

      <title>Landing AI Studio - Responsive Website Template | Block</title>
   </head>
   <body data-bs-theme="dark">
      <!-- Navbar -->
<header>
   <nav class="navbar navbar-expand-lg navbar-light w-100">
      <div class="container px-3">
         <a class="navbar-brand" href="./index.html"><img src="./assets/images/logo/logo.svg" alt /></a>
         <button class="navbar-toggler offcanvas-nav-btn" type="button">
            <i class="bi bi-list"></i>
         </button>
         <div class="offcanvas offcanvas-start offcanvas-nav" style="width: 20rem">
            <div class="offcanvas-header">
               <a href="./index.html" class="text-inverse"><img src="./assets/images/logo/logo.svg" alt /></a>
               <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close"></button>
            </div>
            <div class="offcanvas-body pt-0 align-items-center">
               <ul class="navbar-nav mx-auto align-items-lg-center">
                  <li class="nav-item">
                     <a class="nav-link" href="./home.html">Home</a>
                  </li>
                  <li class="nav-item">
                     <a class="nav-link" href="./scan-pay.html">Scan & Pay</a>
                  </li>
                  <li class="nav-item">
                     <a class="nav-link" href="./service.html">Services</a>
                  </li>
                  <li class="nav-item">
                     <a class="nav-link" href="./contact.html">Contact</a>
                  </li>
                  <li class="nav-item">
                     <a class="nav-link" href="./become-an-agent.html">Become An Agent</a>
                  </li>
               </ul>
               <div class="mt-3 mt-lg-0 d-flex align-items-center">
                  <a href="./signin.html" class="btn btn-light mx-2">Login</a>
                  <a href="https://play.google.com/store/apps/details?id=com.qsoft.aidapay&hl=en&pli=1" class="btn btn-primary">Create account</a>
               </div>
            </div>
         </div>
      </div>
   </nav>
</header>

      <main>
         <!--Hero section start-->
         <section
            id=""
            class="particals py-md-10 py-5"
            style="background: url(./assets/images/ai-studio/ai-hero-glow.png) no-repeat; background-size: cover; background-position: center"
            data-cue="fadeIn">
            <canvas id="starCanvas"></canvas>
            <div class="container py-xl-10">
               <div class="row py-xl-4">
                  <div class="col-xxl-8 offset-xxl-2 col-xl-8 offset-xl-2 col-lg-10 offset-lg-1 col-12">
                     <div class="text-center d-flex flex-column gap-6" data-cue="zoomIn">
                        <div class="d-flex flex-column gap-3">
                           <h1 class="display-4 mb-0"><span class="gradient-text">Unleash the Power of AI</span></h1>
                           <p class="mb-0 lead px-xxl-8">Create stunning content, generate ideas, and automate tasks with our suite of AI-powered tools designed for creators and businesses.</p>
                        </div>
                        <div class="d-flex flex-row gap-3 justify-content-center">
                           <a href="./signup.html" class="btn btn-primary">Sign up for Free</a>
                           <a href="./index.html" class="btn btn-dark">See demo</a>
                        </div>
                        <div class="d-flex flex-row align-items-center justify-content-center gap-3 mt-2">
                           <div class="avatar-group">
                              <span class="avatar avatar-md">
                                 <img alt="avatar " src="./assets/images/avatar/avatar-1.jpg" class="rounded-circle" />
                              </span>
                              <span class="avatar avatar-md">
                                 <img alt=" avatar" src="./assets/images/avatar/avatar-2.jpg" class="rounded-circle" />
                              </span>
                              <span class="avatar avatar-md">
                                 <img alt=" avatar" src="./assets/images/avatar/avatar-4.jpg" class="rounded-circle" />
                              </span>
                              <span class="avatar avatar-md">
                                 <img alt=" avatar" src="./assets/images/avatar/avatar-3.jpg" class="rounded-circle" />
                              </span>
                           </div>
                           <small class="fw-medium">Join 10,000+ users creating with AI</small>
                        </div>
                     </div>
                  </div>
               </div>
            </div>
         </section>
         <!--Hero section end-->
         <!--Ai product section start-->
         <section class="py-xl-9 pb-lg-9 pt-5 pb-6" data-cue="fadeIn">
            <div class="container">
               <div class="row">
                  <div class="col-12">
                     <div class="text-center mb-xl-7 mb-5 d-flex flex-column gap-2">
                        <h2 class="mb-0"><span class="gradient-text">Our AI Products</span></h2>
                        <p class="mb-0 lead">Powerful AI tools to enhance your creativity and productivity</p>
                     </div>
                  </div>
               </div>
               <div class="row g-5 mb-5">
                  <div class="col-lg-4 col-md-6 col-12" data-cue="fadeIn" data-cue-delay="100">
                     <a href="#!">
                        <div class="card border-gradient bg-transparent">
                           <div class="card-body d-flex flex-column gap-6 p-5">
                              <div class="bg-pink-gradient icon-shape icon-xl rounded-3">
                                 <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <g clip-path="url(#9ed2b4d4)">
                                       <path
                                          d="M14 3V7C14 7.26522 14.1054 7.51957 14.2929 7.70711C14.4804 7.89464 14.7348 8 15 8H19"
                                          stroke="white"
                                          stroke-width="1.5"
                                          stroke-linecap="round"
                                          stroke-linejoin="round"></path>
                                       <path
                                          d="M10 21H7C6.46957 21 5.96086 20.7893 5.58579 20.4142C5.21071 20.0391 5 19.5304 5 19V5C5 4.46957 5.21071 3.96086 5.58579 3.58579C5.96086 3.21071 6.46957 3 7 3H14L19 8V12"
                                          stroke="white"
                                          stroke-width="1.5"
                                          stroke-linecap="round"
                                          stroke-linejoin="round"></path>
                                       <path
                                          d="M14 21V17C14 16.4696 14.2107 15.9609 14.5858 15.5858C14.9609 15.2107 15.4696 15 16 15C16.5304 15 17.0391 15.2107 17.4142 15.5858C17.7893 15.9609 18 16.4696 18 17V21"
                                          stroke="white"
                                          stroke-width="1.5"
                                          stroke-linecap="round"
                                          stroke-linejoin="round"></path>
                                       <path d="M14 19H18" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                       <path d="M21 15V21" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                    </g>
                                    <defs>
                                       <clipPath>
                                          <rect width="24" height="24" fill="white"></rect>
                                       </clipPath>
                                    </defs>
                                 </svg>
                              </div>
                              <div class="d-flex flex-column gap-2">
                                 <h3 class="mb-0 fs-4">AI Writer</h3>
                                 <p class="mb-0">Generate blog posts, marketing copy, and creative stories with ease.</p>
                              </div>
                           </div>
                        </div>
                     </a>
                  </div>
                  <div class="col-lg-4 col-md-6 col-12" data-cue="fadeIn" data-cue-delay="200">
                     <a href="#!">
                        <div class="card border-gradient bg-transparent">
                           <div class="card-body d-flex flex-column gap-6 p-5">
                              <div class="bg-info-gradient icon-shape icon-xl rounded-3">
                                 <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <g clip-path="url(#82b990e2)">
                                       <path d="M15 8H15.01" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                       <path
                                          d="M10 21H6C5.20435 21 4.44129 20.6839 3.87868 20.1213C3.31607 19.5587 3 18.7956 3 18V6C3 5.20435 3.31607 4.44129 3.87868 3.87868C4.44129 3.31607 5.20435 3 6 3H18C18.7956 3 19.5587 3.31607 20.1213 3.87868C20.6839 4.44129 21 5.20435 21 6V11"
                                          stroke="white"
                                          stroke-width="1.5"
                                          stroke-linecap="round"
                                          stroke-linejoin="round"></path>
                                       <path d="M3 16L8 11C8.928 10.107 10.072 10.107 11 11L12 12" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                       <path
                                          d="M14 21V17C14 16.4696 14.2107 15.9609 14.5858 15.5858C14.9609 15.2107 15.4696 15 16 15C16.5304 15 17.0391 15.2107 17.4142 15.5858C17.7893 15.9609 18 16.4696 18 17V21"
                                          stroke="white"
                                          stroke-width="1.5"
                                          stroke-linecap="round"
                                          stroke-linejoin="round"></path>
                                       <path d="M14 19H18" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                       <path d="M21 15V21" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                    </g>
                                    <defs>
                                       <clipPath>
                                          <rect width="24" height="24" fill="white"></rect>
                                       </clipPath>
                                    </defs>
                                 </svg>
                              </div>
                              <div class="d-flex flex-column gap-2">
                                 <h3 class="mb-0 fs-4">AI Image Generator</h3>
                                 <p class="mb-0">Create stunning visuals, art, and designs from text descriptions.</p>
                              </div>
                           </div>
                        </div>
                     </a>
                  </div>
                  <div class="col-lg-4 col-md-6 col-12" data-cue="fadeIn" data-cue-delay="300">
                     <a href="#!">
                        <div class="card border-gradient bg-transparent">
                           <div class="card-body d-flex flex-column gap-6 p-5">
                              <div class="bg-success-gradient icon-shape icon-xl rounded-3">
                                 <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <g clip-path="url(#71372fdc)">
                                       <path
                                          d="M6 5H18C18.5304 5 19.0391 5.21071 19.4142 5.58579C19.7893 5.96086 20 6.46957 20 7V19C20 19.5304 19.7893 20.0391 19.4142 20.4142C19.0391 20.7893 18.5304 21 18 21H6C5.46957 21 4.96086 20.7893 4.58579 20.4142C4.21071 20.0391 4 19.5304 4 19V7C4 6.46957 4.21071 5.96086 4.58579 5.58579C4.96086 5.21071 5.46957 5 6 5Z"
                                          stroke="white"
                                          stroke-width="1.5"
                                          stroke-linecap="round"
                                          stroke-linejoin="round"></path>
                                       <path d="M9 16C10 16.667 11 17 12 17C13 17 14 16.667 15 16" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                       <path d="M9 7L8 3" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                       <path d="M15 7L16 3" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                       <path d="M9 12V11" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                       <path d="M15 12V11" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                    </g>
                                    <defs>
                                       <clipPath>
                                          <rect width="24" height="24" fill="white"></rect>
                                       </clipPath>
                                    </defs>
                                 </svg>
                              </div>
                              <div class="d-flex flex-column gap-2">
                                 <h3 class="mb-0 fs-4">AI Chatbot</h3>
                                 <p class="mb-0">Build conversational AI assistants for customer support and engagement.</p>
                              </div>
                           </div>
                        </div>
                     </a>
                  </div>
               </div>
               <div class="row gy-5">
                  <div class="col-lg-8" data-cue="fadeIn" data-cue-delay="400">
                     <div class="row g-5">
                        <div class="col-lg-12 col-md-6 col-12">
                           <a href="#!">
                              <div class="card border-gradient bg-transparent">
                                 <div class="card-body d-flex flex-column gap-6 p-5">
                                    <div class="bg-orange-gradient icon-shape icon-xl rounded-3">
                                       <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                          <g clip-path="url(#94d14284)">
                                             <path
                                                d="M3 17C3 17.7956 3.31607 18.5587 3.87868 19.1213C4.44129 19.6839 5.20435 20 6 20C6.79565 20 7.55871 19.6839 8.12132 19.1213C8.68393 18.5587 9 17.7956 9 17C9 16.2044 8.68393 15.4413 8.12132 14.8787C7.55871 14.3161 6.79565 14 6 14C5.20435 14 4.44129 14.3161 3.87868 14.8787C3.31607 15.4413 3 16.2044 3 17Z"
                                                stroke="white"
                                                stroke-width="1.5"
                                                stroke-linecap="round"
                                                stroke-linejoin="round"></path>
                                             <path d="M9 17V4H19V12" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                             <path d="M9 8H19" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                             <path d="M19 16L17 19H21L19 22" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                          </g>
                                          <defs>
                                             <clipPath>
                                                <rect width="24" height="24" fill="white"></rect>
                                             </clipPath>
                                          </defs>
                                       </svg>
                                    </div>
                                    <div class="d-flex flex-column gap-2">
                                       <h3 class="mb-0 fs-4">AI Video Generator</h3>
                                       <p class="mb-0">Transform text into engaging video content with customizable styles.</p>
                                    </div>
                                 </div>
                              </div>
                           </a>
                        </div>
                        <div class="col-lg-6 col-md-6 col-12">
                           <a href="#!">
                              <div class="card border-gradient bg-transparent">
                                 <div class="card-body d-flex flex-column gap-6 p-5">
                                    <div class="bg-blue-gradient icon-shape icon-xl rounded-3">
                                       <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                          <g clip-path="url(#6e6bd9ef)">
                                             <path
                                                d="M14 3V7C14 7.26522 14.1054 7.51957 14.2929 7.70711C14.4804 7.89464 14.7348 8 15 8H19"
                                                stroke="white"
                                                stroke-width="1.5"
                                                stroke-linecap="round"
                                                stroke-linejoin="round"></path>
                                             <path
                                                d="M12 21H7C6.46957 21 5.96086 20.7893 5.58579 20.4142C5.21071 20.0391 5 19.5304 5 19V5C5 4.46957 5.21071 3.96086 5.58579 3.58579C5.96086 3.21071 6.46957 3 7 3H14L19 8V11.5"
                                                stroke="white"
                                                stroke-width="1.5"
                                                stroke-linecap="round"
                                                stroke-linejoin="round"></path>
                                             <path d="M9 9H10" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                             <path d="M9 13H15" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                             <path d="M9 17H12" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                             <path
                                                d="M19 22.5C19.2053 21.6513 19.6406 20.8755 20.2581 20.2581C20.8755 19.6406 21.6513 19.2053 22.5 19C21.6513 18.7947 20.8755 18.3594 20.2581 17.7419C19.6406 17.1245 19.2053 16.3487 19 15.5C18.7947 16.3487 18.3594 17.1245 17.7419 17.7419C17.1245 18.3594 16.3487 18.7947 15.5 19C16.3487 19.2053 17.1245 19.6406 17.7419 20.2581C18.3594 20.8755 18.7947 21.6513 19 22.5Z"
                                                stroke="white"
                                                stroke-width="1.5"
                                                stroke-linecap="round"
                                                stroke-linejoin="round"></path>
                                          </g>
                                          <defs>
                                             <clipPath>
                                                <rect width="24" height="24" fill="white"></rect>
                                             </clipPath>
                                          </defs>
                                       </svg>
                                    </div>
                                    <div class="d-flex flex-column gap-2">
                                       <h3 class="mb-0 fs-4">AI Speech-to-Text</h3>
                                       <p class="mb-0">Convert audio recordings into accurate text transcriptions.</p>
                                    </div>
                                 </div>
                              </div>
                           </a>
                        </div>
                        <div class="col-lg-6 col-md-6 col-12">
                           <a href="#!">
                              <div class="card border-gradient bg-transparent">
                                 <div class="card-body d-flex flex-column gap-6 p-5">
                                    <div class="bg-pinks-gradient icon-shape icon-xl rounded-3">
                                       <svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                          <g clip-path="url(#ad35a9a2)">
                                             <path
                                                d="M6.5 6C6.5 5.46957 6.71071 4.96086 7.08579 4.58579C7.46086 4.21071 7.96957 4 8.5 4H16.5C17.0304 4 17.5391 4.21071 17.9142 4.58579C18.2893 4.96086 18.5 5.46957 18.5 6V10C18.5 10.5304 18.2893 11.0391 17.9142 11.4142C17.5391 11.7893 17.0304 12 16.5 12H8.5C7.96957 12 7.46086 11.7893 7.08579 11.4142C6.71071 11.0391 6.5 10.5304 6.5 10V6Z"
                                                stroke="white"
                                                stroke-width="1.5"
                                                stroke-linecap="round"
                                                stroke-linejoin="round"></path>
                                             <path d="M12.5 2V4" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                             <path d="M9.5 12V21" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                             <path d="M15.5 12V21" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                             <path d="M5.5 16L9.5 14" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                             <path d="M15.5 14L19.5 16" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                             <path d="M9.5 18H15.5" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                             <path d="M10.5 8V8.01" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                             <path d="M14.5 8V8.01" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                          </g>
                                          <defs>
                                             <clipPath>
                                                <rect width="24" height="24" fill="white" transform="translate(0.5)"></rect>
                                             </clipPath>
                                          </defs>
                                       </svg>
                                    </div>
                                    <div class="d-flex flex-column gap-2">
                                       <h3 class="mb-0 fs-4">AI Code Generator</h3>
                                       <p class="mb-0">Write, debug, and optimize code across multiple programming languages.</p>
                                    </div>
                                 </div>
                              </div>
                           </a>
                        </div>
                     </div>
                  </div>
                  <div class="col-lg-4 col-md-6" data-cue="fadeIn" data-cue-delay="500">
                     <a href="#!">
                        <div class="card h-100 border-gradient bg-transparent">
                           <div class="card-body d-flex flex-column gap-6 p-5">
                              <div class="bg-purle-gradient icon-shape icon-xl rounded-3">
                                 <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <g clip-path="url(#a0f45168)">
                                       <path
                                          d="M9 15H6C5.20435 15 4.44129 14.6839 3.87868 14.1213C3.31607 13.5587 3 12.7956 3 12V6C3 5.20435 3.31607 4.44129 3.87868 3.87868C4.44129 3.31607 5.20435 3 6 3H12C12.7956 3 13.5587 3.31607 14.1213 3.87868C14.6839 4.44129 15 5.20435 15 6V9"
                                          stroke="white"
                                          stroke-width="1.5"
                                          stroke-linecap="round"
                                          stroke-linejoin="round"></path>
                                       <path
                                          d="M9 12C9 11.2044 9.31607 10.4413 9.87868 9.87868C10.4413 9.31607 11.2044 9 12 9H18C18.7956 9 19.5587 9.31607 20.1213 9.87868C20.6839 10.4413 21 11.2044 21 12V18C21 18.7956 20.6839 19.5587 20.1213 20.1213C19.5587 20.6839 18.7956 21 18 21H12C11.2044 21 10.4413 20.6839 9.87868 20.1213C9.31607 19.5587 9 18.7956 9 18V12Z"
                                          stroke="white"
                                          stroke-width="1.5"
                                          stroke-linecap="round"
                                          stroke-linejoin="round"></path>
                                       <path
                                          d="M3 12L5.296 9.70404C5.74795 9.25215 6.36089 8.99829 7 8.99829C7.63911 8.99829 8.25205 9.25215 8.704 9.70404L9 10"
                                          stroke="white"
                                          stroke-width="1.5"
                                          stroke-linecap="round"
                                          stroke-linejoin="round"></path>
                                       <path d="M14 13.5V16.5L16.5 15L14 13.5Z" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                       <path d="M7 6V6.01" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                    </g>
                                    <defs>
                                       <clipPath>
                                          <rect width="24" height="24" fill="white"></rect>
                                       </clipPath>
                                    </defs>
                                 </svg>
                              </div>
                              <div class="d-flex flex-column gap-2">
                                 <h3 class="mb-0 fs-4">AI Video Generator</h3>
                                 <p class="mb-0">Transform text into engaging video content with customizable styles.</p>
                              </div>
                           </div>
                        </div>
                     </a>
                  </div>
               </div>
            </div>
         </section>
         <!--Ai product section end-->
         <!--How it work start-->
         <section class="py-xl-9 py-lg-7 py-5" data-cue="fadeIn">
            <div class="container">
               <div class="row">
                  <div class="col-12">
                     <div class="text-center mb-xl-7 mb-5 d-flex flex-column gap-2">
                        <h2 class="mb-0"><span class="gradient-text">How It Works</span></h2>
                        <p class="mb-0 lead">Get started with our AI tools in just a few simple steps</p>
                     </div>
                  </div>
               </div>
               <div class="row gy-5 gy-md-0 process-step gx-6">
                  <div class="col-lg-4 col-md-6 col-12" data-cue="fadeIn" data-duration="1000">
                     <div class="d-flex flex-column gap-6 p-xxl-6 p-md-4 text-center">
                        <div class="line">
                           <div class="icon-shape icon-md text-dark fw-semibold fs-4 mx-auto border-gradient-mix-color">1</div>
                        </div>
                        <div class="d-flex flex-column gap-2">
                           <h3 class="fs-4 mb-0">Sign Up &amp; Choose a Plan</h3>
                           <p class="mb-0">Get started in minutes by selecting the plan that fits your needs.</p>
                        </div>
                     </div>
                  </div>
                  <div class="col-lg-4 col-md-6 col-12" data-cue="fadeIn" data-duration="1500">
                     <div class="d-flex flex-column gap-6 p-xxl-6 p-md-4 text-center">
                        <div class="line">
                           <div class="icon-shape icon-md text-dark fw-semibold fs-4 mx-auto border-gradient-mix-color">2</div>
                        </div>
                        <div class="d-flex flex-column gap-2">
                           <h3 class="fs-4 mb-0">Access AI Tools &amp; Customize</h3>
                           <p class="mb-0">Use our AI-driven tools to create content, analyze data, and more.</p>
                        </div>
                     </div>
                  </div>
                  <div class="col-lg-4 col-md-6 col-12" data-cue="fadeIn" data-duration="2000">
                     <div class="d-flex flex-column gap-6 p-xxl-6 p-md-4 text-center">
                        <div class="line">
                           <div class="icon-shape icon-md text-dark fw-semibold fs-4 mx-auto border-gradient-mix-color">3</div>
                        </div>
                        <div class="d-flex flex-column gap-2">
                           <h3 class="fs-4 mb-0">Generate &amp; Download Results</h3>
                           <p class="mb-0">Download high-quality outputs for your projects effortlessly.</p>
                        </div>
                     </div>
                  </div>
               </div>
            </div>
         </section>
         <!--How it work end-->
         <!--Testimonial start-->
         <section class="py-xl-9 py-lg-7 py-5" data-cue="fadeIn">
            <div class="container-fluid">
               <div class="row">
                  <div class="col-12">
                     <div class="text-center mb-xl-7 mb-5 d-flex flex-column gap-2">
                        <h2 class="mb-0"><span class="gradient-text">What Our Clients Say</span></h2>
                        <p class="mb-0 lead">Trusted by creators and businesses worldwide</p>
                     </div>
                  </div>
               </div>
               <div class="embla" data-cue="zoomIn">
                  <div class="row">
                     <div class="embla__viewport mb-4" style="width: 100%">
                        <div class="embla__container d-flex flex-row">
                           <div class="embla__slide ms-4">
                              <div class="d-flex flex-column gap-4">
                                 <a href="#!">
                                    <div class="card card-gradient bg-gray-950">
                                       <div class="card-body d-flex flex-column gap-5">
                                          <p class="mb-0">The AI image generator has been a game-changer for our design team. We can quickly iterate on concepts that used to take days.</p>
                                          <div class="d-flex flex-row gap-3 align-content-center">
                                             <img src="./assets/images/avatar/avatar-2.jpg" alt="avatar 1" class="avatar avatar-md rounded-circle" />
                                             <div class="">
                                                <h3 class="fs-5 mb-0">Michael Chen</h3>
                                                <small>Marketing Director,TechGrowth</small>
                                             </div>
                                          </div>
                                       </div>
                                    </div>
                                 </a>
                                 <a href="#!">
                                    <div class="card card-gradient bg-gray-950">
                                       <div class="card-body d-flex flex-column gap-5">
                                          <p class="mb-0">As a solo entrepreneur, these AI tools have given me capabilities that would normally require an entire team. Incredible value.</p>
                                          <div class="d-flex flex-row gap-3 align-content-center">
                                             <img src="./assets/images/avatar/avatar-5.jpg" alt="avatar 1" class="avatar avatar-md rounded-circle" />
                                             <div class="">
                                                <h3 class="fs-5 mb-0">Priya Patel</h3>
                                                <small>Founder,NexGen Solutions</small>
                                             </div>
                                          </div>
                                       </div>
                                    </div>
                                 </a>
                              </div>
                           </div>
                           <div class="embla__slide">
                              <div class="d-flex flex-column gap-4">
                                 <a href="#!">
                                    <div class="card card-gradient bg-gray-950">
                                       <div class="card-body d-flex flex-column gap-5">
                                          <p class="mb-0">The AI image generator has been a game-changer for our design team. We can quickly iterate on concepts that used to take days.</p>
                                          <div class="d-flex flex-row gap-3 align-content-center">
                                             <img src="./assets/images/avatar/avatar-2.jpg" alt="avatar 1" class="avatar avatar-md rounded-circle" />
                                             <div class="">
                                                <h3 class="fs-5 mb-0">Michael Chen</h3>
                                                <small>Marketing Director,TechGrowth</small>
                                             </div>
                                          </div>
                                       </div>
                                    </div>
                                 </a>
                                 <a href="#!">
                                    <div class="card card-gradient bg-gray-950">
                                       <div class="card-body d-flex flex-column gap-5">
                                          <p class="mb-0">As a solo entrepreneur, these AI tools have given me capabilities that would normally require an entire team. Incredible value.</p>
                                          <div class="d-flex flex-row gap-3 align-content-center">
                                             <img src="./assets/images/avatar/avatar-5.jpg" alt="avatar 1" class="avatar avatar-md rounded-circle" />
                                             <div class="">
                                                <h3 class="fs-5 mb-0">Priya Patel</h3>
                                                <small>Founder,NexGen Solutions</small>
                                             </div>
                                          </div>
                                       </div>
                                    </div>
                                 </a>
                              </div>
                           </div>
                           <div class="embla__slide">
                              <div class="d-flex flex-column gap-4">
                                 <a href="#!">
                                    <div class="card card-gradient bg-gray-950">
                                       <div class="card-body d-flex flex-column gap-5">
                                          <p class="mb-0">
                                             Lorem ipsum dolor sit amet consectetur adipisicing elit. Amet dolore aspernatur architecto omnis ipsam minima nulla quae. Provident repudiandae quaerat
                                             ducimus possimus amet. Ducimus, neque?
                                          </p>
                                          <div class="d-flex flex-row gap-3 align-content-center">
                                             <img src="./assets/images/avatar/avatar-3.jpg" alt="avatar 1" class="avatar avatar-md rounded-circle" />
                                             <div class="">
                                                <h3 class="fs-5 mb-0">Ryan Michael</h3>
                                                <small>Director</small>
                                             </div>
                                          </div>
                                       </div>
                                    </div>
                                 </a>
                                 <a href="#!">
                                    <div class="card card-gradient bg-gray-950">
                                       <div class="card-body d-flex flex-column gap-5">
                                          <p class="mb-0">The AI image generator has been a game-changer for our design team. We can quickly iterate on concepts that used to take days.</p>
                                          <div class="d-flex flex-row gap-3 align-content-center">
                                             <img src="./assets/images/avatar/avatar-2.jpg" alt="avatar 1" class="avatar avatar-md rounded-circle" />
                                             <div class="">
                                                <h3 class="fs-5 mb-0">Michael Chen</h3>
                                                <small>Marketing Director,TechGrowth</small>
                                             </div>
                                          </div>
                                       </div>
                                    </div>
                                 </a>
                              </div>
                           </div>
                           <div class="embla__slide">
                              <div class="d-flex flex-column gap-4">
                                 <a href="#!">
                                    <div class="card card-gradient bg-gray-950">
                                       <div class="card-body d-flex flex-column gap-5">
                                          <p class="mb-0">
                                             The predictive analytics feature has allowed us to make data-driven decisions with confidence, leading to a 40% increase in our campaign ROI.
                                          </p>
                                          <div class="d-flex flex-row gap-3 align-content-center">
                                             <img src="./assets/images/avatar/avatar-8.jpg" alt="avatar 1" class="avatar avatar-md rounded-circle" />
                                             <div class="">
                                                <h3 class="fs-5 mb-0">James Rodriguez</h3>
                                                <small>Data Analyst,Insight Innovations</small>
                                             </div>
                                          </div>
                                       </div>
                                    </div>
                                 </a>
                                 <a href="#!">
                                    <div class="card card-gradient bg-gray-950">
                                       <div class="card-body d-flex flex-column gap-5">
                                          <p class="mb-0">
                                             Lorem ipsum dolor sit amet consectetur adipisicing elit. Amet dolore aspernatur architecto omnis ipsam minima nulla quae. Provident repudiandae quaerat
                                             ducimus possimus amet. Ducimus, neque?
                                          </p>
                                          <div class="d-flex flex-row gap-3 align-content-center">
                                             <img src="./assets/images/avatar/avatar-3.jpg" alt="avatar 1" class="avatar avatar-md rounded-circle" />
                                             <div class="">
                                                <h3 class="fs-5 mb-0">Ryan Michael</h3>
                                                <small>Director</small>
                                             </div>
                                          </div>
                                       </div>
                                    </div>
                                 </a>
                              </div>
                           </div>
                           <div class="embla__slide">
                              <div class="d-flex flex-column gap-4">
                                 <a href="#!">
                                    <div class="card card-gradient bg-gray-950">
                                       <div class="card-body d-flex flex-column gap-5">
                                          <p class="mb-0">The AI image generator has been a game-changer for our design team. We can quickly iterate on concepts that used to take days.</p>
                                          <div class="d-flex flex-row gap-3 align-content-center">
                                             <img src="./assets/images/avatar/avatar-2.jpg" alt="avatar 1" class="avatar avatar-md rounded-circle" />
                                             <div class="">
                                                <h3 class="fs-5 mb-0">Michael Chen</h3>
                                                <small>Marketing Director,TechGrowth</small>
                                             </div>
                                          </div>
                                       </div>
                                    </div>
                                 </a>
                                 <a href="#!">
                                    <div class="card card-gradient bg-gray-950">
                                       <div class="card-body d-flex flex-column gap-5">
                                          <p class="mb-0">Lorem ipsum, dolor sit amet consectetur adipisicing elit. Et vel, maxime odio totam temporibus laborum aliquid quis nam!</p>
                                          <div class="d-flex flex-row gap-3 align-content-center">
                                             <img src="./assets/images/avatar/avatar-1.jpg" alt="avatar 1" class="avatar avatar-md rounded-circle" />
                                             <div class="">
                                                <h3 class="fs-5 mb-0">John Deo</h3>
                                                <small>CTO,TechGrowth</small>
                                             </div>
                                          </div>
                                       </div>
                                    </div>
                                 </a>
                              </div>
                           </div>
                           <div class="embla__slide">
                              <div class="d-flex flex-column gap-4">
                                 <a href="#!">
                                    <div class="card card-gradient bg-gray-950">
                                       <div class="card-body d-flex flex-column gap-5">
                                          <p class="mb-0">Lorem ipsum, dolor sit amet consectetur adipisicing elit. Et vel, maxime odio totam temporibus laborum aliquid quis nam!</p>
                                          <div class="d-flex flex-row gap-3 align-content-center">
                                             <img src="./assets/images/avatar/avatar-1.jpg" alt="avatar 1" class="avatar avatar-md rounded-circle" />
                                             <div class="">
                                                <h3 class="fs-5 mb-0">John Deo</h3>
                                                <small>CTO,TechGrowth</small>
                                             </div>
                                          </div>
                                       </div>
                                    </div>
                                 </a>
                                 <a href="#!">
                                    <div class="card card-gradient bg-gray-950">
                                       <div class="card-body d-flex flex-column gap-5">
                                          <p class="mb-0">
                                             Lorem ipsum dolor sit amet consectetur adipisicing elit. Nobis rerum dolorum quae vel, doloribus itaque in, sequi quibusdam animi ipsa, laborum sit?
                                          </p>
                                          <div class="d-flex flex-row gap-3 align-content-center">
                                             <img src="./assets/images/avatar/avatar-6.jpg" alt="avatar 1" class="avatar avatar-md rounded-circle" />
                                             <div class="">
                                                <h3 class="fs-5 mb-0">Misty Roy</h3>
                                                <small>Manager,AI Studio</small>
                                             </div>
                                          </div>
                                       </div>
                                    </div>
                                 </a>
                              </div>
                           </div>
                           <div class="embla__slide">
                              <div class="d-flex flex-column gap-4">
                                 <a href="#!">
                                    <div class="card card-gradient bg-gray-950">
                                       <div class="card-body d-flex flex-column gap-5">
                                          <p class="mb-0">
                                             Lorem ipsum dolor sit amet consectetur adipisicing elit. Nobis rerum dolorum quae vel, doloribus itaque in, sequi quibusdam animi ipsa, laborum sit?
                                          </p>
                                          <div class="d-flex flex-row gap-3 align-content-center">
                                             <img src="./assets/images/avatar/avatar-6.jpg" alt="avatar 1" class="avatar avatar-md rounded-circle" />
                                             <div class="">
                                                <h3 class="fs-5 mb-0">Misty Roy</h3>
                                                <small>Manager,AI Studio</small>
                                             </div>
                                          </div>
                                       </div>
                                    </div>
                                 </a>
                                 <a href="#!">
                                    <div class="card card-gradient bg-gray-950">
                                       <div class="card-body d-flex flex-column gap-5">
                                          <p class="mb-0">
                                             Lorem ipsum dolor sit amet consectetur adipisicing elit. Iusto, debitis itaque impedit, officiis atque aut delectus deleniti dolorum explicabo sequi dicta
                                             neque nihil voluptate similique repellat quidem quas quo molestiae!
                                          </p>
                                          <div class="d-flex flex-row gap-3 align-content-center">
                                             <img src="./assets/images/avatar/avatar-3.jpg" alt="avatar 1" class="avatar avatar-md rounded-circle" />
                                             <div class="">
                                                <h3 class="fs-5 mb-0">Sherleen Khoise</h3>
                                                <small>PO,AI Studio</small>
                                             </div>
                                          </div>
                                       </div>
                                    </div>
                                 </a>
                              </div>
                           </div>
                           <div class="embla__slide">
                              <div class="d-flex flex-column gap-4">
                                 <a href="#!">
                                    <div class="card card-gradient bg-gray-950">
                                       <div class="card-body d-flex flex-column gap-5">
                                          <p class="mb-0">
                                             Lorem ipsum dolor sit amet consectetur adipisicing elit. Iusto, debitis itaque impedit, officiis atque aut delectus deleniti dolorum explicabo sequi dicta
                                             neque nihil voluptate similique repellat quidem quas quo molestiae!
                                          </p>
                                          <div class="d-flex flex-row gap-3 align-content-center">
                                             <img src="./assets/images/avatar/avatar-3.jpg" alt="avatar 1" class="avatar avatar-md rounded-circle" />
                                             <div class="">
                                                <h3 class="fs-5 mb-0">Sherleen Khoise</h3>
                                                <small>PO,AI Studio</small>
                                             </div>
                                          </div>
                                       </div>
                                    </div>
                                 </a>
                                 <a href="#!">
                                    <div class="card card-gradient bg-gray-950">
                                       <div class="card-body d-flex flex-column gap-5">
                                          <p class="mb-0">
                                             Lorem ipsum dolor sit amet consectetur adipisicing elit. Nobis rerum dolorum quae vel, doloribus itaque in, sequi quibusdam animi ipsa, laborum sit?
                                          </p>
                                          <div class="d-flex flex-row gap-3 align-content-center">
                                             <img src="./assets/images/avatar/avatar-6.jpg" alt="avatar 1" class="avatar avatar-md rounded-circle" />
                                             <div class="">
                                                <h3 class="fs-5 mb-0">Misty Roy</h3>
                                                <small>Manager,AI Studio</small>
                                             </div>
                                          </div>
                                       </div>
                                    </div>
                                 </a>
                              </div>
                           </div>
                        </div>
                     </div>
                     <div class="embla__dots"></div>
                  </div>
               </div>
            </div>
         </section>
         <!--Testimonial end-->
         <!--Pricing section start-->
         <section class="py-xl-9 py-lg-7 py-5" data-cue="fadeIn">
            <div class="container pb-xl-5">
               <div class="row">
                  <div class="col-12">
                     <div class="text-center mb-xl-7 mb-5 d-flex flex-column gap-2">
                        <h2 class="mb-0"><span class="gradient-text">Pricing Plans</span></h2>
                        <p class="mb-0 lead">Choose the perfect plan for your needs</p>
                     </div>
                  </div>
               </div>
               <div class="row gy-5 gy-xl-0">
                  <div class="col-xl-4 col-md-6 col-12" data-cue="slideInLeft">
                     <div class="card bg-gray-950">
                        <div class="card-body d-flex flex-column gap-6 p-5">
                           <div class="d-flex flex-column gap-3">
                              <div class="d-flex flex-column gap-1">
                                 <h3 class="mb-0">Free</h3>
                                 <p class="mb-0">Basic access to essential AI tools</p>
                              </div>
                              <div class="d-flex flex-row align-items-center gap-2">
                                 <h3 class="fs-1 fw-bold mb-0">$0</h3>
                                 <span>forever</span>
                              </div>
                              <ul class="list-unstyled mb-0 d-flex flex-column gap-2">
                                 <li class="d-flex flex-row gap-2 align-items-center">
                                    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                       <g clip-path="url(#4997e58d)">
                                          <path d="M5.83333 9.99992L9.99999 14.1666L18.3333 5.83325" stroke="#6FDB93" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                          <path
                                             d="M1.66667 9.99992L5.83334 14.1666M10 9.99992L14.1667 5.83325"
                                             stroke="#6FDB93"
                                             stroke-width="1.5"
                                             stroke-linecap="round"
                                             stroke-linejoin="round"></path>
                                       </g>
                                       <defs>
                                          <clipPath>
                                             <rect width="20" height="20" fill="white"></rect>
                                          </clipPath>
                                       </defs>
                                    </svg>
                                    <span>5 AI generations per day</span>
                                 </li>
                                 <li class="d-flex flex-row gap-2 align-items-center">
                                    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                       <g clip-path="url(#4997e58d)">
                                          <path d="M5.83333 9.99992L9.99999 14.1666L18.3333 5.83325" stroke="#6FDB93" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                          <path
                                             d="M1.66667 9.99992L5.83334 14.1666M10 9.99992L14.1667 5.83325"
                                             stroke="#6FDB93"
                                             stroke-width="1.5"
                                             stroke-linecap="round"
                                             stroke-linejoin="round"></path>
                                       </g>
                                       <defs>
                                          <clipPath>
                                             <rect width="20" height="20" fill="white"></rect>
                                          </clipPath>
                                       </defs>
                                    </svg>
                                    <span>Basic text generation</span>
                                 </li>
                                 <li class="d-flex flex-row gap-2 align-items-center">
                                    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                       <g clip-path="url(#4997e58d)">
                                          <path d="M5.83333 9.99992L9.99999 14.1666L18.3333 5.83325" stroke="#6FDB93" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                          <path
                                             d="M1.66667 9.99992L5.83334 14.1666M10 9.99992L14.1667 5.83325"
                                             stroke="#6FDB93"
                                             stroke-width="1.5"
                                             stroke-linecap="round"
                                             stroke-linejoin="round"></path>
                                       </g>
                                       <defs>
                                          <clipPath>
                                             <rect width="20" height="20" fill="white"></rect>
                                          </clipPath>
                                       </defs>
                                    </svg>
                                    <span>Simple image creation</span>
                                 </li>
                                 <li class="d-flex flex-row gap-2 align-items-center">
                                    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                       <g clip-path="url(#4997e58d)">
                                          <path d="M5.83333 9.99992L9.99999 14.1666L18.3333 5.83325" stroke="#6FDB93" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                          <path
                                             d="M1.66667 9.99992L5.83334 14.1666M10 9.99992L14.1667 5.83325"
                                             stroke="#6FDB93"
                                             stroke-width="1.5"
                                             stroke-linecap="round"
                                             stroke-linejoin="round"></path>
                                       </g>
                                       <defs>
                                          <clipPath>
                                             <rect width="20" height="20" fill="white"></rect>
                                          </clipPath>
                                       </defs>
                                    </svg>
                                    <span>Standard response time</span>
                                 </li>
                                 <li class="d-flex flex-row gap-2 align-items-center">
                                    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                       <g clip-path="url(#4997e58d)">
                                          <path d="M5.83333 9.99992L9.99999 14.1666L18.3333 5.83325" stroke="#6FDB93" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                          <path
                                             d="M1.66667 9.99992L5.83334 14.1666M10 9.99992L14.1667 5.83325"
                                             stroke="#6FDB93"
                                             stroke-width="1.5"
                                             stroke-linecap="round"
                                             stroke-linejoin="round"></path>
                                       </g>
                                       <defs>
                                          <clipPath>
                                             <rect width="20" height="20" fill="white"></rect>
                                          </clipPath>
                                       </defs>
                                    </svg>
                                    <span>Community support</span>
                                 </li>
                              </ul>
                           </div>
                           <div>
                              <a href="#!" class="btn btn-dark">Get Started</a>
                           </div>
                        </div>
                     </div>
                  </div>
                  <div class="col-xl-4 col-md-6 col-12" data-cue="zoomOut">
                     <div class="card position-relative bg-gray-950 border-gradient-mix-color">
                        <div class="card-body d-flex flex-column gap-6 p-5 z-2">
                           <div class="d-flex flex-column gap-3">
                              <div class="position-absolute top-0 end-0 translate-middle">
                                 <span class="badge bg-primary rounded-pill fw-medium border border-dark border-2">Most Popular</span>
                              </div>
                              <div class="d-flex flex-column gap-1">
                                 <h3 class="mb-0">Pro</h3>
                                 <p class="mb-0">Advanced features for professionals</p>
                              </div>
                              <div class="d-flex flex-row align-items-center gap-2">
                                 <h3 class="fs-1 fw-bold mb-0">$29</h3>
                                 <span>per month</span>
                              </div>
                              <ul class="list-unstyled mb-0 d-flex flex-column gap-2">
                                 <li class="d-flex flex-row gap-2 align-items-center">
                                    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                       <g clip-path="url(#4997e58d)">
                                          <path d="M5.83333 9.99992L9.99999 14.1666L18.3333 5.83325" stroke="#6FDB93" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                          <path
                                             d="M1.66667 9.99992L5.83334 14.1666M10 9.99992L14.1667 5.83325"
                                             stroke="#6FDB93"
                                             stroke-width="1.5"
                                             stroke-linecap="round"
                                             stroke-linejoin="round"></path>
                                       </g>
                                       <defs>
                                          <clipPath>
                                             <rect width="20" height="20" fill="white"></rect>
                                          </clipPath>
                                       </defs>
                                    </svg>
                                    <span>Unlimited AI generations</span>
                                 </li>
                                 <li class="d-flex flex-row gap-2 align-items-center">
                                    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                       <g clip-path="url(#4997e58d)">
                                          <path d="M5.83333 9.99992L9.99999 14.1666L18.3333 5.83325" stroke="#6FDB93" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                          <path
                                             d="M1.66667 9.99992L5.83334 14.1666M10 9.99992L14.1667 5.83325"
                                             stroke="#6FDB93"
                                             stroke-width="1.5"
                                             stroke-linecap="round"
                                             stroke-linejoin="round"></path>
                                       </g>
                                       <defs>
                                          <clipPath>
                                             <rect width="20" height="20" fill="white"></rect>
                                          </clipPath>
                                       </defs>
                                    </svg>
                                    <span>Advanced text &amp; image tools</span>
                                 </li>
                                 <li class="d-flex flex-row gap-2 align-items-center">
                                    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                       <g clip-path="url(#4997e58d)">
                                          <path d="M5.83333 9.99992L9.99999 14.1666L18.3333 5.83325" stroke="#6FDB93" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                          <path
                                             d="M1.66667 9.99992L5.83334 14.1666M10 9.99992L14.1667 5.83325"
                                             stroke="#6FDB93"
                                             stroke-width="1.5"
                                             stroke-linecap="round"
                                             stroke-linejoin="round"></path>
                                       </g>
                                       <defs>
                                          <clipPath>
                                             <rect width="20" height="20" fill="white"></rect>
                                          </clipPath>
                                       </defs>
                                    </svg>
                                    <span>Audio &amp; video generation</span>
                                 </li>
                                 <li class="d-flex flex-row gap-2 align-items-center">
                                    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                       <g clip-path="url(#4997e58d)">
                                          <path d="M5.83333 9.99992L9.99999 14.1666L18.3333 5.83325" stroke="#6FDB93" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                          <path
                                             d="M1.66667 9.99992L5.83334 14.1666M10 9.99992L14.1667 5.83325"
                                             stroke="#6FDB93"
                                             stroke-width="1.5"
                                             stroke-linecap="round"
                                             stroke-linejoin="round"></path>
                                       </g>
                                       <defs>
                                          <clipPath>
                                             <rect width="20" height="20" fill="white"></rect>
                                          </clipPath>
                                       </defs>
                                    </svg>
                                    <span>Priority processing</span>
                                 </li>
                                 <li class="d-flex flex-row gap-2 align-items-center">
                                    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                       <g clip-path="url(#4997e58d)">
                                          <path d="M5.83333 9.99992L9.99999 14.1666L18.3333 5.83325" stroke="#6FDB93" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                          <path
                                             d="M1.66667 9.99992L5.83334 14.1666M10 9.99992L14.1667 5.83325"
                                             stroke="#6FDB93"
                                             stroke-width="1.5"
                                             stroke-linecap="round"
                                             stroke-linejoin="round"></path>
                                       </g>
                                       <defs>
                                          <clipPath>
                                             <rect width="20" height="20" fill="white"></rect>
                                          </clipPath>
                                       </defs>
                                    </svg>
                                    <span>Email support</span>
                                 </li>
                                 <li class="d-flex flex-row gap-2 align-items-center">
                                    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                       <g clip-path="url(#4997e58d)">
                                          <path d="M5.83333 9.99992L9.99999 14.1666L18.3333 5.83325" stroke="#6FDB93" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                          <path
                                             d="M1.66667 9.99992L5.83334 14.1666M10 9.99992L14.1667 5.83325"
                                             stroke="#6FDB93"
                                             stroke-width="1.5"
                                             stroke-linecap="round"
                                             stroke-linejoin="round"></path>
                                       </g>
                                       <defs>
                                          <clipPath>
                                             <rect width="20" height="20" fill="white"></rect>
                                          </clipPath>
                                       </defs>
                                    </svg>
                                    <span>API access</span>
                                 </li>
                              </ul>
                           </div>
                           <div>
                              <a href="#!" class="btn btn-primary">Start Free Trial</a>
                           </div>
                        </div>
                     </div>
                  </div>
                  <div class="col-xl-4 col-md-6 col-12" data-cue="slideInRight">
                     <div class="card bg-gray-950">
                        <div class="card-body d-flex flex-column gap-6 p-5">
                           <div class="d-flex flex-column gap-3">
                              <div class="d-flex flex-column gap-1">
                                 <h3 class="mb-0">Enterprise</h3>
                                 <p class="mb-0">Custom solutions for teams</p>
                              </div>
                              <div class="d-flex flex-row gap-2">
                                 <h3 class="fs-1 fw-bold mb-0">Custom</h3>
                                 <span class="mt-auto">pricing</span>
                              </div>
                              <ul class="list-unstyled mb-0 d-flex flex-column gap-2">
                                 <li class="d-flex flex-row gap-2 align-items-center">
                                    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                       <g clip-path="url(#4997e58d)">
                                          <path d="M5.83333 9.99992L9.99999 14.1666L18.3333 5.83325" stroke="#6FDB93" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                          <path
                                             d="M1.66667 9.99992L5.83334 14.1666M10 9.99992L14.1667 5.83325"
                                             stroke="#6FDB93"
                                             stroke-width="1.5"
                                             stroke-linecap="round"
                                             stroke-linejoin="round"></path>
                                       </g>
                                       <defs>
                                          <clipPath>
                                             <rect width="20" height="20" fill="white"></rect>
                                          </clipPath>
                                       </defs>
                                    </svg>
                                    <span>Unlimited AI generations</span>
                                 </li>
                                 <li class="d-flex flex-row gap-2 align-items-center">
                                    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                       <g clip-path="url(#4997e58d)">
                                          <path d="M5.83333 9.99992L9.99999 14.1666L18.3333 5.83325" stroke="#6FDB93" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                          <path
                                             d="M1.66667 9.99992L5.83334 14.1666M10 9.99992L14.1667 5.83325"
                                             stroke="#6FDB93"
                                             stroke-width="1.5"
                                             stroke-linecap="round"
                                             stroke-linejoin="round"></path>
                                       </g>
                                       <defs>
                                          <clipPath>
                                             <rect width="20" height="20" fill="white"></rect>
                                          </clipPath>
                                       </defs>
                                    </svg>
                                    <span>All Pro features</span>
                                 </li>
                                 <li class="d-flex flex-row gap-2 align-items-center">
                                    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                       <g clip-path="url(#4997e58d)">
                                          <path d="M5.83333 9.99992L9.99999 14.1666L18.3333 5.83325" stroke="#6FDB93" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                          <path
                                             d="M1.66667 9.99992L5.83334 14.1666M10 9.99992L14.1667 5.83325"
                                             stroke="#6FDB93"
                                             stroke-width="1.5"
                                             stroke-linecap="round"
                                             stroke-linejoin="round"></path>
                                       </g>
                                       <defs>
                                          <clipPath>
                                             <rect width="20" height="20" fill="white"></rect>
                                          </clipPath>
                                       </defs>
                                    </svg>
                                    <span>Custom model training</span>
                                 </li>
                                 <li class="d-flex flex-row gap-2 align-items-center">
                                    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                       <g clip-path="url(#4997e58d)">
                                          <path d="M5.83333 9.99992L9.99999 14.1666L18.3333 5.83325" stroke="#6FDB93" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                          <path
                                             d="M1.66667 9.99992L5.83334 14.1666M10 9.99992L14.1667 5.83325"
                                             stroke="#6FDB93"
                                             stroke-width="1.5"
                                             stroke-linecap="round"
                                             stroke-linejoin="round"></path>
                                       </g>
                                       <defs>
                                          <clipPath>
                                             <rect width="20" height="20" fill="white"></rect>
                                          </clipPath>
                                       </defs>
                                    </svg>
                                    <span>Dedicated account manager</span>
                                 </li>
                                 <li class="d-flex flex-row gap-2 align-items-center">
                                    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                       <g clip-path="url(#4997e58d)">
                                          <path d="M5.83333 9.99992L9.99999 14.1666L18.3333 5.83325" stroke="#6FDB93" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                          <path
                                             d="M1.66667 9.99992L5.83334 14.1666M10 9.99992L14.1667 5.83325"
                                             stroke="#6FDB93"
                                             stroke-width="1.5"
                                             stroke-linecap="round"
                                             stroke-linejoin="round"></path>
                                       </g>
                                       <defs>
                                          <clipPath>
                                             <rect width="20" height="20" fill="white"></rect>
                                          </clipPath>
                                       </defs>
                                    </svg>
                                    <span>24/7 priority support</span>
                                 </li>
                                 <li class="d-flex flex-row gap-2 align-items-center">
                                    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                       <g clip-path="url(#4997e58d)">
                                          <path d="M5.83333 9.99992L9.99999 14.1666L18.3333 5.83325" stroke="#6FDB93" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                          <path
                                             d="M1.66667 9.99992L5.83334 14.1666M10 9.99992L14.1667 5.83325"
                                             stroke="#6FDB93"
                                             stroke-width="1.5"
                                             stroke-linecap="round"
                                             stroke-linejoin="round"></path>
                                       </g>
                                       <defs>
                                          <clipPath>
                                             <rect width="20" height="20" fill="white"></rect>
                                          </clipPath>
                                       </defs>
                                    </svg>
                                    <span>Advanced security &amp; compliance</span>
                                 </li>
                                 <li class="d-flex flex-row gap-2 align-items-center">
                                    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                       <g clip-path="url(#4997e58d)">
                                          <path d="M5.83333 9.99992L9.99999 14.1666L18.3333 5.83325" stroke="#6FDB93" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                          <path
                                             d="M1.66667 9.99992L5.83334 14.1666M10 9.99992L14.1667 5.83325"
                                             stroke="#6FDB93"
                                             stroke-width="1.5"
                                             stroke-linecap="round"
                                             stroke-linejoin="round"></path>
                                       </g>
                                       <defs>
                                          <clipPath>
                                             <rect width="20" height="20" fill="white"></rect>
                                          </clipPath>
                                       </defs>
                                    </svg>
                                    <span>Custom integrations</span>
                                 </li>
                              </ul>
                           </div>
                           <div>
                              <a href="#!" class="btn btn-dark">Get Started</a>
                           </div>
                        </div>
                     </div>
                  </div>
               </div>
            </div>
         </section>
         <!--Pricing section end-->
         <!--Faq start-->
         <section class="py-xl-9 pb-md-8 pt-lg-8 pb-lg-10 py-5" data-cue="fadeIn">
            <div class="container">
               <div class="row">
                  <div class="col-lg-5 col-12" data-cue="zoomIn">
                     <div class="mb-7 mb-md-0 me-lg-7 text-md-center text-lg-start">
                        <div class="mb-4">
                           <h2 class="mb-3"><span class="gradient-text">Frequently asked questions</span></h2>
                           <p class="mb-0 lead">
                              Can’t find any answer for your question?
                              <br />
                              Ask our
                              <a href="./contact-1.html" class="text-primary">customer support</a>
                           </p>
                        </div>
                     </div>
                  </div>
                  <div class="col-lg-7 col-12" data-cue="zoomIn">
                     <div class="accordion" id="accordionExample">
                        <div class="border mb-2 rounded-3 p-3">
                           <h2 class="h5 mb-0">
                              <a
                                 href="#"
                                 class="text-reset d-flex justify-content-between align-items-center"
                                 data-bs-toggle="collapse"
                                 data-bs-target="#collapseOne"
                                 aria-expanded="false"
                                 aria-controls="collapseOne">
                                 Can I trial block before paying?
                                 <span class="chevron-arrow"><i class="bi bi-chevron-down"></i></span>
                              </a>
                           </h2>
                           <div id="collapseOne" class="accordion-collapse collapse" data-bs-parent="#accordionExample">
                              <div class="mt-3">Lorem ipsum dolor sit amet, consectetur adipisicing elit. Quae harum adipisci possimus et. Iusto pariatur iste nam incidunt ratione modi.</div>
                           </div>
                        </div>

                        <div class="border mb-2 rounded-3 p-3">
                           <h2 class="h5 mb-0">
                              <a
                                 href="#"
                                 class="text-reset d-flex justify-content-between align-items-center"
                                 data-bs-toggle="collapse"
                                 data-bs-target="#collapseTwo"
                                 aria-expanded="true"
                                 aria-controls="collapseTwo">
                                 How are additional plan billed?
                                 <span class="chevron-arrow"><i class="bi bi-chevron-down"></i></span>
                              </a>
                           </h2>
                           <div id="collapseTwo" class="accordion-collapse collapse show" data-bs-parent="#accordionExample">
                              <div class="mt-3">
                                 Sed urna felis, dapibus quis leo nec, luctus auctor augue. Nam gravida placerat sem vitae rutrum. Integer accumsan, enim et facilisis eleifend, ante ligula ornare
                                 nulla, sed pharetra tortor diam eget magna.
                              </div>
                           </div>
                        </div>
                        <div class="border mb-2 rounded-3 p-3">
                           <h2 class="h5 mb-0">
                              <a
                                 href="#"
                                 class="text-reset d-flex justify-content-between align-items-center"
                                 data-bs-toggle="collapse"
                                 data-bs-target="#collapseThree"
                                 aria-expanded="false"
                                 aria-controls="collapseThree">
                                 When should I change my plan?
                                 <span class="chevron-arrow"><i class="bi bi-chevron-down"></i></span>
                              </a>
                           </h2>
                           <div id="collapseThree" class="accordion-collapse collapse" data-bs-parent="#accordionExample">
                              <div class="mt-3">
                                 Lorem ipsum dolor sit, amet consectetur adipisicing elit. Inventore tenetur cum doloremque iusto molestiae. Minus beatae quam cumque modi quidem asperiores aliquam
                                 pariatur in iste.
                              </div>
                           </div>
                        </div>
                        <div class="border mb-2 rounded-3 p-3">
                           <h2 class="h5 mb-0">
                              <a
                                 href="#"
                                 class="text-reset d-flex justify-content-between align-items-center"
                                 data-bs-toggle="collapse"
                                 data-bs-target="#collapseFour"
                                 aria-expanded="false"
                                 aria-controls="collapseFour">
                                 What payment methods do you offer?
                                 <span class="chevron-arrow"><i class="bi bi-chevron-down"></i></span>
                              </a>
                           </h2>
                           <div id="collapseFour" class="accordion-collapse collapse" data-bs-parent="#accordionExample">
                              <div class="mt-3">
                                 Lorem ipsum dolor sit, amet consectetur adipisicing elit. Inventore tenetur cum doloremque iusto molestiae. Minus beatae quam cumque modi quidem asperiores aliquam
                                 pariatur in iste.
                              </div>
                           </div>
                        </div>
                        <div class="border mb-2 rounded-3 p-3">
                           <h2 class="h5 mb-0">
                              <a
                                 href="#"
                                 class="text-reset d-flex justify-content-between align-items-center"
                                 data-bs-toggle="collapse"
                                 data-bs-target="#collapseFive"
                                 aria-expanded="false"
                                 aria-controls="collapseFive">
                                 What is your refund policy?
                                 <span class="chevron-arrow"><i class="bi bi-chevron-down"></i></span>
                              </a>
                           </h2>
                           <div id="collapseFive" class="accordion-collapse collapse" data-bs-parent="#accordionExample">
                              <div class="mt-3">
                                 Lorem ipsum dolor sit, amet consectetur adipisicing elit. Inventore tenetur cum doloremque iusto molestiae. Minus beatae quam cumque modi quidem asperiores aliquam
                                 pariatur in iste.
                              </div>
                           </div>
                        </div>
                        <div class="border mb-2 rounded-3 p-3">
                           <h2 class="h5 mb-0">
                              <a
                                 href="#"
                                 class="text-reset d-flex justify-content-between align-items-center"
                                 data-bs-toggle="collapse"
                                 data-bs-target="#collapseSix"
                                 aria-expanded="false"
                                 aria-controls="collapseSix">
                                 How are paid plans billed when moving other plan?
                                 <span class="chevron-arrow"><i class="bi bi-chevron-down"></i></span>
                              </a>
                           </h2>
                           <div id="collapseSix" class="accordion-collapse collapse" data-bs-parent="#accordionExample">
                              <div class="mt-3">
                                 Lorem ipsum dolor sit, amet consectetur adipisicing elit. Inventore tenetur cum doloremque iusto molestiae. Minus beatae quam cumque modi quidem asperiores aliquam
                                 pariatur in iste.
                              </div>
                           </div>
                        </div>
                     </div>
                  </div>
               </div>
            </div>
         </section>
         <!--Faq end-->
         <!--Call to action start-->
         <section data-cue="fadeIn" class="py-lg-9 py-md-8 py-5" style="background: url(./assets/images/ai-studio/cta-glows.png) no-repeat; background-size: cover; background-position: center">
            <div class="container">
               <div class="row">
                  <div class="col-xxl-6 offset-xxl-3 col-12">
                     <div class="d-flex flex-column gap-6">
                        <div class="text-center d-flex flex-column gap-2" data-cue="zoomOut">
                           <h2 class="mb-0 display-6">Ready to Transform Your Creative Process?</h2>

                           <p class="mb-0 px-xl-5 lead">Join thousands of creators and businesses using our AI to work smarter, faster, and more creatively.</p>
                        </div>
                        <div class="d-flex flex-row gap-3 align-content-center justify-content-center">
                           <a href="#!" class="btn btn-primary">Get Started Free</a>
                           <a href="./index.html" class="btn btn-dark">See demo</a>
                        </div>
                        <div class="d-flex justify-content-center">
                           <small class="fw-medium">No credit card required. Start with a free account.</small>
                        </div>
                     </div>
                  </div>
               </div>
            </div>
         </section>
         <!--Call to action end-->
      </main>
      <!-- Modal -->

      <!-- Footer -->
<footer class="pt-7">
    <div class="container">
       <!-- Footer 4 column -->
       <div class="row">
          <div class="col-xl-4  col-12">
            <div class="d-flex flex-column gap-4 mb-5 mb-xl-0">
                <div>
                    <a href="#" class="text-inverse">
                       <img src="./assets/images/logo/logo.svg" alt="logo" />
                    </a>
                 </div>
                 <div class="d-flex flex-column gap-4">
               
                    <p class="mb-0">Empowering creators and businesses with cutting-edge AI tools to transform ideas into reality.</p>
                   <div class="d-flex flex-row align-items-center gap-4">
                     <a href="#!" class="text-reset">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" class="bi bi-facebook" viewBox="0 0 16 16">
                           <path d="M16 8.049c0-4.446-3.582-8.05-8-8.05C3.58 0-.002 3.603-.002 8.05c0 4.017 2.926 7.347 6.75 7.951v-5.625h-2.03V8.05H6.75V6.275c0-2.017 1.195-3.131 3.022-3.131.876 0 1.791.157 1.791.157v1.98h-1.009c-.993 0-1.303.621-1.303 1.258v1.51h2.218l-.354 2.326H9.25V16c3.824-.604 6.75-3.934 6.75-7.951"/>
                         </svg>
                     </a>
                     <a href="#!" class="text-reset">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" class="bi bi-twitter" viewBox="0 0 16 16">
                           <path d="M5.026 15c6.038 0 9.341-5.003 9.341-9.334q.002-.211-.006-.422A6.7 6.7 0 0 0 16 3.542a6.7 6.7 0 0 1-1.889.518 3.3 3.3 0 0 0 1.447-1.817 6.5 6.5 0 0 1-2.087.793A3.286 3.286 0 0 0 7.875 6.03a9.32 9.32 0 0 1-6.767-3.429 3.29 3.29 0 0 0 1.018 4.382A3.3 3.3 0 0 1 .64 6.575v.045a3.29 3.29 0 0 0 2.632 3.218 3.2 3.2 0 0 1-.865.115 3 3 0 0 1-.614-.057 3.28 3.28 0 0 0 3.067 2.277A6.6 6.6 0 0 1 .78 13.58a6 6 0 0 1-.78-.045A9.34 9.34 0 0 0 5.026 15"/>
                         </svg>
                     </a>
                     <a href="#!" class="text-reset">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" class="bi bi-instagram" viewBox="0 0 16 16">
                           <path d="M8 0C5.829 0 5.556.01 4.703.048 3.85.088 3.269.222 2.76.42a3.9 3.9 0 0 0-1.417.923A3.9 3.9 0 0 0 .42 2.76C.222 3.268.087 3.85.048 4.7.01 5.555 0 5.827 0 8.001c0 2.172.01 2.444.048 3.297.04.852.174 1.433.372 1.942.205.526.478.972.923 1.417.444.445.89.719 1.416.923.51.198 1.09.333 1.942.372C5.555 15.99 5.827 16 8 16s2.444-.01 3.298-.048c.851-.04 1.434-.174 1.943-.372a3.9 3.9 0 0 0 1.416-.923c.445-.445.718-.891.923-1.417.197-.509.332-1.09.372-1.942C15.99 10.445 16 10.173 16 8s-.01-2.445-.048-3.299c-.04-.851-.175-1.433-.372-1.941a3.9 3.9 0 0 0-.923-1.417A3.9 3.9 0 0 0 13.24.42c-.51-.198-1.092-.333-1.943-.372C10.443.01 10.172 0 7.998 0zm-.717 1.442h.718c2.136 0 2.389.007 3.232.046.78.035 1.204.166 1.486.275.373.145.64.319.92.599s.453.546.598.92c.11.281.24.705.275 1.485.039.843.047 1.096.047 3.231s-.008 2.389-.047 3.232c-.035.78-.166 1.203-.275 1.485a2.5 2.5 0 0 1-.599.919c-.28.28-.546.453-.92.598-.28.11-.704.24-1.485.276-.843.038-1.096.047-3.232.047s-2.39-.009-3.233-.047c-.78-.036-1.203-.166-1.485-.276a2.5 2.5 0 0 1-.92-.598 2.5 2.5 0 0 1-.6-.92c-.109-.281-.24-.705-.275-1.485-.038-.843-.046-1.096-.046-3.233s.008-2.388.046-3.231c.036-.78.166-1.204.276-1.486.145-.373.319-.64.599-.92s.546-.453.92-.598c.282-.11.705-.24 1.485-.276.738-.034 1.024-.044 2.515-.045zm4.988 1.328a.96.96 0 1 0 0 1.92.96.96 0 0 0 0-1.92m-4.27 1.122a4.109 4.109 0 1 0 0 8.217 4.109 4.109 0 0 0 0-8.217m0 1.441a2.667 2.667 0 1 1 0 5.334 2.667 2.667 0 0 1 0-5.334"/>
                         </svg>
                     </a>
                     <a href="#!" class="text-reset">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" class="bi bi-linkedin" viewBox="0 0 16 16">
                           <path d="M0 1.146C0 .513.526 0 1.175 0h13.65C15.474 0 16 .513 16 1.146v13.708c0 .633-.526 1.146-1.175 1.146H1.175C.526 16 0 15.487 0 14.854zm4.943 12.248V6.169H2.542v7.225zm-1.2-8.212c.837 0 1.358-.554 1.358-1.248-.015-.709-.52-1.248-1.342-1.248S2.4 3.226 2.4 3.934c0 .694.521 1.248 1.327 1.248zm4.908 8.212V9.359c0-.216.016-.432.08-.586.173-.431.568-.878 1.232-.878.869 0 1.216.662 1.216 1.634v3.865h2.401V9.25c0-2.22-1.184-3.252-2.764-3.252-1.274 0-1.845.7-2.165 1.193v.025h-.016l.016-.025V6.169h-2.4c.03.678 0 7.225 0 7.225z"/>
                         </svg>
                     </a>
                     <a href="#!" class="text-reset">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" class="bi bi-youtube" viewBox="0 0 16 16">
                           <path d="M8.051 1.999h.089c.822.003 4.987.033 6.11.335a2.01 2.01 0 0 1 1.415 1.42c.101.38.172.883.22 1.402l.01.104.022.26.008.104c.065.914.073 1.77.074 1.957v.075c-.001.194-.01 1.108-.082 2.06l-.008.105-.009.104c-.05.572-.124 1.14-.235 1.558a2.01 2.01 0 0 1-1.415 1.42c-1.16.312-5.569.334-6.18.335h-.142c-.309 0-1.587-.006-2.927-.052l-.17-.006-.087-.004-.171-.007-.171-.007c-1.11-.049-2.167-.128-2.654-.26a2.01 2.01 0 0 1-1.415-1.419c-.111-.417-.185-.986-.235-1.558L.09 9.82l-.008-.104A31 31 0 0 1 0 7.68v-.123c.002-.215.01-.958.064-1.778l.007-.103.003-.052.008-.104.022-.26.01-.104c.048-.519.119-1.023.22-1.402a2.01 2.01 0 0 1 1.415-1.42c.487-.13 1.544-.21 2.654-.26l.17-.007.172-.006.086-.003.171-.007A100 100 0 0 1 7.858 2zM6.4 5.209v4.818l4.157-2.408z"/>
                         </svg>
                     </a>
                   </div>
                </div>
            </div>
            
            
          </div>
      
          <div class="col-xl-2 col-lg-3 col-12">
            <div class="position-relative">
                <div class="mb-3 d-flex justify-content-between border-bottom border-bottom-lg-0">
                   <h5 class="mb-3 mb-lg-0">Product</h5>
                   <a class="d-block d-lg-none stretched-link text-inherit" data-bs-toggle="collapse" href="#collapseLanding" role="button" aria-expanded="false" aria-controls="collapseLanding">
                      <i class="bi bi-chevron-down"></i>
                   </a>
                </div>
                <div class="collapse d-lg-block" id="collapseLanding">
                   <ul class="list-unstyled mb-0 py-3 py-lg-0">
                      <li class="mb-2">
                         <a href="#!" class="text-decoration-none text-reset">AI Writer</a>
                      </li>
                      <li class="mb-2">
                         <a href="#!" class="text-decoration-none text-reset">Image Generator</a>
                      </li>
                      <li class="mb-2">
                         <a href="#!" class="text-decoration-none text-reset">Chatbot</a>
                      </li>
                      <li class="mb-2">
                         <a href="#!" class="text-decoration-none text-reset">Music Creator</a>
                      </li>
                      <li class="mb-2">
                         <a href="#!" class="text-decoration-none text-reset">Video Generator
                        </a>
                      </li>
                   </ul>
                </div>
             </div>
         </div>
         <div class="col-xl-2 col-lg-3 col-12">
            <div class="position-relative">
                <div class="mb-3 d-flex justify-content-between border-bottom border-bottom-lg-0">
                   <h5 class="mb-3 mb-lg-0">Company
                  </h5>
                   <a class="d-block d-lg-none stretched-link text-inherit" data-bs-toggle="collapse" href="#collapseCompany" role="button" aria-expanded="false" aria-controls="collapseCompany">
                      <i class="bi bi-chevron-down"></i>
                   </a>
                </div>
                <div class="collapse d-lg-block" id="collapseCompany">
                   <ul class="list-unstyled mb-0 py-3 py-lg-0">
                      <li class="mb-2">
                         <a href="#!" class="text-decoration-none text-reset">About Us
                        </a>
                      </li>
                      <li class="mb-2">
                         <a href="#!" class="text-decoration-none text-reset">Careers</a>
                      </li>
                      <li class="mb-2">
                         <a href="#!" class="text-decoration-none text-reset">Blog</a>
                      </li>
                      <li class="mb-2">
                         <a href="#!" class="text-decoration-none text-reset">Press
                        </a>
                      </li>
                      <li class="mb-2">
                         <a href="#!" class="text-decoration-none text-reset">Contact
                        </a>
                      </li>
                   </ul>
                </div>
             </div>
         </div>
         <div class="col-xl-2 col-lg-3 col-12">
            <div class="position-relative">
                <div class="mb-3 d-flex justify-content-between border-bottom border-bottom-lg-0">
                   <h5 class="mb-3 mb-lg-0">Resources
                  </h5>
                   <a class="d-block d-lg-none stretched-link text-inherit" data-bs-toggle="collapse" href="#collapseResources" role="button" aria-expanded="false" aria-controls="collapseResources">
                      <i class="bi bi-chevron-down"></i>
                   </a>
                </div>
                <div class="collapse d-lg-block" id="collapseResources">
                   <ul class="list-unstyled mb-0 py-3 py-lg-0">
                      <li class="mb-2">
                         <a href="#!" class="text-decoration-none text-reset">Documentation
                        </a>
                      </li>
                      <li class="mb-2">
                         <a href="#!" class="text-decoration-none text-reset">Tutorials</a>
                      </li>
                      <li class="mb-2">
                         <a href="#!" class="text-decoration-none text-reset">Case Studies</a>
                      </li>
                      <li class="mb-2">
                         <a href="#!" class="text-decoration-none text-reset">API Reference
                        </a>
                      </li>
                      <li class="mb-2">
                         <a href="#!" class="text-decoration-none text-reset">Community
                        </a>
                      </li>
                   </ul>
                </div>
             </div>
         </div>
         <div class="col-xl-2 col-lg-3 col-12">
            <div class="position-relative">
                <div class="mb-3 d-flex justify-content-between border-bottom border-bottom-lg-0">
                   <h5 class="mb-3 mb-lg-0">Legal
                  </h5>
                   <a class="d-block d-lg-none stretched-link text-inherit" data-bs-toggle="collapse" href="#collapseLegal" role="button" aria-expanded="false" aria-controls="collapseLegal">
                      <i class="bi bi-chevron-down"></i>
                   </a>
                </div>
                <div class="collapse d-lg-block" id="collapseLegal">
                   <ul class="list-unstyled mb-0 py-3 py-lg-0">
                      <li class="mb-2">
                         <a href="#!" class="text-decoration-none text-reset">Terms of Service
                        </a>
                      </li>
                      <li class="mb-2">
                         <a href="#!" class="text-decoration-none text-reset">Privacy Policy</a>
                      </li>
                      <li class="mb-2">
                         <a href="#!" class="text-decoration-none text-reset">Cookie Policy</a>
                      </li>
                      <li class="mb-2">
                         <a href="#!" class="text-decoration-none text-reset">GDPR
                        </a>
                      </li>
                      <li class="mb-2">
                         <a href="#!" class="text-decoration-none text-reset">Accessibility
                        </a>
                      </li>
                   </ul>
                </div>
             </div>
         </div>
       </div>
       <div class=" mt-lg-7 mt-5 border-top py-lg-7 py-5">
         <div class="row align-items-center gy-2 gy-lg-0">
            <div class="col-xl-7 col-lg-4 col-12">
               <div class="small">
                © 2025 AI Studio. All rights reserved.
   
                 
               </div>
            </div>
            <div class=" col-xl-5 col-lg-8 col-12">
               <div class="">
                <form class="needs-validation" novalidate>
                   <div>
                      <h4 class="mb-3 fs-5">Subscribe to our newsletter</h4>
                      
                      <div class="row g-2">
                         <div class="col-md-9 col-8">
                            <label for="subscribeEmail" class="visually-hidden">Email</label>
                            <span class="position-relative d-flex align-items-center"><span class="position-absolute z-1 top-0 mt-2 mx-3"> <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-envelope" viewBox="0 0 16 16">
                               <path d="M0 4a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2zm2-1a1 1 0 0 0-1 1v.217l7 4.2 7-4.2V4a1 1 0 0 0-1-1zm13 2.383-4.708 2.825L15 11.105zm-.034 6.876-5.64-3.471L8 9.583l-1.326-.795-5.64 3.47A1 1 0 0 0 2 13h12a1 1 0 0 0 .966-.741M1 11.105l4.708-2.897L1 5.383z"></path>
                             </svg></span>
                               <input type="email" class="form-control ps-6" id="subscribeEmail" placeholder="Email address" required="">
                            </span>
                            <div class="invalid-feedback">Please enter email.</div>
                         </div>
                         <div class="col-md-3 col-4">
                            <div>
                               <button type="submit" class="btn btn-primary">Subscribe</button>
                            </div>
                         </div>
                      </div>
                   </div>
                </form>
               </div>
            </div>
         </div>
      </div>
    </div>
    
 </footer>
  <div class="btn-scroll-top">
   <svg class="progress-square svg-content" width="100%" height="100%" viewBox="0 0 40 40">
      <path d="M8 1H32C35.866 1 39 4.13401 39 8V32C39 35.866 35.866 39 32 39H8C4.13401 39 1 35.866 1 32V8C1 4.13401 4.13401 1 8 1Z" />
   </svg>
</div>
 <!-- Libs JS -->
<script src="./assets/libs/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
<script src="./assets/libs/simplebar/dist/simplebar.min.js"></script>
<script src="./assets/libs/headhesive/dist/headhesive.min.js"></script>

<!-- Theme JS -->
<script src="./assets/js/theme.min.js"></script>

      <script src="./assets/js/vendors/partical.js"></script>
      <script src="./assets/libs/embla-carousel/embla-carousel.umd.js"></script>
      <script src="./assets/libs/embla-carousel-auto-scroll/embla-carousel-auto-scroll.umd.js"></script>
      <script src="./assets/js/vendors/embla.js"></script>
      <script src="./assets/libs/scrollcue/scrollCue.min.js"></script>
      <script src="./assets/js/vendors/scrollcue.js"></script>
      <script src="./assets/js/vendors/partical.js"></script>
   </body>
</html>
