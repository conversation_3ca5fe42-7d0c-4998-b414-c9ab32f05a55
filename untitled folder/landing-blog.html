<!doctype html>
<html lang="en">
   <head>
      <!-- Required meta tags -->
<meta charset="utf-8" />
<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
 <!-- Favicon icon-->
<link rel="apple-touch-icon" sizes="180x180" href="./assets/images/favicon/apple-touch-icon.png" />
<link rel="icon" type="image/png" sizes="32x32" href="./assets/images/favicon/favicon-32x32.png" />
<link rel="icon" type="image/png" sizes="16x16" href="./assets/images/favicon/favicon-16x16.png" />
<link rel="manifest" href="./assets/images/favicon/site.webmanifest" />
<link rel="mask-icon" href="./assets/images/favicon/block-safari-pinned-tab.svg" color="#8b3dff" />
<link rel="shortcut icon" href="./assets/images/favicon/favicon.ico" />
<meta name="msapplication-TileColor" content="#8b3dff" />
<meta name="msapplication-config" content="./assets/images/favicon/tile.xml" />

<!-- Color modes -->
<script src="./assets/js/vendors/color-modes.js"></script>

<!-- Libs CSS -->
<link href="./assets/libs/simplebar/dist/simplebar.min.css" rel="stylesheet" />
<link href="./assets/libs/bootstrap-icons/font/bootstrap-icons.min.css" rel="stylesheet" />

<!-- Scroll Cue -->
<link rel="stylesheet" href="./assets/libs/scrollcue/scrollCue.css" />

<!-- Box icons -->
<link rel="stylesheet" href="./assets/fonts/css/boxicons.min.css" />

<!-- Theme CSS -->
<link rel="stylesheet" href="./assets/css/theme.min.css">

      <title>Langing Blog - Responsive Website Template | Block</title>
   </head>
   <body>
      <!-- Navbar -->
<header>
   <nav class="navbar navbar-expand-lg navbar-light w-100">
      <div class="container px-3">
         <a class="navbar-brand" href="./index.html"><img src="./assets/images/logo/logo.svg" alt /></a>
         <button class="navbar-toggler offcanvas-nav-btn" type="button">
            <i class="bi bi-list"></i>
         </button>
         <div class="offcanvas offcanvas-start offcanvas-nav" style="width: 20rem">
            <div class="offcanvas-header">
               <a href="./index.html" class="text-inverse"><img src="./assets/images/logo/logo.svg" alt /></a>
               <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close"></button>
            </div>
            <div class="offcanvas-body pt-0 align-items-center">
               <ul class="navbar-nav mx-auto align-items-lg-center">
                  <li class="nav-item">
                     <a class="nav-link" href="./home.html">Home</a>
                  </li>
                  <li class="nav-item">
                     <a class="nav-link" href="./scan-pay.html">Scan & Pay</a>
                  </li>
                  <li class="nav-item">
                     <a class="nav-link" href="./service.html">Services</a>
                  </li>
                  <li class="nav-item">
                     <a class="nav-link" href="./contact.html">Contact</a>
                  </li>
                  <li class="nav-item">
                     <a class="nav-link" href="./become-an-agent.html">Become An Agent</a>
                  </li>
               </ul>
               <div class="mt-3 mt-lg-0 d-flex align-items-center">
                  <a href="./signin.html" class="btn btn-light mx-2">Login</a>
                  <a href="https://play.google.com/store/apps/details?id=com.qsoft.aidapay&hl=en&pli=1" class="btn btn-primary">Create account</a>
               </div>
            </div>
         </div>
      </div>
   </nav>
</header>

      <main>
         <!-- Header start -->
         <section class="mt-8" data-cue="fadeIn">
            <div class="container">
               <a href="#!">
                  <div
                     class="py-lg-10 rounded-3 px-lg-8 py-md-8 px-md-6 p-4 image-blur"
                     style="background-image: url(./assets/images/landings/agency/agency-hero-img.jpg); background-position: center; background-repeat: no-repeat; background-size: cover">
                     <div class="row g-0">
                        <div class="col-xxl-6 col-xl-7 col-lg-8">
                           <div class="d-flex flex-column gap-10" data-cue="zoomIn">
                              <div>
                                 <span class="badge border border-white text-white-stable px-3 py-2 fw-medium rounded-pill fs-6">Lifestyle</span>
                              </div>
                              <div class="d-flex flex-column gap-6">
                                 <div class="d-flex flex-column gap-3">
                                    <h1 class="mb-0 text-white-stable">Introducing Block Bootstrap 5 based design in 2024</h1>
                                    <p class="mb-0 text-white-stable">
                                       Adipisicing sit Lorem excepteur mollit irure mollit reprehenderit deserunt fugiat aute. Et ex sint aute dolore duis non culpa ullamco cupidatat officia.
                                    </p>
                                 </div>
                                 <div class="d-flex align-items-center gap-3">
                                    <div class="d-flex flex-row align-items-center gap-2">
                                       <img src="./assets/images/avatar/avatar-1.jpg" alt="Avatar" class="avatar avatar-xs rounded-circle" />
                                       <div class="mb-0 text-white-stable fs-6">Sandip Chauhan</div>
                                    </div>

                                    <span class="text-white-stable fs-6">Tuesday, February 28, 2025</span>
                                 </div>
                              </div>
                           </div>
                        </div>
                     </div>
                  </div>
               </a>
            </div>
         </section>
         <!-- Header end -->
         <!-- Events start -->
         <section class="my-8" data-cue="fadeIn">
            <div class="container">
               <div class="row g-5">
                  <div class="col-lg-6" data-cue="slideInLeft">
                     <a href="#!">
                        <div class="card h-100 overflow-hidden">
                           <div class="row h-100 g-0">
                              <div
                                 class="col-md-5 image-blur"
                                 style="
                                    background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0) 10%, rgba(0, 0, 0, 0.8) 100%), url(./assets/images/blog/blog-img-2.jpg);
                                    background-position: center;
                                    background-repeat: no-repeat;
                                    background-size: cover;
                                 "></div>
                              <div class="col-md-7">
                                 <div class="card-body d-flex flex-column gap-4">
                                    <div class="d-flex flex-row align-items-center justify-content-between">
                                       <span class="badge bg-primary-subtle text-primary-emphasis rounded-pill text-uppercase">Sport</span>
                                       <div class="bg-warning-subtle border border-warning-subtle text-warning icon-shape icon-sm rounded">
                                          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-patch-check-fill" viewBox="0 0 16 16">
                                             <path
                                                d="M10.067.87a2.89 2.89 0 0 0-4.134 0l-.622.638-.89-.011a2.89 2.89 0 0 0-2.924 2.924l.01.89-.636.622a2.89 2.89 0 0 0 0 4.134l.637.622-.011.89a2.89 2.89 0 0 0 2.924 2.924l.89-.01.622.636a2.89 2.89 0 0 0 4.134 0l.622-.637.89.011a2.89 2.89 0 0 0 2.924-2.924l-.01-.89.636-.622a2.89 2.89 0 0 0 0-4.134l-.637-.622.011-.89a2.89 2.89 0 0 0-2.924-2.924l-.89.01zm.287 5.984-3 3a.5.5 0 0 1-.708 0l-1.5-1.5a.5.5 0 1 1 .708-.708L7 8.793l2.646-2.647a.5.5 0 0 1 .708.708" />
                                          </svg>
                                       </div>
                                    </div>
                                    <div class="d-flex flex-column gap-2">
                                       <h3 class="mb-0 text-truncate h4">A deep introduction to Bootstrap Block Themes</h3>
                                       <p class="mb-0 text-truncate">
                                          Duis ut aliquip fugiat nulla aliquip mollit mollit ullamco est labore. Nostrud cillum eu reprehenderit elit qui aliquip qui commodo ad id elit. Ut culpa nulla
                                          magna elit eiusmod proident qui id enim.
                                       </p>
                                    </div>
                                    <div class="d-flex align-items-center align-items-center gap-3">
                                       <div class="d-flex flex-row align-items-center gap-2">
                                          <img src="./assets/images/avatar/avatar-7.jpg" alt="Avatar" class="avatar avatar-xs rounded-circle" />
                                          <div class="mb-0 fs-6">Anita Parmar</div>
                                       </div>
                                       <span class="fs-6">Feb 28, 2025</span>
                                    </div>
                                 </div>
                              </div>
                           </div>
                        </div>
                     </a>
                  </div>
                  <div class="col-lg-6" data-cue="slideInRight">
                     <a href="#!">
                        <div class="card h-100 overflow-hidden">
                           <div class="row h-100 g-0">
                              <div
                                 class="col-md-5 image-blur"
                                 style="
                                    background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0) 10%, rgba(0, 0, 0, 0.8) 100%), url(./assets/images/blog/blog-img-1.jpg);
                                    background-position: center;
                                    background-repeat: no-repeat;
                                    background-size: cover;
                                 "></div>
                              <div class="col-md-7">
                                 <div class="card-body d-flex flex-column gap-4">
                                    <div class="">
                                       <span class="badge bg-success-subtle text-success-emphasis rounded-pill text-uppercase">Lifestyle</span>
                                    </div>
                                    <div class="d-flex flex-column gap-2">
                                       <h3 class="mb-0 text-truncate h4">The writing trends that will define 2025 (Get Excited)</h3>
                                       <p class="mb-0 text-truncate">
                                          Et eiusmod ex irure voluptate fugiat nisi aute. Mollit in id aute elit amet labore cillum exercitation aliqua. Esse qui qui adipisicing amet incididunt amet
                                          eiusmod.
                                       </p>
                                    </div>
                                    <div class="d-flex align-items-center align-items-center gap-3">
                                       <div class="d-flex flex-row align-items-center gap-2">
                                          <img src="./assets/images/avatar/avatar-2.jpg" alt="Avatar" class="avatar avatar-xs rounded-circle" />
                                          <div class="mb-0 fs-6">Jitu Chauhan</div>
                                       </div>
                                       <span class="fs-6">Mar 15, 2025</span>
                                    </div>
                                 </div>
                              </div>
                           </div>
                        </div>
                     </a>
                  </div>
               </div>
            </div>
         </section>
         <!-- Events end -->
         <!-- Events start -->
         <section class="mb-xl-9 mb-5" data-cue="fadeIn">
            <div class="container">
               <div class="row gy-5 gy-lg-0">
                  <div class="col-xxl-9 col-lg-8 col-12">
                     <div class="row g-5">
                        <div class="col-xxl-6 col-12" data-cue="fadeIn">
                           <a href="#!">
                              <div
                                 class="rounded-3 p-4 image-blur"
                                 style="
                                    background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0) 10%, rgba(0, 0, 0, 0.8) 100%), url(./assets/images/blog/blog-img-3.jpg);
                                    background-position: center;
                                    background-repeat: no-repeat;
                                    background-size: cover;
                                 ">
                                 <div class="d-flex flex-column gap-10" data-cue="zoomOut">
                                    <div>
                                       <span class="badge border rounded-pill border-white text-white-stable px-3 py-2 fw-medium fs-6">Sport</span>
                                    </div>
                                    <div class="d-flex flex-column gap-4">
                                       <div class="d-flex flex-column gap-1">
                                          <h3 class="mb-0 text-white-stable text-truncate">The writing trends that will define 2025 (Get Excited)</h3>
                                          <p class="mb-0 text-white-stable text-truncate">
                                             Mollit minim officia commodo voluptate reprehenderit incididunt ullamco qui consectetur. Minim eiusmod elit quis non non elit id enim aliquip nisi eu.
                                          </p>
                                       </div>
                                       <div class="d-flex align-items-center align-items-center gap-3">
                                          <div class="d-flex flex-row align-items-center gap-2">
                                             <img src="./assets/images/avatar/avatar-1.jpg" alt="Avatar" class="avatar avatar-xs rounded-circle" />
                                             <div class="mb-0 text-white-stable fs-6">Jitu Chauhan</div>
                                          </div>
                                          <span class="text-white-stable fs-6">Feb 28, 2025</span>
                                       </div>
                                    </div>
                                 </div>
                              </div>
                           </a>
                        </div>
                        <div class="col-xxl-6 col-12" data-cue="fadeIn">
                           <a href="#!">
                              <div
                                 class="rounded-3 p-4 image-blur"
                                 style="
                                    background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0) 10%, rgba(0, 0, 0, 0.8) 100%), url(./assets/images/blog/blog-img-2.jpg);
                                    background-position: center;
                                    background-repeat: no-repeat;
                                    background-size: cover;
                                 ">
                                 <div class="d-flex flex-column gap-10" data-cue="zoomOut">
                                    <div>
                                       <span class="badge border rounded-pill border-white text-white-stable px-3 py-2 fw-medium fs-6">Technology</span>
                                    </div>
                                    <div class="d-flex flex-column gap-4">
                                       <div class="d-flex flex-column gap-1">
                                          <h3 class="mb-0 text-white-stable text-truncate">A deep introduction to Bootstrap Block Themes</h3>
                                          <p class="mb-0 text-white-stable text-truncate">
                                             Velit enim irure cillum irure labore dolor amet incididunt. Consequat tempor duis sint cupidatat amet esse sint. Tempor exercitation occaecat nisi eu non
                                             Lorem fugiat anim voluptate velit.
                                          </p>
                                       </div>
                                       <div class="d-flex align-items-center align-items-center gap-3">
                                          <div class="d-flex flex-row align-items-center gap-2">
                                             <img src="./assets/images/avatar/avatar-7.jpg" alt="Avatar" class="avatar avatar-xs rounded-circle" />
                                             <div class="mb-0 text-white-stable fs-6">Anita Parmar</div>
                                          </div>
                                          <span class="text-white-stable fs-6">May 28, 2025</span>
                                       </div>
                                    </div>
                                 </div>
                              </div>
                           </a>
                        </div>
                        <div class="col-xxl-6 col-12" data-cue="fadeIn">
                           <a href="#!">
                              <div
                                 class="rounded-3 p-4 image-blur"
                                 style="
                                    background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0) 10%, rgba(0, 0, 0, 0.8) 100%), url(./assets/images/blog/blog-img-8.jpg);
                                    background-position: center;
                                    background-repeat: no-repeat;
                                    background-size: cover;
                                 ">
                                 <div class="d-flex flex-column gap-10" data-cue="zoomOut">
                                    <div>
                                       <span class="badge border rounded-pill border-white text-white-stable px-3 py-2 fw-medium fs-6">Business</span>
                                    </div>
                                    <div class="d-flex flex-column gap-4">
                                       <div class="d-flex flex-column gap-1">
                                          <h3 class="mb-0 text-white-stable text-truncate">You will destroy yourself financially if you save</h3>
                                          <p class="mb-0 text-white-stable text-truncate">
                                             Elit qui do aliquip incididunt non laboris Lorem ad sint. Pariatur veniam ipsum voluptate labore cillum non aliqua qui labore voluptate qui. Id commodo
                                             laborum est est pariatur consectetur exercitation nostrud.
                                          </p>
                                       </div>
                                       <div class="d-flex align-items-center align-items-center gap-3">
                                          <div class="d-flex flex-row align-items-center gap-2">
                                             <img src="./assets/images/avatar/avatar-6.jpg" alt="Avatar" class="avatar avatar-xs rounded-circle" />
                                             <div class="mb-0 text-white-stable fs-6">Sandip Chauhan</div>
                                          </div>
                                          <span class="text-white-stable fs-6">May 16, 2025</span>
                                       </div>
                                    </div>
                                 </div>
                              </div>
                           </a>
                        </div>
                        <div class="col-xxl-6 col-12" data-cue="fadeIn">
                           <a href="#!">
                              <div
                                 class="rounded-3 p-4 image-blur"
                                 style="
                                    background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0) 10%, rgba(0, 0, 0, 0.8) 100%), url(./assets/images/blog/blog-img-9.jpg);
                                    background-position: center;
                                    background-repeat: no-repeat;
                                    background-size: cover;
                                 ">
                                 <div class="d-flex flex-column gap-10" data-cue="zoomOut">
                                    <div>
                                       <span class="badge border rounded-pill border-white text-white-stable px-3 py-2 fw-medium fs-6">Lifestyle</span>
                                    </div>
                                    <div class="d-flex flex-column gap-4">
                                       <div class="d-flex flex-column gap-1">
                                          <h3 class="mb-0 text-white-stable text-truncate">Block Template for startup business</h3>
                                          <p class="mb-0 text-white-stable text-truncate">
                                             Enim nisi fugiat pariatur ut anim Lorem labore. Consectetur in minim pariatur officia adipisicing eu duis. Deserunt incididunt minim duis ad eu amet
                                             aliquip laboris voluptate.
                                          </p>
                                       </div>
                                       <div class="d-flex align-items-center align-items-center gap-3">
                                          <div class="d-flex flex-row align-items-center gap-2">
                                             <img src="./assets/images/avatar/avatar-4.jpg" alt="Avatar" class="avatar avatar-xs rounded-circle" />
                                             <div class="mb-0 text-white-stable fs-6">Manasvi Suthar</div>
                                          </div>
                                          <span class="text-white-stable fs-6">May 18, 2025</span>
                                       </div>
                                    </div>
                                 </div>
                              </div>
                           </a>
                        </div>
                        <div class="col-xxl-6 col-12" data-cue="fadeIn">
                           <a href="#!">
                              <div
                                 class="rounded-3 p-4 image-blur"
                                 style="
                                    background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0) 10%, rgba(0, 0, 0, 0.8) 100%), url(./assets/images/blog/blog-img-3.jpg);
                                    background-position: center;
                                    background-repeat: no-repeat;
                                    background-size: cover;
                                 ">
                                 <div class="d-flex flex-column gap-10" data-cue="zoomOut">
                                    <div>
                                       <span class="badge border rounded-pill border-white text-white-stable px-3 py-2 fw-medium fs-6">Sport</span>
                                    </div>
                                    <div class="d-flex flex-column gap-4">
                                       <div class="d-flex flex-column gap-1">
                                          <h3 class="mb-0 text-white-stable text-truncate">The writing trends that will define 2025 (Get Excited)</h3>
                                          <p class="mb-0 text-white-stable text-truncate">
                                             Mollit minim officia commodo voluptate reprehenderit incididunt ullamco qui consectetur. Minim eiusmod elit quis non non elit id enim aliquip nisi eu.
                                          </p>
                                       </div>
                                       <div class="d-flex align-items-center align-items-center gap-3">
                                          <div class="d-flex flex-row align-items-center gap-2">
                                             <img src="./assets/images/avatar/avatar-1.jpg" alt="Avatar" class="avatar avatar-xs rounded-circle" />
                                             <div class="mb-0 text-white-stable fs-6">Jitu Chauhan</div>
                                          </div>
                                          <span class="text-white-stable fs-6">Feb 28, 2025</span>
                                       </div>
                                    </div>
                                 </div>
                              </div>
                           </a>
                        </div>
                        <div class="col-xxl-6 col-12" data-cue="fadeIn">
                           <a href="#!">
                              <div
                                 class="rounded-3 p-4 image-blur"
                                 style="
                                    background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0) 10%, rgba(0, 0, 0, 0.8) 100%), url(./assets/images/blog/blog-img-2.jpg);
                                    background-position: center;
                                    background-repeat: no-repeat;
                                    background-size: cover;
                                 ">
                                 <div class="d-flex flex-column gap-10" data-cue="zoomOut">
                                    <div>
                                       <span class="badge border rounded-pill border-white text-white-stable px-3 py-2 fw-medium fs-6">Technology</span>
                                    </div>
                                    <div class="d-flex flex-column gap-4">
                                       <div class="d-flex flex-column gap-1">
                                          <h3 class="mb-0 text-white-stable text-truncate">A deep introduction to Bootstrap Block Themes</h3>
                                          <p class="mb-0 text-white-stable text-truncate">
                                             Velit enim irure cillum irure labore dolor amet incididunt. Consequat tempor duis sint cupidatat amet esse sint. Tempor exercitation occaecat nisi eu non
                                             Lorem fugiat anim voluptate velit.
                                          </p>
                                       </div>
                                       <div class="d-flex align-items-center align-items-center gap-3">
                                          <div class="d-flex flex-row align-items-center gap-2">
                                             <img src="./assets/images/avatar/avatar-7.jpg" alt="Avatar" class="avatar avatar-xs rounded-circle" />
                                             <div class="mb-0 text-white-stable fs-6">Anita Parmar</div>
                                          </div>
                                          <span class="text-white-stable fs-6">May 28, 2025</span>
                                       </div>
                                    </div>
                                 </div>
                              </div>
                           </a>
                        </div>

                        <div class="col-12" data-cue="fadeIn">
                           <div class="bg-primary bg-opacity-25 p-md-7 p-4 rounded-3 overflow-hidden pattern-square text-center d-flex flex-column gap-5">
                              <div>
                                 <span class="badge border border-primary bg-primary bg-opacity-10 rounded-pill fs-5 px-3 py-2 text-primary-emphasis fs-6">Community Version</span>
                              </div>
                              <div class="d-flex flex-column gap-2">
                                 <h2 class="display-6 mb-0">Premium Membership</h2>
                                 <p class="mb-0">You can upgrade your plan anytime and start to engaging with other community users.</p>
                              </div>
                              <div>
                                 <a href="#!" class="btn btn-primary">Upgrade Plan</a>
                              </div>
                           </div>
                        </div>
                     </div>
                  </div>
                  <div class="col-xxl-3 col-lg-4 col-12">
                     <div class="d-flex flex-column gap-5" data-cue="fadeIn">
                        <div class="bg-success bg-opacity-10 rounded-3 p-md-5 p-4" data-cue="zoomOut">
                           <div class="d-flex flex-column gap-4">
                              <div>
                                 <span class="bg-success-subtle text-success-emphasis rounded-circle icon-shape icon-xl">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" class="bi bi-envelope" viewBox="0 0 16 16">
                                       <path
                                          d="M0 4a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2zm2-1a1 1 0 0 0-1 1v.217l7 4.2 7-4.2V4a1 1 0 0 0-1-1zm13 2.383-4.708 2.825L15 11.105zm-.034 6.876-5.64-3.471L8 9.583l-1.326-.795-5.64 3.47A1 1 0 0 0 2 13h12a1 1 0 0 0 .966-.741M1 11.105l4.708-2.897L1 5.383z" />
                                    </svg>
                                 </span>
                              </div>
                              <div class="d-flex flex-column gap-2">
                                 <h3 class="mb-0 text-success-emphasis h4">Subscribe Newsletter</h3>
                                 <p class="mb-0 text-dark">Register now to get latest updates about the topics you interest.</p>
                              </div>
                              <form class="needs-validation" novalidate>
                                 <div class="mb-3">
                                    <label for="subscribeEmail" class="form-label visually-hidden">Email</label>

                                    <input type="email" class="form-control border-0" id="subscribeEmail" placeholder="<EMAIL>" required="" />
                                    <div class="invalid-feedback">Please enter email.</div>
                                 </div>

                                 <div class="d-grid">
                                    <button class="btn btn-success" type="submit">Sign Up</button>
                                 </div>
                              </form>
                           </div>
                        </div>
                        <div class="bg-danger bg-opacity-10 rounded-3 py-md-5 py-4" data-cue="zoomIn">
                           <div class="d-flex flex-column gap-4">
                              <div class="px-4">
                                 <span class="bg-danger-subtle text-danger-emphasis rounded-circle icon-shape icon-xl">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" class="bi bi-vector-pen" viewBox="0 0 16 16">
                                       <path
                                          fill-rule="evenodd"
                                          d="M10.646.646a.5.5 0 0 1 .708 0l4 4a.5.5 0 0 1 0 .708l-1.902 1.902-.829 3.313a1.5 1.5 0 0 1-1.024 1.073L1.254 14.746 4.358 4.4A1.5 1.5 0 0 1 5.43 3.377l3.313-.828zm-1.8 2.908-3.173.793a.5.5 0 0 0-.358.342l-2.57 8.565 8.567-2.57a.5.5 0 0 0 .34-.357l.794-3.174-3.6-3.6z" />
                                       <path fill-rule="evenodd" d="M2.832 13.228 8 9a1 1 0 1 0-1-1l-4.228 5.168-.026.086z" />
                                    </svg>
                                 </span>
                              </div>
                              <div class="d-flex flex-column gap-2 px-md-5 px-4">
                                 <h3 class="mb-0 text-danger-emphasis h4">Become an Author</h3>
                                 <p class="mb-0 text-dark">Register now to get latest updates about the topics you interest.</p>
                              </div>
                              <div>
                                 <div class="marquee" data-cue="slideInLeft">
                                    <div class="track d-flex gap-2">
                                       <span>
                                          <img src="./assets/images/avatar/avatar-1.jpg" alt="avatar" class="avatar avatar-md rounded-circle" />
                                       </span>
                                       <span>
                                          <img src="./assets/images/avatar/avatar-2.jpg" alt="avatar" class="avatar avatar-md rounded-circle" />
                                       </span>
                                       <span>
                                          <img src="./assets/images/avatar/avatar-3.jpg" alt="avatar" class="avatar avatar-md rounded-circle" />
                                       </span>
                                       <span>
                                          <img src="./assets/images/avatar/avatar-4.jpg" alt="avatar" class="avatar avatar-md rounded-circle" />
                                       </span>
                                       <span>
                                          <img src="./assets/images/avatar/avatar-5.jpg" alt="avatar" class="avatar avatar-md rounded-circle" />
                                       </span>
                                       <span>
                                          <img src="./assets/images/avatar/avatar-6.jpg" alt="avatar" class="avatar avatar-md rounded-circle" />
                                       </span>
                                       <span>
                                          <img src="./assets/images/avatar/avatar-7.jpg" alt="avatar" class="avatar avatar-md rounded-circle" />
                                       </span>
                                       <span>
                                          <img src="./assets/images/avatar/avatar-9.jpg" alt="avatar" class="avatar avatar-md rounded-circle" />
                                       </span>
                                       <span>
                                          <img src="./assets/images/avatar/avatar-3.jpg" alt="avatar" class="avatar avatar-md rounded-circle" />
                                       </span>
                                       <span>
                                          <img src="./assets/images/avatar/avatar-4.jpg" alt="avatar" class="avatar avatar-md rounded-circle" />
                                       </span>
                                    </div>
                                 </div>
                                 <div class="marquee" data-cue="slideInRight">
                                    <div class="track-2 d-flex gap-2">
                                       <span>
                                          <img src="./assets/images/avatar/avatar-8.jpg" alt="avatar" class="avatar avatar-md rounded-circle" />
                                       </span>
                                       <span>
                                          <img src="./assets/images/avatar/avatar-9.jpg" alt="avatar" class="avatar avatar-md rounded-circle" />
                                       </span>
                                       <span>
                                          <img src="./assets/images/avatar/avatar-1.jpg" alt="avatar" class="avatar avatar-md rounded-circle" />
                                       </span>
                                       <span>
                                          <img src="./assets/images/avatar/avatar-5.jpg" alt="avatar" class="avatar avatar-md rounded-circle" />
                                       </span>
                                       <span>
                                          <img src="./assets/images/avatar/avatar-3.jpg" alt="avatar" class="avatar avatar-md rounded-circle" />
                                       </span>
                                       <span>
                                          <img src="./assets/images/avatar/avatar-4.jpg" alt="avatar" class="avatar avatar-md rounded-circle" />
                                       </span>
                                       <span>
                                          <img src="./assets/images/avatar/avatar-2.jpg" alt="avatar" class="avatar avatar-md rounded-circle" />
                                       </span>
                                       <span>
                                          <img src="./assets/images/avatar/avatar-8.jpg" alt="avatar" class="avatar avatar-md rounded-circle" />
                                       </span>
                                       <span>
                                          <img src="./assets/images/avatar/avatar-4.jpg" alt="avatar" class="avatar avatar-md rounded-circle" />
                                       </span>
                                       <span>
                                          <img src="./assets/images/avatar/avatar-1.jpg" alt="avatar" class="avatar avatar-md rounded-circle" />
                                       </span>
                                    </div>
                                 </div>
                              </div>
                              <form class="needs-validation px-md-5 px-4" novalidate>
                                 <div class="mb-3">
                                    <label for="authorEmail" class="form-label visually-hidden">Email</label>

                                    <input type="email" class="form-control border-0" id="authorEmail" placeholder="<EMAIL>" required="" />
                                    <div class="invalid-feedback">Please enter email.</div>
                                 </div>

                                 <div class="d-grid">
                                    <button class="btn btn-danger" type="submit">Register as an author</button>
                                 </div>
                              </form>
                           </div>
                        </div>
                     </div>
                  </div>
               </div>
            </div>
         </section>
         <!-- Events end -->
         <!-- Event start -->
         <section class="my-xl-9 my-5" data-cue="fadeIn">
            <div class="container">
               <div class="">
                  <div class="row">
                     <div class="col-12" data-cue="fadeIn">
                        <ul class="nav nav-pills mb-6 nav-primary" id="pills-tab" role="tablist">
                           <li class="nav-item" role="presentation">
                              <a
                                 href="#"
                                 class="nav-link active me-2"
                                 id="pillsBusiness-tab"
                                 data-bs-toggle="pill"
                                 data-bs-target="#pillsBusiness"
                                 role="tab"
                                 aria-controls="pillsBusiness"
                                 aria-selected="true">
                                 Business
                              </a>
                           </li>
                           <li class="nav-item" role="presentation">
                              <a
                                 href="#"
                                 class="nav-link me-2"
                                 id="pillsLifestyle-tab"
                                 data-bs-toggle="pill"
                                 data-bs-target="#pillsLifestyle"
                                 role="tab"
                                 aria-controls="pillsLifestyle"
                                 aria-selected="false">
                                 Lifestyle
                              </a>
                           </li>
                           <li class="nav-item" role="presentation">
                              <a href="#" class="nav-link me-2" id="pillsSport-tab" data-bs-toggle="pill" data-bs-target="#pillsSport" role="tab" aria-controls="pillsSport" aria-selected="false">
                                 Sport
                              </a>
                           </li>
                           <li class="nav-item" role="presentation">
                              <a href="#" class="nav-link me-2" id="pillsTravel-tab" data-bs-toggle="pill" data-bs-target="#pillsTravel" role="tab" aria-controls="pillsTravel" aria-selected="false">
                                 Travel
                              </a>
                           </li>
                           <li class="nav-item" role="presentation">
                              <a
                                 href="#"
                                 class="nav-link me-2"
                                 id="pillsTechnology-tab"
                                 data-bs-toggle="pill"
                                 data-bs-target="#pillsTechnology"
                                 role="tab"
                                 aria-controls="pillsTechnology"
                                 aria-selected="false">
                                 Technology
                              </a>
                           </li>
                        </ul>
                        <div class="tab-content mb-6" id="pills-tabContent">
                           <div class="tab-pane show active" id="pillsBusiness" role="tabpanel" aria-labelledby="pillsBusiness-tab" tabindex="0">
                              <div class="table-responsive-xl">
                                 <div class="row flex-nowrap pb-4">
                                    <div class="col-lg-4 col-md-6 col-12" data-cue="zoomIn">
                                       <div class="card h-100">
                                          <div class="card-body d-flex flex-column gap-5">
                                             <img src="./assets/images/blog/blog-img-1.jpg" alt="blog" class="rounded img-fluid w-100" />
                                             <div class="d-flex flex-column gap-4">
                                                <div class="d-flex flex-column gap-2">
                                                   <h3 class="mb-0 h4">
                                                      <a href="#!" class="text-reset">The writing trends that will define 2025 (Get Excited)</a>
                                                   </h3>
                                                   <p class="mb-0">Elit qui do aliquip incididunt non laboris Lorem ad sint. Pariatur veniam ipsum voluptate labore qui.</p>
                                                </div>
                                                <div class="d-flex align-items-center align-items-center gap-3">
                                                   <div class="d-flex flex-row align-items-center gap-2">
                                                      <img src="./assets/images/avatar/avatar-3.jpg" alt="Avatar" class="avatar avatar-xs rounded-circle" />
                                                      <div class="mb-0 fs-6">Jitu Chauhan</div>
                                                   </div>
                                                   <span class="fs-6">Mar 16, 2025</span>
                                                </div>
                                             </div>
                                          </div>
                                       </div>
                                    </div>
                                    <div class="col-lg-4 col-md-6 col-12" data-cue="zoomIn">
                                       <div class="card h-100">
                                          <div class="card-body d-flex flex-column gap-5">
                                             <img src="./assets/images/blog/blog-img-2.jpg" alt="blog" class="rounded img-fluid w-100" />
                                             <div class="d-flex flex-column gap-4">
                                                <div class="d-flex flex-column gap-2">
                                                   <h3 class="mb-0 h4">
                                                      <a href="#!" class="text-reset">The ultimate Block Bootstrap website template</a>
                                                   </h3>
                                                   <p class="mb-0">Nulla velit incididunt consequat mollit cillum nostrud Lorem do consectetur velit. Lorem ipsum dolor sit amet.</p>
                                                </div>
                                                <div class="d-flex align-items-center align-items-center gap-3">
                                                   <div class="d-flex flex-row align-items-center gap-2">
                                                      <img src="./assets/images/avatar/avatar-8.jpg" alt="Avatar" class="avatar avatar-xs rounded-circle" />
                                                      <div class="mb-0 fs-6">Sandip Chauhan</div>
                                                   </div>
                                                   <span class="fs-6">Mar 15, 2025</span>
                                                </div>
                                             </div>
                                          </div>
                                       </div>
                                    </div>
                                    <div class="col-lg-4 col-md-6 col-12" data-cue="zoomIn">
                                       <div class="card h-100">
                                          <div class="card-body d-flex flex-column gap-5">
                                             <div class="position-relative">
                                                <img src="./assets/images/blog/blog-img-3.jpg" alt="blog" class="rounded img-fluid w-100" />
                                                <div class="position-absolute top-0 start-0 p-3">
                                                   <div class="bg-warning-subtle border border-warning-subtle text-warning icon-shape icon-sm rounded">
                                                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-patch-check-fill" viewBox="0 0 16 16">
                                                         <path
                                                            d="M10.067.87a2.89 2.89 0 0 0-4.134 0l-.622.638-.89-.011a2.89 2.89 0 0 0-2.924 2.924l.01.89-.636.622a2.89 2.89 0 0 0 0 4.134l.637.622-.011.89a2.89 2.89 0 0 0 2.924 2.924l.89-.01.622.636a2.89 2.89 0 0 0 4.134 0l.622-.637.89.011a2.89 2.89 0 0 0 2.924-2.924l-.01-.89.636-.622a2.89 2.89 0 0 0 0-4.134l-.637-.622.011-.89a2.89 2.89 0 0 0-2.924-2.924l-.89.01zm.287 5.984-3 3a.5.5 0 0 1-.708 0l-1.5-1.5a.5.5 0 1 1 .708-.708L7 8.793l2.646-2.647a.5.5 0 0 1 .708.708"></path>
                                                      </svg>
                                                   </div>
                                                </div>
                                             </div>
                                             <div class="d-flex flex-column gap-4">
                                                <div class="d-flex flex-column gap-2">
                                                   <h3 class="mb-0 h4">
                                                      <a href="#!" class="text-reset">Block Template for startup business</a>
                                                   </h3>
                                                   <p class="mb-0">Labore eu nisi magna anim officia eiusmod quis. Voluptate ut commodo laboris dolor esse enim.</p>
                                                </div>
                                                <div class="d-flex align-items-center align-items-center gap-3">
                                                   <div class="d-flex flex-row align-items-center gap-2">
                                                      <img src="./assets/images/avatar/avatar-7.jpg" alt="Avatar" class="avatar avatar-xs rounded-circle" />
                                                      <div class="mb-0 fs-6">Anita Parmar</div>
                                                   </div>
                                                   <span class="fs-6">Apr 7, 2025</span>
                                                </div>
                                             </div>
                                          </div>
                                       </div>
                                    </div>
                                 </div>
                              </div>
                           </div>
                           <div class="tab-pane" id="pillsLifestyle" role="tabpanel" aria-labelledby="pillsLifestyle-tab" tabindex="0">
                              <div class="table-responsive-xl">
                                 <div class="row flex-nowrap pb-4">
                                    <div class="col-lg-4 col-md-6 col-12" data-cue="zoomIn">
                                       <div class="card h-100">
                                          <div class="card-body d-flex flex-column gap-5">
                                             <img src="./assets/images/blog/blog-img-4.jpg" alt="blog" class="rounded img-fluid w-100" />
                                             <div class="d-flex flex-column gap-4">
                                                <div class="d-flex flex-column gap-2">
                                                   <h3 class="mb-0 h4">
                                                      <a href="#!" class="text-reset">A deep introduction to Bootstrap Block Themes</a>
                                                   </h3>
                                                   <p class="mb-0">Adipisicing sit Lorem excepteur mollit irure mollit reprehenderit deserunt fugiat aute Lorem ipsum dolor sit amet.</p>
                                                </div>
                                                <div class="d-flex align-items-center align-items-center gap-3">
                                                   <div class="d-flex flex-row align-items-center gap-2">
                                                      <img src="./assets/images/avatar/avatar-2.jpg" alt="Avatar" class="avatar avatar-xs rounded-circle" />
                                                      <div class="mb-0 fs-6">Jitu Chauhan</div>
                                                   </div>
                                                   <span class="fs-6">Feb 28, 2025</span>
                                                </div>
                                             </div>
                                          </div>
                                       </div>
                                    </div>
                                    <div class="col-lg-4 col-md-6 col-12" data-cue="zoomIn">
                                       <div class="card h-100">
                                          <div class="card-body d-flex flex-column gap-5">
                                             <div class="position-relative">
                                                <img src="./assets/images/blog/blog-img-6.jpg" alt="blog" class="rounded img-fluid w-100" />
                                                <div class="position-absolute top-0 start-0 p-3">
                                                   <div class="bg-warning-subtle border border-warning-subtle text-warning icon-shape icon-sm rounded">
                                                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-patch-check-fill" viewBox="0 0 16 16">
                                                         <path
                                                            d="M10.067.87a2.89 2.89 0 0 0-4.134 0l-.622.638-.89-.011a2.89 2.89 0 0 0-2.924 2.924l.01.89-.636.622a2.89 2.89 0 0 0 0 4.134l.637.622-.011.89a2.89 2.89 0 0 0 2.924 2.924l.89-.01.622.636a2.89 2.89 0 0 0 4.134 0l.622-.637.89.011a2.89 2.89 0 0 0 2.924-2.924l-.01-.89.636-.622a2.89 2.89 0 0 0 0-4.134l-.637-.622.011-.89a2.89 2.89 0 0 0-2.924-2.924l-.89.01zm.287 5.984-3 3a.5.5 0 0 1-.708 0l-1.5-1.5a.5.5 0 1 1 .708-.708L7 8.793l2.646-2.647a.5.5 0 0 1 .708.708"></path>
                                                      </svg>
                                                   </div>
                                                </div>
                                             </div>
                                             <div class="d-flex flex-column gap-4">
                                                <div class="d-flex flex-column gap-2">
                                                   <h3 class="mb-0 h4">
                                                      <a href="#!" class="text-reset">Block Template for startup business</a>
                                                   </h3>
                                                   <p class="mb-0">Enim nisi fugiat pariatur ut anim Lorem labore. Consectetur in minim pariatur officia adipisicing eu duis.</p>
                                                </div>
                                                <div class="d-flex align-items-center align-items-center gap-3">
                                                   <div class="d-flex flex-row align-items-center gap-2">
                                                      <img src="./assets/images/avatar/avatar-7.jpg" alt="Avatar" class="avatar avatar-xs rounded-circle" />
                                                      <div class="mb-0 fs-6">Anita Parmar</div>
                                                   </div>
                                                   <span class="fs-6">Mar 15, 2025</span>
                                                </div>
                                             </div>
                                          </div>
                                       </div>
                                    </div>
                                    <div class="col-lg-4 col-md-6 col-12" data-cue="zoomIn">
                                       <div class="card h-100">
                                          <div class="card-body d-flex flex-column gap-5">
                                             <img src="./assets/images/blog/blog-img-8.jpg" alt="blog" class="rounded img-fluid w-100" />
                                             <div class="d-flex flex-column gap-4">
                                                <div class="d-flex flex-column gap-2">
                                                   <h3 class="mb-0 h4">
                                                      <a href="#!" class="text-reset">The ultimate Block Bootstrap website template</a>
                                                   </h3>
                                                   <p class="mb-0">Nulla velit incididunt consequat mollit cillum nostrud Lorem do consectetur velit. Lorem ipsum dolor sit amet.</p>
                                                </div>
                                                <div class="d-flex align-items-center align-items-center gap-3">
                                                   <div class="d-flex flex-row align-items-center gap-2">
                                                      <img src="./assets/images/avatar/avatar-8.jpg" alt="Avatar" class="avatar avatar-xs rounded-circle" />
                                                      <div class="mb-0 fs-6">Sandip Chauhan</div>
                                                   </div>
                                                   <span class="fs-6">Mar 15, 2025</span>
                                                </div>
                                             </div>
                                          </div>
                                       </div>
                                    </div>
                                 </div>
                              </div>
                           </div>
                           <div class="tab-pane" id="pillsSport" role="tabpanel" aria-labelledby="pillsSport-tab" tabindex="0">
                              <div class="table-responsive-xl">
                                 <div class="row flex-nowrap pb-4">
                                    <div class="col-lg-4 col-md-6 col-12" data-cue="zoomIn">
                                       <div class="card h-100">
                                          <div class="card-body d-flex flex-column gap-5">
                                             <img src="./assets/images/blog/blog-img-2.jpg" alt="blog" class="rounded img-fluid w-100" />
                                             <div class="d-flex flex-column gap-4">
                                                <div class="d-flex flex-column gap-2">
                                                   <h3 class="mb-0 h4">
                                                      <a href="#!" class="text-reset">Block Template for startup business</a>
                                                   </h3>
                                                   <p class="mb-0">Enim nisi fugiat pariatur ut anim Lorem labore. Consectetur in minim pariatur officia adipisicing eu duis.</p>
                                                </div>
                                                <div class="d-flex align-items-center align-items-center gap-3">
                                                   <div class="d-flex flex-row align-items-center gap-2">
                                                      <img src="./assets/images/avatar/avatar-7.jpg" alt="Avatar" class="avatar avatar-xs rounded-circle" />
                                                      <div class="mb-0 fs-6">Anita Parmar</div>
                                                   </div>
                                                   <span class="fs-6">Mar 15, 2025</span>
                                                </div>
                                             </div>
                                          </div>
                                       </div>
                                    </div>
                                    <div class="col-lg-4 col-md-6 col-12" data-cue="zoomIn">
                                       <div class="card h-100">
                                          <div class="card-body d-flex flex-column gap-5">
                                             <div class="position-relative">
                                                <img src="./assets/images/blog/blog-img-4.jpg" alt="blog" class="rounded img-fluid w-100" />
                                                <div class="position-absolute top-0 start-0 p-3">
                                                   <div class="bg-warning-subtle border border-warning-subtle text-warning icon-shape icon-sm rounded">
                                                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-patch-check-fill" viewBox="0 0 16 16">
                                                         <path
                                                            d="M10.067.87a2.89 2.89 0 0 0-4.134 0l-.622.638-.89-.011a2.89 2.89 0 0 0-2.924 2.924l.01.89-.636.622a2.89 2.89 0 0 0 0 4.134l.637.622-.011.89a2.89 2.89 0 0 0 2.924 2.924l.89-.01.622.636a2.89 2.89 0 0 0 4.134 0l.622-.637.89.011a2.89 2.89 0 0 0 2.924-2.924l-.01-.89.636-.622a2.89 2.89 0 0 0 0-4.134l-.637-.622.011-.89a2.89 2.89 0 0 0-2.924-2.924l-.89.01zm.287 5.984-3 3a.5.5 0 0 1-.708 0l-1.5-1.5a.5.5 0 1 1 .708-.708L7 8.793l2.646-2.647a.5.5 0 0 1 .708.708"></path>
                                                      </svg>
                                                   </div>
                                                </div>
                                             </div>
                                             <div class="d-flex flex-column gap-4">
                                                <div class="d-flex flex-column gap-2">
                                                   <h3 class="mb-0 h4">
                                                      <a href="#!" class="text-reset">The ultimate Block Bootstrap website template</a>
                                                   </h3>
                                                   <p class="mb-0">Nulla velit incididunt consequat mollit cillum nostrud Lorem do consectetur velit. Lorem ipsum dolor sit amet.</p>
                                                </div>
                                                <div class="d-flex align-items-center align-items-center gap-3">
                                                   <div class="d-flex flex-row align-items-center gap-2">
                                                      <img src="./assets/images/avatar/avatar-8.jpg" alt="Avatar" class="avatar avatar-xs rounded-circle" />
                                                      <div class="mb-0 fs-6">Sandip Chauhan</div>
                                                   </div>
                                                   <span class="fs-6">Mar 15, 2025</span>
                                                </div>
                                             </div>
                                          </div>
                                       </div>
                                    </div>
                                    <div class="col-lg-4 col-md-6 col-12" data-cue="zoomIn">
                                       <div class="card h-100">
                                          <div class="card-body d-flex flex-column gap-5">
                                             <img src="./assets/images/blog/blog-img-6.jpg" alt="blog" class="rounded img-fluid w-100" />

                                             <div class="d-flex flex-column gap-4">
                                                <div class="d-flex flex-column gap-2">
                                                   <h3 class="mb-0 h4">
                                                      <a href="#!" class="text-reset">A deep introduction to Bootstrap Block Themes</a>
                                                   </h3>
                                                   <p class="mb-0">Adipisicing sit Lorem excepteur mollit irure mollit reprehenderit deserunt fugiat aute Lorem ipsum dolor sit amet.</p>
                                                </div>
                                                <div class="d-flex align-items-center align-items-center gap-3">
                                                   <div class="d-flex flex-row align-items-center gap-2">
                                                      <img src="./assets/images/avatar/avatar-2.jpg" alt="Avatar" class="avatar avatar-xs rounded-circle" />
                                                      <div class="mb-0 fs-6">Jitu Chauhan</div>
                                                   </div>
                                                   <span class="fs-6">Feb 28, 2025</span>
                                                </div>
                                             </div>
                                          </div>
                                       </div>
                                    </div>
                                 </div>
                              </div>
                           </div>
                           <div class="tab-pane" id="pillsTravel" role="tabpanel" aria-labelledby="pillsTravel-tab" tabindex="0">
                              <div class="table-responsive-xl">
                                 <div class="row flex-nowrap pb-4">
                                    <div class="col-md-6 col-12">
                                       <div class="card h-100">
                                          <div class="card-body d-flex flex-column gap-5">
                                             <div class="position-relative">
                                                <img src="./assets/images/blog/blog-img-1.jpg" alt="blog" class="rounded img-fluid w-100" />
                                                <div class="position-absolute top-0 start-0 p-3">
                                                   <div class="bg-warning-subtle border border-warning-subtle text-warning icon-shape icon-sm rounded">
                                                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-patch-check-fill" viewBox="0 0 16 16">
                                                         <path
                                                            d="M10.067.87a2.89 2.89 0 0 0-4.134 0l-.622.638-.89-.011a2.89 2.89 0 0 0-2.924 2.924l.01.89-.636.622a2.89 2.89 0 0 0 0 4.134l.637.622-.011.89a2.89 2.89 0 0 0 2.924 2.924l.89-.01.622.636a2.89 2.89 0 0 0 4.134 0l.622-.637.89.011a2.89 2.89 0 0 0 2.924-2.924l-.01-.89.636-.622a2.89 2.89 0 0 0 0-4.134l-.637-.622.011-.89a2.89 2.89 0 0 0-2.924-2.924l-.89.01zm.287 5.984-3 3a.5.5 0 0 1-.708 0l-1.5-1.5a.5.5 0 1 1 .708-.708L7 8.793l2.646-2.647a.5.5 0 0 1 .708.708"></path>
                                                      </svg>
                                                   </div>
                                                </div>
                                             </div>
                                             <div class="d-flex flex-column gap-4">
                                                <div class="d-flex flex-column gap-2">
                                                   <h3 class="mb-0 h4">
                                                      <a href="#!" class="text-reset">The ultimate Block Bootstrap website template</a>
                                                   </h3>
                                                   <p class="mb-0">
                                                      Nulla velit incididunt consequat mollit cillum nostrud Lorem do consectetur velit. Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet
                                                      consectetur adipisicing elit.
                                                   </p>
                                                </div>
                                                <div class="d-flex align-items-center align-items-center gap-3">
                                                   <div class="d-flex flex-row align-items-center gap-2">
                                                      <img src="./assets/images/avatar/avatar-8.jpg" alt="Avatar" class="avatar avatar-xs rounded-circle" />
                                                      <div class="mb-0 fs-6">Sandip Chauhan</div>
                                                   </div>
                                                   <span class="fs-6">Mar 15, 2025</span>
                                                </div>
                                             </div>
                                          </div>
                                       </div>
                                    </div>
                                    <div class="col-md-6 col-12">
                                       <div class="card h-100">
                                          <div class="card-body d-flex flex-column gap-5">
                                             <img src="./assets/images/blog/blog-img-5.jpg" alt="blog" class="rounded img-fluid w-100" />

                                             <div class="d-flex flex-column gap-4">
                                                <div class="d-flex flex-column gap-2">
                                                   <h3 class="mb-0 h4">
                                                      <a href="#!" class="text-reset">Block Template for startup business</a>
                                                   </h3>
                                                   <p class="mb-0">
                                                      Labore eu nisi magna anim officia eiusmod quis. Voluptate ut commodo laboris dolor esse enim ipsum dolor sit amet consectetur adipisicing elit.
                                                      Minima architecto iure optio quo eos ducimus.
                                                   </p>
                                                </div>
                                                <div class="d-flex align-items-center align-items-center gap-3">
                                                   <div class="d-flex flex-row align-items-center gap-2">
                                                      <img src="./assets/images/avatar/avatar-7.jpg" alt="Avatar" class="avatar avatar-xs rounded-circle" />
                                                      <div class="mb-0 fs-6">Anita Parmar</div>
                                                   </div>
                                                   <span class="fs-6">Apr 7, 2025</span>
                                                </div>
                                             </div>
                                          </div>
                                       </div>
                                    </div>
                                 </div>
                              </div>
                           </div>
                           <div class="tab-pane" id="pillsTechnology" role="tabpanel" aria-labelledby="pillsTechnology-tab" tabindex="0">
                              <div class="table-responsive-xl">
                                 <div class="row flex-nowrap pb-4">
                                    <div class="col-lg-4 col-md-6 col-12" data-cue="zoomIn">
                                       <div class="card h-100">
                                          <div class="card-body d-flex flex-column gap-5">
                                             <img src="./assets/images/blog/blog-img-8.jpg" alt="blog" class="rounded img-fluid w-100" />
                                             <div class="d-flex flex-column gap-4">
                                                <div class="d-flex flex-column gap-2">
                                                   <h3 class="mb-0 h4">
                                                      <a href="#!" class="text-reset">The writing trends that will define 2025 (Get Excited)</a>
                                                   </h3>
                                                   <p class="mb-0">Elit qui do aliquip incididunt non laboris Lorem ad sint. Pariatur veniam ipsum voluptate labore qui.</p>
                                                </div>
                                                <div class="d-flex align-items-center align-items-center gap-3">
                                                   <div class="d-flex flex-row align-items-center gap-2">
                                                      <img src="./assets/images/avatar/avatar-3.jpg" alt="Avatar" class="avatar avatar-xs rounded-circle" />
                                                      <div class="mb-0 fs-6">Jitu Chauhan</div>
                                                   </div>
                                                   <span class="fs-6">Mar 16, 2025</span>
                                                </div>
                                             </div>
                                          </div>
                                       </div>
                                    </div>
                                    <div class="col-lg-4 col-md-6 col-12" data-cue="zoomIn">
                                       <div class="card h-100">
                                          <div class="card-body d-flex flex-column gap-5">
                                             <div class="position-relative">
                                                <img src="./assets/images/blog/blog-img-3.jpg" alt="blog" class="rounded img-fluid w-100" />
                                                <div class="position-absolute top-0 start-0 p-3">
                                                   <div class="bg-warning-subtle border border-warning-subtle text-warning icon-shape icon-sm rounded">
                                                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-patch-check-fill" viewBox="0 0 16 16">
                                                         <path
                                                            d="M10.067.87a2.89 2.89 0 0 0-4.134 0l-.622.638-.89-.011a2.89 2.89 0 0 0-2.924 2.924l.01.89-.636.622a2.89 2.89 0 0 0 0 4.134l.637.622-.011.89a2.89 2.89 0 0 0 2.924 2.924l.89-.01.622.636a2.89 2.89 0 0 0 4.134 0l.622-.637.89.011a2.89 2.89 0 0 0 2.924-2.924l-.01-.89.636-.622a2.89 2.89 0 0 0 0-4.134l-.637-.622.011-.89a2.89 2.89 0 0 0-2.924-2.924l-.89.01zm.287 5.984-3 3a.5.5 0 0 1-.708 0l-1.5-1.5a.5.5 0 1 1 .708-.708L7 8.793l2.646-2.647a.5.5 0 0 1 .708.708"></path>
                                                      </svg>
                                                   </div>
                                                </div>
                                             </div>
                                             <div class="d-flex flex-column gap-4">
                                                <div class="d-flex flex-column gap-2">
                                                   <h3 class="mb-0 h4">
                                                      <a href="#!" class="text-reset">Block Template for startup business</a>
                                                   </h3>
                                                   <p class="mb-0">Labore eu nisi magna anim officia eiusmod quis. Voluptate ut commodo laboris dolor esse enim.</p>
                                                </div>
                                                <div class="d-flex align-items-center align-items-center gap-3">
                                                   <div class="d-flex flex-row align-items-center gap-2">
                                                      <img src="./assets/images/avatar/avatar-7.jpg" alt="Avatar" class="avatar avatar-xs rounded-circle" />
                                                      <div class="mb-0 fs-6">Anita Parmar</div>
                                                   </div>
                                                   <span class="fs-6">Apr 7, 2025</span>
                                                </div>
                                             </div>
                                          </div>
                                       </div>
                                    </div>
                                    <div class="col-lg-4 col-md-6 col-12" data-cue="zoomIn">
                                       <div class="card h-100">
                                          <div class="card-body d-flex flex-column gap-5">
                                             <img src="./assets/images/blog/blog-img-6.jpg" alt="blog" class="rounded img-fluid w-100" />

                                             <div class="d-flex flex-column gap-4">
                                                <div class="d-flex flex-column gap-2">
                                                   <h3 class="mb-0 h4">
                                                      <a href="#!" class="text-reset">Block Template for startup business</a>
                                                   </h3>
                                                   <p class="mb-0">Labore eu nisi magna anim officia eiusmod quis. Voluptate ut commodo laboris dolor esse enim.</p>
                                                </div>
                                                <div class="d-flex align-items-center align-items-center gap-3">
                                                   <div class="d-flex flex-row align-items-center gap-2">
                                                      <img src="./assets/images/avatar/avatar-2.jpg" alt="Avatar" class="avatar avatar-xs rounded-circle" />
                                                      <div class="mb-0 fs-6">Sandip Chauhan</div>
                                                   </div>
                                                   <span class="fs-6">Apr 7, 2025</span>
                                                </div>
                                             </div>
                                          </div>
                                       </div>
                                    </div>
                                 </div>
                              </div>
                           </div>
                        </div>
                        <div class="d-flex align-items-center justify-content-center">
                           <a href="#!" class="btn btn-light">See All Post</a>
                        </div>
                     </div>
                  </div>
               </div>
            </div>
         </section>
         <!-- Event end -->
         <!-- Download app -->
         <section class="mb-8" data-bs-theme="dark" data-cue="fadeIn">
            <div class="container">
               <div class="row g-0">
                  <div class="bg-primary-dark py-xl-8 py-5 rounded-3 line-pattern">
                     <div class="col-lg-6 offset-lg-3 col-12 position-relative z-1">
                        <div class="text-center d-flex flex-column gap-5 px-4" data-cue="zoomOut">
                           <div>
                              <span class="badge border border-white rounded-pill px-3 py-2">Download Our Mobile App</span>
                           </div>
                           <div class="d-flex flex-column gap-4">
                              <div class="d-flex flex-column gap-2">
                                 <h2 class="h1 mb-0">Keep continue to reading</h2>
                                 <p class="mb-0 text-primary-emphasis">You can download our mobile app and continue to reading from all platforms.</p>
                              </div>
                              <div class="d-flex gap-2 justify-content-center">
                                 <a href="#!"><img src="./assets/images/mobile-app/playstore.svg" alt="playstore" /></a>
                                 <a href="#!"><img src="./assets/images/mobile-app/appstore.svg" alt="appstore" /></a>
                              </div>
                           </div>
                        </div>
                     </div>
                  </div>
               </div>
            </div>
         </section>
         <!-- Download app -->
      </main>

      <footer class="pt-7">
   <div class="container">
      <!-- Footer 4 column -->
      <div class="row">
         <div class="col-lg-9 col-12">
            <div class="row" id="ft-links">
               <div class="col-lg-3 col-12">
                  <div class="position-relative">
                     <div class="mb-3 pb-2 d-flex justify-content-between border-bottom border-bottom-lg-0">
                        <h4>Service</h4>
                        <a class="d-block d-lg-none stretched-link text-body" data-bs-toggle="collapse" href="#collapseLanding" role="button" aria-expanded="true" aria-controls="collapseLanding">
                           <i class="bi bi-chevron-down"></i>
                        </a>
                     </div>
                     <div class="d-lg-block collapse show" id="collapseLanding" data-bs-parent="#ft-links" style="">
                        <ul class="list-unstyled mb-0 py-3 py-lg-0">
                           <li class="mb-2">
                              <a href="./index.html" class="text-decoration-none text-reset">Web App Development</a>
                           </li>
                           <li class="mb-2">
                              <a href="#!" class="text-decoration-none text-reset">Front End Development</a>
                           </li>
                           <li class="mb-2">
                              <a href="#!" class="text-decoration-none text-reset">MVP Development</a>
                           </li>
                           <li class="mb-2">
                              <a href="#!" class="text-decoration-none text-reset">Digital Marketing</a>
                           </li>
                           <li class="mb-2">
                              <a href="#!" class="text-decoration-none text-reset">Content Writing</a>
                           </li>
                        </ul>
                     </div>
                  </div>
               </div>
               <div class="col-lg-3 col-12">
                  <div>
                     <div class="mb-3 pb-2 d-flex justify-content-between border-bottom border-bottom-lg-0 position-relative">
                        <h4>About us</h4>
                        <a
                           class="d-block d-lg-none stretched-link text-body collapsed"
                           data-bs-toggle="collapse"
                           href="#collapseAccounts"
                           role="button"
                           aria-expanded="false"
                           aria-controls="collapseAccounts">
                           <i class="bi bi-chevron-down"></i>
                        </a>
                     </div>
                     <div class="collapse d-lg-block" id="collapseAccounts" data-bs-parent="#ft-links">
                        <ul class="list-unstyled mb-0 py-3 py-lg-0">
                           <li class="mb-2">
                              <a href="#!" class="text-decoration-none text-reset">Case Studies</a>
                           </li>
                           <li class="mb-2">
                              <a href="#!" class="text-decoration-none text-reset">Blog</a>
                           </li>
                           <li class="mb-2">
                              <a href="#!" class="text-decoration-none text-reset">Services</a>
                           </li>
                           <li class="mb-2">
                              <a href="#!" class="text-decoration-none text-reset">About</a>
                           </li>
                           <li class="mb-2">
                              <a href="#!" class="text-decoration-none text-reset">Career</a>
                           </li>
                        </ul>
                     </div>
                  </div>
               </div>
               <div class="col-lg-3 col-12">
                  <div class="mb-3 pb-2 d-flex justify-content-between border-bottom border-bottom-lg-0 position-relative">
                     <h4>Technology</h4>
                     <a
                        class="d-block d-lg-none stretched-link text-body collapsed"
                        data-bs-toggle="collapse"
                        href="#collapseResources"
                        role="button"
                        aria-expanded="false"
                        aria-controls="collapseResources">
                        <i class="bi bi-chevron-down"></i>
                     </a>
                  </div>
                  <div class="collapse d-lg-block" id="collapseResources" data-bs-parent="#ft-links">
                     <ul class="list-unstyled mb-0 py-3 py-lg-0">
                        <li class="mb-2">
                           <a href="./docs/index.html" class="text-decoration-none text-reset">Next.js</a>
                        </li>
                        <li class="mb-2">
                           <a href="#!" class="text-decoration-none text-reset">Sanity</a>
                        </li>
                        <li class="mb-2">
                           <a href="./changelog.html" class="text-decoration-none text-reset">Content ful</a>
                        </li>
                        <li class="mb-2">
                           <a href="#!" class="text-decoration-none text-reset">Vercel</a>
                        </li>
                        <li class="mb-2">
                           <a href="#!" class="text-decoration-none text-reset">Netlify</a>
                        </li>
                     </ul>
                  </div>
               </div>
               <div class="col-lg-3 col-12">
                  <div class="mb-3 pb-2 d-flex justify-content-between border-bottom border-bottom-lg-0 position-relative">
                     <h4>Locations</h4>
                     <a
                        class="d-block d-lg-none stretched-link text-body collapsed"
                        data-bs-toggle="collapse"
                        href="#collapseLocations"
                        role="button"
                        aria-expanded="false"
                        aria-controls="collapseLocations">
                        <i class="bi bi-chevron-down"></i>
                     </a>
                  </div>
                  <div class="collapse d-lg-block" id="collapseLocations" data-bs-parent="#ft-links">
                     <ul class="list-unstyled mb-0 py-3 py-lg-0">
                        <li class="mb-2">
                           <a href="./docs/index.html" class="text-decoration-none text-reset">India</a>
                        </li>
                        <li class="mb-2">
                           <a href="#!" class="text-decoration-none text-reset">Australia</a>
                        </li>
                        <li class="mb-2">
                           <a href="./changelog.html" class="text-decoration-none text-reset">Brazil</a>
                        </li>
                        <li class="mb-2">
                           <a href="#!" class="text-decoration-none text-reset">Canada</a>
                        </li>
                     </ul>
                  </div>
               </div>
            </div>
         </div>
         <div class="col-lg-3 col-12">
            <div class="me-7">
               <h4 class="mb-4">Headquarters</h4>
               <p class="text-body-secondary">Codescandy, 412, Residency Rd, Shanthala Nagar, Ashok Nagar, Bengaluru, Karnataka, India 560025</p>
            </div>
         </div>
      </div>
   </div>
   <div class="container mt-7 pt-lg-7 pb-4">
      <div class="row align-items-center">
         <div class="col-md-3">
            <a class="mb-4 mb-lg-0 d-block text-inverse" href="../index.html"><img src="./assets/images/logo/logo.svg" alt="" /></a>
         </div>
         <div class="col-md-9 col-lg-6">
            <div class="small mb-3 mb-lg-0 text-lg-center">
               Copyright © 2024

               <span class="text-primary"><a href="#">Block Bootstrap 5 Theme</a></span>
               | Designed by
               <span class="text-primary"><a href="#">CodesCandy</a></span>
            </div>
         </div>
         <div class="col-lg-3">
            <div class="text-lg-end d-flex align-items-center justify-content-lg-end">
               <div class="dropdown">
                  <button class="btn btn-light btn-icon rounded-circle d-flex align-items-center" type="button" aria-expanded="false" data-bs-toggle="dropdown" aria-label="Toggle theme (auto)">
                     <i class="bi theme-icon-active lh-1"><i class="bi theme-icon bi-sun-fill"></i></i>
                     <span class="visually-hidden bs-theme-text">Toggle theme</span>
                  </button>
                  <ul class="dropdown-menu dropdown-menu-end shadow" aria-labelledby="bs-theme-text">
                     <li>
                        <button type="button" class="dropdown-item d-flex align-items-center active" data-bs-theme-value="light" aria-pressed="true">
                           <i class="bi theme-icon bi-sun-fill"></i>
                           <span class="ms-2">Light</span>
                        </button>
                     </li>
                     <li>
                        <button type="button" class="dropdown-item d-flex align-items-center" data-bs-theme-value="dark" aria-pressed="false">
                           <i class="bi theme-icon bi-moon-stars-fill"></i>
                           <span class="ms-2">Dark</span>
                        </button>
                     </li>
                     <li>
                        <button type="button" class="dropdown-item d-flex align-items-center" data-bs-theme-value="auto" aria-pressed="false">
                           <i class="bi theme-icon bi-circle-half"></i>
                           <span class="ms-2">Auto</span>
                        </button>
                     </li>
                  </ul>
               </div>
               <div class="ms-3 d-flex gap-2">
                  <a href="#!" class="btn btn-instagram btn-light btn-icon">
                     <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-instagram" viewBox="0 0 16 16">
                        <path
                           d="M8 0C5.829 0 5.556.01 4.703.048 3.85.088 3.269.222 2.76.42a3.917 3.917 0 0 0-1.417.923A3.927 3.927 0 0 0 .42 2.76C.222 3.268.087 3.85.048 4.7.01 5.555 0 5.827 0 8.001c0 2.172.01 2.444.048 3.297.04.852.174 1.433.372 1.942.205.526.478.972.923 1.417.444.445.89.719 1.416.923.51.198 1.09.333 1.942.372C5.555 15.99 5.827 16 8 16s2.444-.01 3.298-.048c.851-.04 1.434-.174 1.943-.372a3.916 3.916 0 0 0 1.416-.923c.445-.445.718-.891.923-1.417.197-.509.332-1.09.372-1.942C15.99 10.445 16 10.173 16 8s-.01-2.445-.048-3.299c-.04-.851-.175-1.433-.372-1.941a3.926 3.926 0 0 0-.923-1.417A3.911 3.911 0 0 0 13.24.42c-.51-.198-1.092-.333-1.943-.372C10.443.01 10.172 0 7.998 0h.003zm-.717 1.442h.718c2.136 0 2.389.007 3.232.046.78.035 1.204.166 1.486.275.373.145.64.319.92.599.28.28.453.546.598.92.11.281.24.705.275 1.485.039.843.047 1.096.047 3.231s-.008 2.389-.047 3.232c-.035.78-.166 1.203-.275 1.485a2.47 2.47 0 0 1-.599.919c-.28.28-.546.453-.92.598-.28.11-.704.24-1.485.276-.843.038-1.096.047-3.232.047s-2.39-.009-3.233-.047c-.78-.036-1.203-.166-1.485-.276a2.478 2.478 0 0 1-.92-.598 2.48 2.48 0 0 1-.6-.92c-.109-.281-.24-.705-.275-1.485-.038-.843-.046-1.096-.046-3.233 0-2.136.008-2.388.046-3.231.036-.78.166-1.204.276-1.486.145-.373.319-.64.599-.92.28-.28.546-.453.92-.598.282-.11.705-.24 1.485-.276.738-.034 1.024-.044 2.515-.045v.002zm4.988 1.328a.96.96 0 1 0 0 1.92.96.96 0 0 0 0-1.92zm-4.27 1.122a4.109 4.109 0 1 0 0 8.217 4.109 4.109 0 0 0 0-8.217zm0 1.441a2.667 2.667 0 1 1 0 5.334 2.667 2.667 0 0 1 0-5.334z"></path>
                     </svg>
                  </a>
                  <a href="#!" class="btn btn-facebook btn-icon">
                     <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-facebook" viewBox="0 0 16 16">
                        <path
                           d="M16 8.049c0-4.446-3.582-8.05-8-8.05C3.58 0-.002 3.603-.002 8.05c0 4.017 2.926 7.347 6.75 7.951v-5.625h-2.03V8.05H6.75V6.275c0-2.017 1.195-3.131 3.022-3.131.876 0 1.791.157 1.791.157v1.98h-1.009c-.993 0-1.303.621-1.303 1.258v1.51h2.218l-.354 2.326H9.25V16c3.824-.604 6.75-3.934 6.75-7.951z"></path>
                     </svg>
                  </a>
                  <a href="#!" class="btn btn-twitter btn-icon">
                     <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-twitter" viewBox="0 0 16 16">
                        <path
                           d="M5.026 15c6.038 0 9.341-5.003 9.341-9.334 0-.14 0-.282-.006-.422A6.685 6.685 0 0 0 16 3.542a6.658 6.658 0 0 1-1.889.518 3.301 3.301 0 0 0 1.447-1.817 6.533 6.533 0 0 1-2.087.793A3.286 3.286 0 0 0 7.875 6.03a9.325 9.325 0 0 1-6.767-3.429 3.289 3.289 0 0 0 1.018 4.382A3.323 3.323 0 0 1 .64 6.575v.045a3.288 3.288 0 0 0 2.632 3.218 3.203 3.203 0 0 1-.865.115 3.23 3.23 0 0 1-.614-.057 3.283 3.283 0 0 0 3.067 2.277A6.588 6.588 0 0 1 .78 13.58a6.32 6.32 0 0 1-.78-.045A9.344 9.344 0 0 0 5.026 15z"></path>
                     </svg>
                  </a>
               </div>
            </div>
         </div>
      </div>
   </div>
</footer>
 <div class="btn-scroll-top">
   <svg class="progress-square svg-content" width="100%" height="100%" viewBox="0 0 40 40">
      <path d="M8 1H32C35.866 1 39 4.13401 39 8V32C39 35.866 35.866 39 32 39H8C4.13401 39 1 35.866 1 32V8C1 4.13401 4.13401 1 8 1Z" />
   </svg>
</div>
 <!-- Libs JS -->
<script src="./assets/libs/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
<script src="./assets/libs/simplebar/dist/simplebar.min.js"></script>
<script src="./assets/libs/headhesive/dist/headhesive.min.js"></script>

<!-- Theme JS -->
<script src="./assets/js/theme.min.js"></script>


      <script src="./assets/libs/scrollcue/scrollCue.min.js"></script>
      <script src="./assets/js/vendors/scrollcue.js"></script>
   </body>
</html>
