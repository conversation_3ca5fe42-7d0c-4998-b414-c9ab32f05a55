<!doctype html>
<html lang="en">
   <head>
      <!-- Required meta tags -->
<meta charset="utf-8" />
<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />

      <link href="../assets/libs/prismjs/themes/prism-okaidia.min.css" rel="stylesheet" />
      <link rel="stylesheet" href="../assets/libs/swiper/swiper-bundle.min.css" />
      <!-- Favicon icon-->
<link rel="apple-touch-icon" sizes="180x180" href="../assets/images/favicon/apple-touch-icon.png" />
<link rel="icon" type="image/png" sizes="32x32" href="../assets/images/favicon/favicon-32x32.png" />
<link rel="icon" type="image/png" sizes="16x16" href="../assets/images/favicon/favicon-16x16.png" />
<link rel="manifest" href="../assets/images/favicon/site.webmanifest" />
<link rel="mask-icon" href="../assets/images/favicon/block-safari-pinned-tab.svg" color="#8b3dff" />
<link rel="shortcut icon" href="../assets/images/favicon/favicon.ico" />
<meta name="msapplication-TileColor" content="#8b3dff" />
<meta name="msapplication-config" content="../assets/images/favicon/tile.xml" />

<!-- Color modes -->
<script src="../assets/js/vendors/color-modes.js"></script>

<!-- Libs CSS -->
<link href="../assets/libs/simplebar/dist/simplebar.min.css" rel="stylesheet" />
<link href="../assets/libs/bootstrap-icons/font/bootstrap-icons.min.css" rel="stylesheet" />

<!-- Scroll Cue -->
<link rel="stylesheet" href="../assets/libs/scrollcue/scrollCue.css" />

<!-- Box icons -->
<link rel="stylesheet" href="../assets/fonts/css/boxicons.min.css" />

<!-- Theme CSS -->
<link rel="stylesheet" href="../assets/css/theme.min.css">

      <title>Services Snippet - Responsive Website Template | Block</title>
   </head>

   <body>
      <!-- Navbar -->
<header>
   <nav class="navbar navbar-expand-lg  navbar-light w-100">
      <div class="container px-3">
         <a class="navbar-brand" href="../index.html"><img src="../assets/images/logo/logo.svg" alt /></a>
         <button class="navbar-toggler offcanvas-nav-btn" type="button">
            <i class="bi bi-list"></i>
         </button>
         <div class="offcanvas offcanvas-start offcanvas-nav" style="width: 20rem">
            <div class="offcanvas-header">
               <a href="../index.html" class="text-inverse"><img src="../assets/images/logo/logo.svg" alt /></a>
               <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close"></button>
            </div>
            <div class="offcanvas-body pt-0 align-items-center">
               <ul class="navbar-nav mx-auto align-items-lg-center">
                  <li class="nav-item">
                     <a class="nav-link" href="../home.html">Home</a>
                  </li>
                  <li class="nav-item">
                     <a class="nav-link" href="../scan-pay.html">Scan & Pay</a>
                  </li>
                  <li class="nav-item">
                     <a class="nav-link" href="../service.html">Services</a>
                  </li>
                  <li class="nav-item">
                     <a class="nav-link" href="../contact.html">Contact</a>
                  </li>
                  <li class="nav-item">
                     <a class="nav-link" href="../become-an-agent.html">Become An Agent</a>
                  </li>
               </ul>
               <div class="mt-3 mt-lg-0 d-flex align-items-center">
                  <a href="../signin.html" class="btn btn-light mx-2">Login</a>
                  <a href="https://play.google.com/store/apps/details?id=com.qsoft.aidapay&hl=en&pli=1" class="btn btn-primary">Create account</a>
               </div>
            </div>
         </div>
      </div>
   </nav>
</header>

      <main>
         <div class="pattern-square"></div>
         <section class="bg-light py-5 py-lg-8 bg-opacity-50">
            <div class="container">
               <div class="row">
                  <div class="col-12 col-md-6">
                     <div>
                        <h1 class="mb-0">Services Sections</h1>
                     </div>
                  </div>
                  <!-- Navbar Filter tabs -->
<div class="mt-6 col-12 d-flex flex-wrap gap-2">
   <a href="../blocks/about.html" class="filter-badge ">About</a>
   <a href="../blocks/award.html" class="filter-badge ">Award</a>
   <a href="../blocks/blog.html" class="filter-badge ">Blog</a>
   <a href="../blocks/cta.html" class="filter-badge ">Call to Action</a>
   <a href="../blocks/clients.html" class="filter-badge ">Clients</a>
   <a href="../blocks/contact.html" class="filter-badge ">Contact</a>
   <a href="../blocks/carousel.html" class="filter-badge ">Carousel</a>
   <a href="../blocks/case-study.html" class="filter-badge ">Case Study</a>
   <a href="../blocks/facts.html" class="filter-badge ">Facts</a>
   <a href="../blocks/faq.html" class="filter-badge ">FAQ</a>
   <a href="../blocks/features.html" class="filter-badge ">Features</a>
   <a href="../blocks/form.html" class="filter-badge ">Form</a>
   <a href="../blocks/footer.html" class="filter-badge ">Footer</a>
   <a href="../blocks/hero.html" class="filter-badge ">Hero</a>
   <a href="../blocks/integration.html" class="filter-badge ">Integration</a>
   <a href="../blocks/industry.html" class="filter-badge ">Industry</a>
   <a href="../blocks/location.html" class="filter-badge ">Location</a>
   <a href="../blocks/navbar.html" class="filter-badge ">Navbar</a>
   <a href="../blocks/portfolio.html" class="filter-badge ">Portfolio</a>
   <a href="../blocks/pricing.html" class="filter-badge ">Pricing</a>
   <a href="../blocks/process.html" class="filter-badge ">Process</a>
   <a href="../blocks/services.html" class="filter-badge  active ">Services</a>
   <a href="../blocks/team.html" class="filter-badge ">Team</a>
   <a href="../blocks/testimonails.html" class="filter-badge ">Testimonials</a>
</div>

               </div>
            </div>
         </section>
         <section class="py-lg-8 py-5">
            <div class="container">
               <div class="mb-lg-7 mb-5">
                  <div class="row align-items-center">
                     <div class="col-lg-8 col-xl-9 col-7">
                        <div>
                           <h2 class="text-truncate h5 mb-0">Service #1</h2>
                        </div>
                     </div>
                     <div class="col-lg-4 col-xl-3 col-5 d-flex justify-content-end">
                        <ul class="nav nav-pills nav-custom-pill" id="pills-tabTwo" role="tablist">
                           <li class="nav-item">
                              <a
                                 class="nav-link active"
                                 id="pills-award-one-preview-tab"
                                 data-bs-toggle="pill"
                                 href="#pills-award-one-preview"
                                 role="tab"
                                 aria-controls="pills-award-one-preview"
                                 aria-selected="true">
                                 <span class="lh-1"><i class="bi bi-eye"></i></span>
                                 <span class="ms-2 d-none d-lg-block">Preview</span>
                              </a>
                           </li>
                           <li class="nav-item">
                              <a
                                 class="nav-link"
                                 id="pills-award-one-code-tab"
                                 data-bs-toggle="pill"
                                 href="#pills-award-one-code"
                                 role="tab"
                                 aria-controls="pills-award-one-code"
                                 aria-selected="false">
                                 <span class="lh-1"><i class="bi bi-code"></i></span>
                                 <span class="ms-2 d-none d-lg-block">Code</span>
                              </a>
                           </li>
                        </ul>
                     </div>
                  </div>
                  <div class="row">
                     <div class="col-md-12">
                        <div class="tab-content border mt-3 p-3 rounded-2" id="pills-tabTwoContent">
                           <div class="tab-pane tab-example-preview fade show active" id="pills-award-one-preview" role="tabpanel" aria-labelledby="pills-award-one-preview-tab">
                              <section class="py-5">
                                 <!--Logo section start-->
                                 <div class="container">
                                    <div class="row mb-6">
                                       <div class="col-xl-5">
                                          <div>
                                             <h2 class="mb-0">Maximize your online visibility with expert SEO services</h2>
                                          </div>
                                       </div>
                                       <div class="offset-xl-1 col-xl-6">
                                          <div>
                                             <p class="mb-0">
                                                At
                                                <span class="text-dark fw-semibold">[Agency Name]</span>
                                                , we specialize in delivering comprehensive SEO solutions designed to enhance your online presence, drive targeted traffic, and boost your business
                                                growth.
                                             </p>
                                          </div>
                                       </div>
                                    </div>
                                    <div class="row gy-4">
                                       <div class="col-xl-3">
                                          <div class="card border-0 h-100" data-bs-theme="dark">
                                             <img src="../assets/images/seo/seo-img.jpg" alt="" class="rounded-3 img-fluid w-100 object-fit-cover horizontal-img" />
                                             <div class="card-img-overlay" style="background-color: rgb(30, 41, 59, 0.75)">
                                                <div class="position-absolute bottom-0 start-0 end-0 p-4">
                                                   <h3>SEO Services</h3>
                                                   <p class="text-white-stick">Our SEO services are tailored to meet the unique needs of your business,.</p>
                                                   <a href="#" class="btn btn-primary">All SEO services</a>
                                                </div>
                                             </div>
                                          </div>
                                       </div>
                                       <div class="col-xl-9">
                                          <div class="row gx-4 gy-4">
                                             <div class="col-lg-4 col-md-6 col-12">
                                                <div class="card shadow-sm card-lift h-100">
                                                   <div class="card-body">
                                                      <div class="mb-4">
                                                         <svg width="48" height="48" viewBox="0 0 56 56" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                            <path
                                                               d="M0 16C0 7.16344 7.16344 0 16 0H32C40.8366 0 48 7.16344 48 16V32C48 40.8366 40.8366 48 32 48H16C7.16344 48 0 40.8366 0 32V16Z"
                                                               fill="#FFC107"
                                                               fill-opacity="0.25" />
                                                            <path
                                                               opacity="0.2"
                                                               d="M44 29C44 31.9667 43.1203 34.8668 41.4721 37.3336C39.8238 39.8003 37.4812 41.7229 34.7403 42.8582C31.9994 43.9935 28.9834 44.2906 26.0737 43.7118C23.1639 43.133 20.4912 41.7044 18.3934 39.6066C16.2956 37.5088 14.867 34.8361 14.2882 31.9264C13.7094 29.0166 14.0065 26.0006 15.1418 23.2597C16.2771 20.5189 18.1997 18.1762 20.6665 16.528C23.1332 14.8797 26.0333 14 29 14C30.9698 14 32.9204 14.388 34.7403 15.1418C36.5601 15.8956 38.2137 17.0005 39.6066 18.3934C40.9995 19.7863 42.1044 21.4399 42.8582 23.2597C43.612 25.0796 44 27.0302 44 29Z"
                                                               fill="#8B3DFF" />
                                                            <path
                                                               d="M51.0612 48.9392L41.675 39.553C44.4009 36.2838 45.7614 32.0895 45.4735 27.8427C45.1857 23.596 43.2716 19.6237 40.1295 16.7522C36.9874 13.8807 32.8592 12.3312 28.6038 12.426C24.3483 12.5207 20.2932 14.2525 17.2821 17.261C14.2709 20.2695 12.5356 24.323 12.4371 28.5784C12.3385 32.8338 13.8844 36.9633 16.7531 40.108C19.6218 43.2526 23.5924 45.1702 27.8389 45.4618C32.0854 45.7534 36.2809 44.3966 39.5525 41.6736L48.9387 51.0617C49.0781 51.2011 49.2436 51.3116 49.4257 51.3871C49.6077 51.4625 49.8029 51.5013 50 51.5013C50.1971 51.5013 50.3923 51.4625 50.5743 51.3871C50.7564 51.3116 50.9219 51.2011 51.0612 51.0617C51.2006 50.9224 51.3112 50.7569 51.3866 50.5748C51.462 50.3927 51.5008 50.1976 51.5008 50.0005C51.5008 49.8034 51.462 49.6082 51.3866 49.4261C51.3112 49.244 51.2006 49.0786 51.0612 48.9392ZM15.5 29.0005C15.5 26.3304 16.2918 23.7203 17.7752 21.5003C19.2586 19.2802 21.367 17.5499 23.8338 16.5281C26.3006 15.5063 29.015 15.239 31.6337 15.7599C34.2525 16.2808 36.6579 17.5665 38.5459 19.4545C40.4339 21.3426 41.7197 23.748 42.2406 26.3668C42.7615 28.9855 42.4942 31.6999 41.4724 34.1667C40.4506 36.6335 38.7203 38.7419 36.5002 40.2253C34.2801 41.7087 31.67 42.5005 29 42.5005C25.4208 42.4965 21.9893 41.0729 19.4584 38.542C16.9276 36.0112 15.504 32.5797 15.5 29.0005Z"
                                                               fill="#8B3DFF" />
                                                         </svg>
                                                      </div>
                                                      <h3 class="fs-4">Keyword Research</h3>
                                                      <p class="mb-0">We conduct thorough keyword research to identify the high-traffic keywords for your industry.</p>
                                                      <div class="border-top mt-4">
                                                         <a href="#" class="text-reset mt-3 d-inline-flex gap-2 align-items-center icon-link icon-link-hover">
                                                            Explore Keyword Research

                                                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-arrow-right" viewBox="0 0 16 16">
                                                               <path
                                                                  fill-rule="evenodd"
                                                                  d="M1 8a.5.5 0 0 1 .5-.5h11.793l-3.147-3.146a.5.5 0 0 1 .708-.708l4 4a.5.5 0 0 1 0 .708l-4 4a.5.5 0 0 1-.708-.708L13.293 8.5H1.5A.5.5 0 0 1 1 8" />
                                                            </svg>
                                                         </a>
                                                      </div>
                                                   </div>
                                                </div>
                                             </div>
                                             <div class="col-lg-4 col-md-6 col-12">
                                                <div class="card shadow-sm card-lift h-100">
                                                   <div class="card-body">
                                                      <div class="mb-4">
                                                         <svg width="48" height="48" viewBox="0 0 56 56" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                            <path
                                                               d="M0 16C0 7.16344 7.16344 0 16 0H32C40.8366 0 48 7.16344 48 16V32C48 40.8366 40.8366 48 32 48H16C7.16344 48 0 40.8366 0 32V16Z"
                                                               fill="#FFC107"
                                                               fill-opacity="0.25" />
                                                            <path
                                                               opacity="0.2"
                                                               d="M48.5 34.125V39.75C48.5 41.3915 48.1767 43.017 47.5485 44.5335C46.9203 46.0501 45.9996 47.4281 44.8389 48.5888C43.6781 49.7496 42.3001 50.6703 40.7836 51.2985C39.267 51.9267 37.6415 52.25 36 52.25C29.0969 52.25 27.2797 48.5 21.4188 38.1875C21.0109 37.4701 20.9033 36.6206 21.1196 35.8242C21.3358 35.0278 21.8582 34.3493 22.5728 33.9366C23.2875 33.524 24.1363 33.4108 24.9341 33.6218C25.7319 33.8327 26.4139 34.3506 26.8313 35.0625L29.75 39.75V22.875C29.75 22.0462 30.0793 21.2513 30.6653 20.6653C31.2514 20.0792 32.0462 19.75 32.875 19.75C33.7038 19.75 34.4987 20.0792 35.0847 20.6653C35.6708 21.2513 36 22.0462 36 22.875V31.625C36 30.7962 36.3293 30.0013 36.9153 29.4153C37.5014 28.8292 38.2962 28.5 39.125 28.5C39.9538 28.5 40.7487 28.8292 41.3347 29.4153C41.9208 30.0013 42.25 30.7962 42.25 31.625V34.125C42.25 33.2962 42.5793 32.5013 43.1653 31.9153C43.7514 31.3292 44.5462 31 45.375 31C46.2038 31 46.9987 31.3292 47.5847 31.9153C48.1708 32.5013 48.5 33.2962 48.5 34.125Z"
                                                               fill="#8B3DFF" />
                                                            <path
                                                               d="M45.375 29.75C44.6463 29.7492 43.9291 29.9313 43.2891 30.2797C43.0977 29.6871 42.782 29.1423 42.3632 28.6815C41.9443 28.2207 41.4319 27.8547 40.8603 27.6078C40.2886 27.361 39.6708 27.239 39.0482 27.25C38.4256 27.2611 37.8126 27.4049 37.25 27.6719V22.875C37.25 21.7147 36.7891 20.6019 35.9686 19.7814C35.1481 18.9609 34.0353 18.5 32.875 18.5C31.7147 18.5 30.6019 18.9609 29.7814 19.7814C28.9609 20.6019 28.5 21.7147 28.5 22.875V35.375L27.9031 34.4172C27.6145 33.9201 27.2309 33.4848 26.774 33.136C26.3171 32.7872 25.7961 32.5318 25.2405 32.3844C24.1185 32.0866 22.9242 32.2468 21.9203 32.8297C20.9164 33.4125 20.1852 34.3703 19.8875 35.4923C19.5898 36.6143 19.75 37.8086 20.3328 38.8125L21.0625 40.0984C26.4391 49.5766 28.6641 53.5 36 53.5C39.6455 53.4959 43.1404 52.0459 45.7182 49.4682C48.2959 46.8904 49.7459 43.3955 49.75 39.75V34.125C49.75 32.9647 49.2891 31.8519 48.4686 31.0314C47.6481 30.2109 46.5353 29.75 45.375 29.75ZM47.25 39.75C47.2467 42.7327 46.0604 45.5922 43.9513 47.7013C41.8422 49.8104 38.9827 50.9967 36 51C30.1203 51 28.525 48.1875 23.2375 38.8625L22.5047 37.5703V37.5625C22.2559 37.1323 22.188 36.6209 22.3161 36.1407C22.4441 35.6605 22.7575 35.2508 23.1875 35.0016C23.4723 34.8366 23.7959 34.7503 24.125 34.7516C24.4546 34.7511 24.7785 34.8375 25.0641 35.0021C25.3496 35.1667 25.5868 35.4036 25.7516 35.6891C25.7579 35.7016 25.7653 35.7136 25.7735 35.725L28.6906 40.4125C28.8346 40.6424 29.0494 40.8192 29.3027 40.9162C29.556 41.0133 29.834 41.0253 30.0947 40.9505C30.3554 40.8757 30.5847 40.7182 30.7481 40.5016C30.9114 40.285 30.9998 40.0212 31 39.75V22.875C31 22.3777 31.1976 21.9008 31.5492 21.5492C31.9008 21.1975 32.3777 21 32.875 21C33.3723 21 33.8492 21.1975 34.2008 21.5492C34.5525 21.9008 34.75 22.3777 34.75 22.875V33.5C34.75 33.8315 34.8817 34.1495 35.1161 34.3839C35.3505 34.6183 35.6685 34.75 36 34.75C36.3315 34.75 36.6495 34.6183 36.8839 34.3839C37.1183 34.1495 37.25 33.8315 37.25 33.5V31.625C37.25 31.1277 37.4476 30.6508 37.7992 30.2992C38.1508 29.9475 38.6277 29.75 39.125 29.75C39.6223 29.75 40.0992 29.9475 40.4508 30.2992C40.8025 30.6508 41 31.1277 41 31.625V34.75C41 35.0815 41.1317 35.3995 41.3661 35.6339C41.6005 35.8683 41.9185 36 42.25 36C42.5815 36 42.8995 35.8683 43.1339 35.6339C43.3683 35.3995 43.5 35.0815 43.5 34.75V34.125C43.5 33.6277 43.6976 33.1508 44.0492 32.7992C44.4008 32.4475 44.8777 32.25 45.375 32.25C45.8723 32.25 46.3492 32.4475 46.7008 32.7992C47.0525 33.1508 47.25 33.6277 47.25 34.125V39.75Z"
                                                               fill="#8B3DFF" />
                                                         </svg>
                                                      </div>
                                                      <h3 class="fs-4">On-Page SEO</h3>
                                                      <p class="mb-0">Our on-page SEO services optimize your website's content and HTML source code.</p>
                                                      <div class="border-top mt-4">
                                                         <a href="#" class="text-reset mt-3 d-inline-flex gap-2 align-items-center icon-link icon-link-hover">
                                                            Explore Social Media

                                                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-arrow-right" viewBox="0 0 16 16">
                                                               <path
                                                                  fill-rule="evenodd"
                                                                  d="M1 8a.5.5 0 0 1 .5-.5h11.793l-3.147-3.146a.5.5 0 0 1 .708-.708l4 4a.5.5 0 0 1 0 .708l-4 4a.5.5 0 0 1-.708-.708L13.293 8.5H1.5A.5.5 0 0 1 1 8" />
                                                            </svg>
                                                         </a>
                                                      </div>
                                                   </div>
                                                </div>
                                             </div>
                                             <div class="col-lg-4 col-md-6 col-12">
                                                <div class="card shadow-sm card-lift h-100">
                                                   <div class="card-body">
                                                      <div class="mb-4">
                                                         <svg width="48" height="48" viewBox="0 0 56 56" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                            <path
                                                               d="M0 16C0 7.16344 7.16344 0 16 0H32C40.8366 0 48 7.16344 48 16V32C48 40.8366 40.8366 48 32 48H16C7.16344 48 0 40.8366 0 32V16Z"
                                                               fill="#FFC107"
                                                               fill-opacity="0.25" />
                                                            <path
                                                               opacity="0.2"
                                                               d="M48.8031 33.8029L33.8031 48.8029C32.3928 50.1905 30.4912 50.9646 28.5127 50.9565C26.5341 50.9485 24.639 50.1589 23.2399 48.7599C21.8409 47.3608 21.0513 45.4657 21.0433 43.4871C21.0352 41.5086 21.8093 39.607 23.1969 38.1967L38.1969 23.1967C39.6073 21.8091 41.5088 21.035 43.4874 21.043C45.4659 21.0511 47.3611 21.8406 48.7601 23.2397C50.1592 24.6387 50.9487 26.5339 50.9568 28.5124C50.9648 30.491 50.1907 32.3925 48.8031 33.8029Z"
                                                               fill="#8B3DFF" />
                                                            <path
                                                               d="M41.8843 30.1153C42.0006 30.2314 42.0928 30.3692 42.1557 30.521C42.2186 30.6727 42.2509 30.8354 42.2509 30.9997C42.2509 31.1639 42.2186 31.3266 42.1557 31.4783C42.0928 31.6301 42.0006 31.7679 41.8843 31.884L31.8843 41.884C31.7682 42.0002 31.6303 42.0923 31.4786 42.1551C31.3268 42.218 31.1642 42.2503 31 42.2503C30.8357 42.2503 30.6731 42.218 30.5213 42.1551C30.3696 42.0923 30.2317 42.0002 30.1156 41.884C29.9994 41.7679 29.9073 41.63 29.8445 41.4783C29.7816 41.3265 29.7493 41.1639 29.7493 40.9997C29.7493 40.8354 29.7816 40.6728 29.8445 40.521C29.9073 40.3693 29.9994 40.2314 30.1156 40.1153L40.1156 30.1153C40.2317 29.9991 40.3695 29.9069 40.5213 29.844C40.673 29.781 40.8357 29.7487 41 29.7487C41.1642 29.7487 41.3269 29.781 41.4786 29.844C41.6304 29.9069 41.7682 29.9991 41.8843 30.1153ZM49.6875 22.3122C48.8749 21.4995 47.9103 20.8549 46.8486 20.4152C45.787 19.9754 44.6491 19.749 43.5 19.749C42.3508 19.749 41.2129 19.9754 40.1513 20.4152C39.0896 20.8549 38.125 21.4995 37.3125 22.3122L32.6156 27.0075C32.381 27.242 32.2493 27.5601 32.2493 27.8918C32.2493 28.2235 32.381 28.5417 32.6156 28.7762C32.8501 29.0108 33.1683 29.1425 33.5 29.1425C33.8317 29.1425 34.1498 29.0108 34.3843 28.7762L39.0812 24.0887C40.2576 22.9382 41.8402 22.298 43.4857 22.307C45.1312 22.3161 46.7067 22.9737 47.8703 24.1371C49.0339 25.3006 49.6918 26.8759 49.7012 28.5214C49.7105 30.1669 49.0706 31.7496 47.9203 32.9262L43.2218 37.6231C42.9873 37.8574 42.8554 38.1754 42.8553 38.5069C42.8551 38.8385 42.9867 39.1565 43.221 39.3911C43.4554 39.6256 43.7733 39.7575 44.1049 39.7576C44.4364 39.7578 44.7545 39.6262 44.989 39.3918L49.6875 34.6872C50.5001 33.8746 51.1447 32.91 51.5844 31.8483C52.0242 30.7867 52.2506 29.6488 52.2506 28.4997C52.2506 27.3505 52.0242 26.2126 51.5844 25.151C51.1447 24.0893 50.5001 23.1247 49.6875 22.3122ZM37.6156 43.2215L32.9187 47.9184C32.3407 48.5094 31.6512 48.9798 30.8901 49.3025C30.1291 49.6252 29.3116 49.7937 28.4849 49.7982C27.6583 49.8028 26.839 49.6433 26.0744 49.329C25.3098 49.0148 24.6152 48.5519 24.0307 47.9673C23.4462 47.3828 22.9835 46.6881 22.6694 45.9234C22.3552 45.1588 22.1959 44.3394 22.2006 43.5128C22.2053 42.6862 22.3739 41.8687 22.6967 41.1077C23.0196 40.3467 23.4901 39.6572 24.0812 39.0793L28.7765 34.384C29.0111 34.1495 29.1428 33.8314 29.1428 33.4997C29.1428 33.1679 29.0111 32.8498 28.7765 32.6153C28.542 32.3807 28.2238 32.249 27.8921 32.249C27.5604 32.249 27.2423 32.3807 27.0078 32.6153L22.3125 37.3122C20.6714 38.9532 19.7495 41.1789 19.7495 43.4997C19.7495 45.8204 20.6714 48.0461 22.3125 49.6872C23.9535 51.3282 26.1792 52.2501 28.5 52.2501C30.8207 52.2501 33.0464 51.3282 34.6875 49.6872L39.3843 44.9887C39.6187 44.7542 39.7502 44.4361 39.7501 44.1046C39.75 43.773 39.6181 43.4551 39.3835 43.2207C39.149 42.9864 38.831 42.8548 38.4994 42.855C38.1678 42.8551 37.8499 42.987 37.6156 43.2215Z"
                                                               fill="#8B3DFF" />
                                                         </svg>
                                                      </div>
                                                      <h3 class="fs-4">Link Building</h3>
                                                      <p class="mb-0">We build a robust backlink profile through ethical and effective link-building strategies.</p>
                                                      <div class="border-top mt-4">
                                                         <a href="#" class="text-reset mt-3 d-inline-flex gap-2 align-items-center icon-link icon-link-hover">
                                                            Link Building Services

                                                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-arrow-right" viewBox="0 0 16 16">
                                                               <path
                                                                  fill-rule="evenodd"
                                                                  d="M1 8a.5.5 0 0 1 .5-.5h11.793l-3.147-3.146a.5.5 0 0 1 .708-.708l4 4a.5.5 0 0 1 0 .708l-4 4a.5.5 0 0 1-.708-.708L13.293 8.5H1.5A.5.5 0 0 1 1 8" />
                                                            </svg>
                                                         </a>
                                                      </div>
                                                   </div>
                                                </div>
                                             </div>
                                             <div class="col-lg-4 col-md-6 col-12">
                                                <div class="card shadow-sm card-lift h-100">
                                                   <div class="card-body">
                                                      <div class="mb-4">
                                                         <svg width="48" height="48" viewBox="0 0 56 56" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                            <path
                                                               d="M0 16C0 7.16344 7.16344 0 16 0H32C40.8366 0 48 7.16344 48 16V32C48 40.8366 40.8366 48 32 48H16C7.16344 48 0 40.8366 0 32V16Z"
                                                               fill="#FFC107"
                                                               fill-opacity="0.25" />
                                                            <path
                                                               opacity="0.2"
                                                               d="M36 19.75C32.6848 19.75 29.5054 21.067 27.1612 23.4112C24.817 25.7554 23.5 28.9348 23.5 32.25C23.5 43.5 36 52.25 36 52.25C36 52.25 48.5 43.5 48.5 32.25C48.5 28.9348 47.183 25.7554 44.8388 23.4112C42.4946 21.067 39.3152 19.75 36 19.75ZM36 37.25C35.0111 37.25 34.0444 36.9568 33.2221 36.4073C32.3999 35.8579 31.759 35.077 31.3806 34.1634C31.0022 33.2498 30.9031 32.2445 31.0961 31.2745C31.289 30.3046 31.7652 29.4137 32.4645 28.7145C33.1637 28.0152 34.0546 27.539 35.0245 27.3461C35.9945 27.1531 36.9998 27.2522 37.9134 27.6306C38.827 28.009 39.6079 28.6499 40.1573 29.4721C40.7068 30.2944 41 31.2611 41 32.25C41 33.5761 40.4732 34.8479 39.5355 35.7855C38.5979 36.7232 37.3261 37.25 36 37.25Z"
                                                               fill="#8B3DFF" />
                                                            <path
                                                               d="M36 26C34.7639 26 33.5555 26.3666 32.5277 27.0533C31.4999 27.7401 30.6988 28.7162 30.2258 29.8582C29.7527 31.0003 29.6289 32.2569 29.8701 33.4693C30.1112 34.6817 30.7065 35.7953 31.5806 36.6694C32.4547 37.5435 33.5683 38.1388 34.7807 38.3799C35.9931 38.6211 37.2497 38.4973 38.3918 38.0242C39.5338 37.5512 40.5099 36.7501 41.1967 35.7223C41.8834 34.6945 42.25 33.4861 42.25 32.25C42.25 30.5924 41.5915 29.0027 40.4194 27.8306C39.2473 26.6585 37.6576 26 36 26ZM36 36C35.2583 36 34.5333 35.7801 33.9166 35.368C33.2999 34.956 32.8193 34.3703 32.5355 33.6851C32.2516 32.9998 32.1774 32.2458 32.3221 31.5184C32.4667 30.791 32.8239 30.1228 33.3483 29.5983C33.8728 29.0739 34.541 28.7167 35.2684 28.5721C35.9958 28.4274 36.7498 28.5016 37.4351 28.7855C38.1203 29.0693 38.706 29.5499 39.118 30.1666C39.5301 30.7833 39.75 31.5083 39.75 32.25C39.75 33.2446 39.3549 34.1984 38.6517 34.9017C37.9484 35.6049 36.9946 36 36 36ZM36 18.5C32.3545 18.5041 28.8596 19.9541 26.2818 22.5318C23.7041 25.1096 22.2541 28.6045 22.25 32.25C22.25 37.1562 24.5172 42.3563 28.8125 47.2891C30.7425 49.5181 32.9148 51.5252 35.2891 53.2734C35.4992 53.4207 35.7496 53.4996 36.0062 53.4996C36.2629 53.4996 36.5133 53.4207 36.7234 53.2734C39.0934 51.5245 41.2614 49.5174 43.1875 47.2891C47.4766 42.3563 49.75 37.1562 49.75 32.25C49.7459 28.6045 48.2959 25.1096 45.7182 22.5318C43.1404 19.9541 39.6455 18.5041 36 18.5ZM36 50.6875C33.4172 48.6562 24.75 41.1953 24.75 32.25C24.75 29.2663 25.9353 26.4048 28.045 24.295C30.1548 22.1853 33.0163 21 36 21C38.9837 21 41.8452 22.1853 43.955 24.295C46.0647 26.4048 47.25 29.2663 47.25 32.25C47.25 41.1922 38.5828 48.6562 36 50.6875Z"
                                                               fill="#8B3DFF" />
                                                         </svg>
                                                      </div>
                                                      <h3 class="fs-4">Local SEO</h3>
                                                      <p class="mb-0">Our local SEO services focus on optimizing your online presence to attract more business.</p>
                                                      <div class="border-top mt-4">
                                                         <a href="#" class="text-reset mt-3 d-inline-flex gap-2 align-items-center icon-link icon-link-hover">
                                                            Local SEO Services

                                                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-arrow-right" viewBox="0 0 16 16">
                                                               <path
                                                                  fill-rule="evenodd"
                                                                  d="M1 8a.5.5 0 0 1 .5-.5h11.793l-3.147-3.146a.5.5 0 0 1 .708-.708l4 4a.5.5 0 0 1 0 .708l-4 4a.5.5 0 0 1-.708-.708L13.293 8.5H1.5A.5.5 0 0 1 1 8" />
                                                            </svg>
                                                         </a>
                                                      </div>
                                                   </div>
                                                </div>
                                             </div>
                                             <div class="col-lg-4 col-md-6 col-12">
                                                <div class="card shadow-sm card-lift h-100">
                                                   <div class="card-body">
                                                      <div class="mb-4">
                                                         <svg width="48" height="48" viewBox="0 0 56 56" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                            <path
                                                               d="M0 16C0 7.16344 7.16344 0 16 0H32C40.8366 0 48 7.16344 48 16V32C48 40.8366 40.8366 48 32 48H16C7.16344 48 0 40.8366 0 32V16Z"
                                                               fill="#FFC107"
                                                               fill-opacity="0.25" />
                                                            <path
                                                               opacity="0.2"
                                                               d="M49.75 26L47.85 36.4469C47.7453 37.0229 47.4418 37.544 46.9924 37.9192C46.543 38.2944 45.9761 38.4999 45.3906 38.5H25.7734L23.5 26H49.75Z"
                                                               fill="#8B3DFF" />
                                                            <path
                                                               d="M50.7094 25.1984C50.592 25.058 50.4453 24.9451 50.2795 24.8676C50.1137 24.7901 49.933 24.75 49.75 24.75H24.5437L23.7797 20.5531C23.675 19.9771 23.3715 19.456 22.9221 19.0808C22.4726 18.7056 21.9058 18.5001 21.3203 18.5H18.5C18.1685 18.5 17.8505 18.6317 17.6161 18.8661C17.3817 19.1005 17.25 19.4185 17.25 19.75C17.25 20.0815 17.3817 20.3995 17.6161 20.6339C17.8505 20.8683 18.1685 21 18.5 21H21.3125L25.3062 42.9203C25.4239 43.5704 25.7112 44.1779 26.1391 44.6812C25.5485 45.2328 25.1223 45.9372 24.9076 46.7163C24.6929 47.4953 24.6982 48.3186 24.9229 49.0948C25.1475 49.871 25.5828 50.5699 26.1804 51.1139C26.7779 51.6578 27.5145 52.0256 28.3084 52.1766C29.1022 52.3275 29.9224 52.2555 30.6779 51.9688C31.4334 51.682 32.0947 51.1916 32.5885 50.552C33.0823 49.9123 33.3893 49.1484 33.4755 48.3449C33.5616 47.5414 33.4235 46.7298 33.0766 46H40.1734C39.8938 46.5854 39.7491 47.2262 39.75 47.875C39.75 48.7403 40.0066 49.5862 40.4873 50.3056C40.9681 51.0251 41.6513 51.5858 42.4508 51.917C43.2502 52.2481 44.1298 52.3347 44.9785 52.1659C45.8272 51.9971 46.6067 51.5804 47.2186 50.9686C47.8304 50.3567 48.2471 49.5772 48.4159 48.7285C48.5847 47.8799 48.4981 47.0002 48.167 46.2008C47.8358 45.4013 47.2751 44.7181 46.5556 44.2373C45.8362 43.7566 44.9903 43.5 44.125 43.5H28.9953C28.7026 43.5 28.4191 43.3972 28.1944 43.2096C27.9697 43.022 27.818 42.7615 27.7656 42.4734L27.2703 39.75H45.3953C46.2735 39.7499 47.1238 39.4415 47.798 38.8787C48.4721 38.3159 48.9274 37.5344 49.0844 36.6703L50.9844 26.2234C51.0165 26.0429 51.0086 25.8574 50.961 25.6803C50.9135 25.5032 50.8276 25.3387 50.7094 25.1984ZM31 47.875C31 48.2458 30.89 48.6084 30.684 48.9167C30.478 49.225 30.1851 49.4654 29.8425 49.6073C29.4999 49.7492 29.1229 49.7863 28.7592 49.714C28.3955 49.6416 28.0614 49.463 27.7992 49.2008C27.537 48.9386 27.3584 48.6045 27.286 48.2408C27.2137 47.8771 27.2508 47.5001 27.3927 47.1575C27.5346 46.8149 27.775 46.522 28.0833 46.316C28.3916 46.11 28.7542 46 29.125 46C29.6223 46 30.0992 46.1975 30.4508 46.5492C30.8025 46.9008 31 47.3777 31 47.875ZM46 47.875C46 48.2458 45.89 48.6084 45.684 48.9167C45.478 49.225 45.1851 49.4654 44.8425 49.6073C44.4999 49.7492 44.1229 49.7863 43.7592 49.714C43.3955 49.6416 43.0614 49.463 42.7992 49.2008C42.5369 48.9386 42.3584 48.6045 42.286 48.2408C42.2137 47.8771 42.2508 47.5001 42.3927 47.1575C42.5346 46.8149 42.775 46.522 43.0833 46.316C43.3916 46.11 43.7542 46 44.125 46C44.6223 46 45.0992 46.1975 45.4508 46.5492C45.8025 46.9008 46 47.3777 46 47.875ZM46.625 36.2234C46.5725 36.5122 46.4201 36.7734 46.1944 36.9611C45.9687 37.1488 45.6842 37.2511 45.3906 37.25H26.8156L24.9984 27.25H48.2516L46.625 36.2234Z"
                                                               fill="#8B3DFF" />
                                                         </svg>
                                                      </div>
                                                      <h3 class="fs-4">E-commerce SEO</h3>
                                                      <p class="mb-0">Our on-page SEO services optimize your website's content and HTML source code.</p>
                                                      <div class="border-top mt-4">
                                                         <a href="#" class="text-reset mt-3 d-inline-flex gap-2 align-items-center icon-link icon-link-hover">
                                                            E-commerce SEO

                                                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-arrow-right" viewBox="0 0 16 16">
                                                               <path
                                                                  fill-rule="evenodd"
                                                                  d="M1 8a.5.5 0 0 1 .5-.5h11.793l-3.147-3.146a.5.5 0 0 1 .708-.708l4 4a.5.5 0 0 1 0 .708l-4 4a.5.5 0 0 1-.708-.708L13.293 8.5H1.5A.5.5 0 0 1 1 8" />
                                                            </svg>
                                                         </a>
                                                      </div>
                                                   </div>
                                                </div>
                                             </div>
                                             <div class="col-lg-4 col-md-6 col-12">
                                                <div class="card shadow-sm card-lift h-100">
                                                   <div class="card-body">
                                                      <div class="mb-4">
                                                         <svg width="48" height="48" viewBox="0 0 56 56" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                            <path
                                                               d="M0 16C0 7.16344 7.16344 0 16 0H32C40.8366 0 48 7.16344 48 16V32C48 40.8366 40.8366 48 32 48H16C7.16344 48 0 40.8366 0 32V16Z"
                                                               fill="#FFC107"
                                                               fill-opacity="0.25" />
                                                            <path
                                                               opacity="0.2"
                                                               d="M50 18.5V47H14V17H48.5C48.8978 17 49.2794 17.158 49.5607 17.4393C49.842 17.7206 50 18.1022 50 18.5Z"
                                                               fill="#8B3DFF" />
                                                            <path
                                                               d="M51.5 47C51.5 47.3978 51.342 47.7794 51.0607 48.0607C50.7794 48.342 50.3978 48.5 50 48.5H14C13.6022 48.5 13.2206 48.342 12.9393 48.0607C12.658 47.7794 12.5 47.3978 12.5 47V17C12.5 16.6022 12.658 16.2206 12.9393 15.9393C13.2206 15.658 13.6022 15.5 14 15.5C14.3978 15.5 14.7794 15.658 15.0607 15.9393C15.342 16.2206 15.5 16.6022 15.5 17V37.3794L24.9387 27.9387C25.0781 27.7993 25.2435 27.6886 25.4256 27.6132C25.6077 27.5377 25.8029 27.4988 26 27.4988C26.1971 27.4988 26.3923 27.5377 26.5744 27.6132C26.7565 27.6886 26.9219 27.7993 27.0613 27.9387L32 32.8794L41.8794 23H38C37.6022 23 37.2206 22.842 36.9393 22.5607C36.658 22.2794 36.5 21.8978 36.5 21.5C36.5 21.1022 36.658 20.7206 36.9393 20.4393C37.2206 20.158 37.6022 20 38 20H45.5C45.8978 20 46.2794 20.158 46.5607 20.4393C46.842 20.7206 47 21.1022 47 21.5V29C47 29.3978 46.842 29.7794 46.5607 30.0607C46.2794 30.342 45.8978 30.5 45.5 30.5C45.1022 30.5 44.7206 30.342 44.4393 30.0607C44.158 29.7794 44 29.3978 44 29V25.1206L33.0613 36.0613C32.9219 36.2007 32.7565 36.3114 32.5744 36.3868C32.3923 36.4623 32.1971 36.5012 32 36.5012C31.8029 36.5012 31.6077 36.4623 31.4256 36.3868C31.2435 36.3114 31.0781 36.2007 30.9387 36.0613L26 31.1206L15.5 41.6206V45.5H50C50.3978 45.5 50.7794 45.658 51.0607 45.9393C51.342 46.2206 51.5 46.6022 51.5 47Z"
                                                               fill="#8B3DFF" />
                                                         </svg>
                                                      </div>
                                                      <h3 class="fs-4 text-truncate">SEO Analytics & Reporting</h3>
                                                      <p class="mb-0">We build a robust backlink profile through ethical & effective link-build strategies.</p>
                                                      <div class="border-top mt-4">
                                                         <a href="#" class="text-reset mt-3 d-inline-flex gap-2 align-items-center icon-link icon-link-hover">
                                                            SEO Analytics Service

                                                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-arrow-right" viewBox="0 0 16 16">
                                                               <path
                                                                  fill-rule="evenodd"
                                                                  d="M1 8a.5.5 0 0 1 .5-.5h11.793l-3.147-3.146a.5.5 0 0 1 .708-.708l4 4a.5.5 0 0 1 0 .708l-4 4a.5.5 0 0 1-.708-.708L13.293 8.5H1.5A.5.5 0 0 1 1 8" />
                                                            </svg>
                                                         </a>
                                                      </div>
                                                   </div>
                                                </div>
                                             </div>
                                          </div>
                                       </div>
                                    </div>
                                 </div>
                              </section>
                           </div>
                           <div class="tab-pane tab-example-code fade" id="pills-award-one-code" role="tabpanel" aria-labelledby="pills-award-one-code-tab">
                              <pre
                                 class="language-markup"
                                 tabindex="0"><code class="language-markup">    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>section</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>py-5<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                                       <span class="token comment">&lt;!--Logo section start--&gt;</span>
                                       <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>container<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                                          <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>row mb-6<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                                             <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>col-xl-5<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                                                <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span><span class="token punctuation">&gt;</span></span>
                                                   <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>h2</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>mb-0<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Maximize your online visibility with expert SEO services<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>h2</span><span class="token punctuation">&gt;</span></span>
                                                <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
                                             <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
                                             <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>offset-xl-1 col-xl-6<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                                                <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span><span class="token punctuation">&gt;</span></span>
                                                   <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>p</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>mb-0<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                                                      At
                                                      <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>span</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>text-dark fw-semibold<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>[Agency Name]<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>span</span><span class="token punctuation">&gt;</span></span>
                                                      , we specialize in delivering comprehensive SEO solutions designed to enhance your online presence, drive targeted traffic, and boost your
                                                      business growth.
                                                   <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>p</span><span class="token punctuation">&gt;</span></span>
                                                <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
                                             <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
                                          <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
                                          <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>row gy-4<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                                             <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>col-xl-3<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                                                <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>card border-0 h-100<span class="token punctuation">"</span></span> <span class="token attr-name">data-bs-theme</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>dark<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                                                   <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>img</span> <span class="token attr-name">src</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>../assets/images/seo/seo-img.jpg<span class="token punctuation">"</span></span> <span class="token attr-name">alt</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span><span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>rounded-3 img-fluid w-100 object-fit-cover horizontal-img<span class="token punctuation">"</span></span> <span class="token punctuation">/&gt;</span></span>
                                                   <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>card-img-overlay<span class="token punctuation">"</span></span> <span class="token attr-name">style</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>background-color: rgb(30, 41, 59, 0.75)<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                                                      <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>position-absolute bottom-0 start-0 end-0 p-4<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                                                         <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>h3</span><span class="token punctuation">&gt;</span></span>SEO Services<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>h3</span><span class="token punctuation">&gt;</span></span>
                                                         <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>p</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>text-white-stick<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Our SEO services are tailored to meet the unique needs of your business,.<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>p</span><span class="token punctuation">&gt;</span></span>
                                                         <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>a</span> <span class="token attr-name">href</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>#<span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>btn btn-primary<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>All SEO services<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>a</span><span class="token punctuation">&gt;</span></span>
                                                      <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
                                                   <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
                                                <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
                                             <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
                                             <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>col-xl-9<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                                                <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>row gx-4 gy-4<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                                                   <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>col-lg-4 col-md-6 col-12<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                                                      <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>card shadow-sm card-lift h-100<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                                                         <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>card-body<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                                                            <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>mb-4<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                                                               <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>svg</span> <span class="token attr-name">width</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>48<span class="token punctuation">"</span></span> <span class="token attr-name">height</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>48<span class="token punctuation">"</span></span> <span class="token attr-name">viewBox</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>0 0 56 56<span class="token punctuation">"</span></span> <span class="token attr-name">fill</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>none<span class="token punctuation">"</span></span> <span class="token attr-name">xmlns</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>http://www.w3.org/2000/svg<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                                                                  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>path</span>
                                                                     <span class="token attr-name">d</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>M0 16C0 7.16344 7.16344 0 16 0H32C40.8366 0 48 7.16344 48 16V32C48 40.8366 40.8366 48 32 48H16C7.16344 48 0 40.8366 0 32V16Z<span class="token punctuation">"</span></span>
                                                                     <span class="token attr-name">fill</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>#FFC107<span class="token punctuation">"</span></span>
                                                                     <span class="token attr-name">fill-opacity</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>0.25<span class="token punctuation">"</span></span> <span class="token punctuation">/&gt;</span></span>
                                                                  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>path</span>
                                                                     <span class="token attr-name">opacity</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>0.2<span class="token punctuation">"</span></span>
                                                                     <span class="token attr-name">d</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>M44 29C44 31.9667 43.1203 34.8668 41.4721 37.3336C39.8238 39.8003 37.4812 41.7229 34.7403 42.8582C31.9994 43.9935 28.9834 44.2906 26.0737 43.7118C23.1639 43.133 20.4912 41.7044 18.3934 39.6066C16.2956 37.5088 14.867 34.8361 14.2882 31.9264C13.7094 29.0166 14.0065 26.0006 15.1418 23.2597C16.2771 20.5189 18.1997 18.1762 20.6665 16.528C23.1332 14.8797 26.0333 14 29 14C30.9698 14 32.9204 14.388 34.7403 15.1418C36.5601 15.8956 38.2137 17.0005 39.6066 18.3934C40.9995 19.7863 42.1044 21.4399 42.8582 23.2597C43.612 25.0796 44 27.0302 44 29Z<span class="token punctuation">"</span></span>
                                                                     <span class="token attr-name">fill</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>#8B3DFF<span class="token punctuation">"</span></span> <span class="token punctuation">/&gt;</span></span>
                                                                  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>path</span>
                                                                     <span class="token attr-name">d</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>M51.0612 48.9392L41.675 39.553C44.4009 36.2838 45.7614 32.0895 45.4735 27.8427C45.1857 23.596 43.2716 19.6237 40.1295 16.7522C36.9874 13.8807 32.8592 12.3312 28.6038 12.426C24.3483 12.5207 20.2932 14.2525 17.2821 17.261C14.2709 20.2695 12.5356 24.323 12.4371 28.5784C12.3385 32.8338 13.8844 36.9633 16.7531 40.108C19.6218 43.2526 23.5924 45.1702 27.8389 45.4618C32.0854 45.7534 36.2809 44.3966 39.5525 41.6736L48.9387 51.0617C49.0781 51.2011 49.2436 51.3116 49.4257 51.3871C49.6077 51.4625 49.8029 51.5013 50 51.5013C50.1971 51.5013 50.3923 51.4625 50.5743 51.3871C50.7564 51.3116 50.9219 51.2011 51.0612 51.0617C51.2006 50.9224 51.3112 50.7569 51.3866 50.5748C51.462 50.3927 51.5008 50.1976 51.5008 50.0005C51.5008 49.8034 51.462 49.6082 51.3866 49.4261C51.3112 49.244 51.2006 49.0786 51.0612 48.9392ZM15.5 29.0005C15.5 26.3304 16.2918 23.7203 17.7752 21.5003C19.2586 19.2802 21.367 17.5499 23.8338 16.5281C26.3006 15.5063 29.015 15.239 31.6337 15.7599C34.2525 16.2808 36.6579 17.5665 38.5459 19.4545C40.4339 21.3426 41.7197 23.748 42.2406 26.3668C42.7615 28.9855 42.4942 31.6999 41.4724 34.1667C40.4506 36.6335 38.7203 38.7419 36.5002 40.2253C34.2801 41.7087 31.67 42.5005 29 42.5005C25.4208 42.4965 21.9893 41.0729 19.4584 38.542C16.9276 36.0112 15.504 32.5797 15.5 29.0005Z<span class="token punctuation">"</span></span>
                                                                     <span class="token attr-name">fill</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>#8B3DFF<span class="token punctuation">"</span></span> <span class="token punctuation">/&gt;</span></span>
                                                               <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>svg</span><span class="token punctuation">&gt;</span></span>
                                                            <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
                                                            <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>h3</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>fs-4<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Keyword Research<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>h3</span><span class="token punctuation">&gt;</span></span>
                                                            <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>p</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>mb-0<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>We conduct thorough keyword research to identify the high-traffic keywords for your industry.<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>p</span><span class="token punctuation">&gt;</span></span>
                                                            <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>border-top mt-4<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                                                               <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>a</span> <span class="token attr-name">href</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>#<span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>text-reset mt-3 d-inline-flex gap-2 align-items-center icon-link icon-link-hover<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                                                                  Explore Keyword Research

                                                                  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>svg</span> <span class="token attr-name">xmlns</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>http://www.w3.org/2000/svg<span class="token punctuation">"</span></span> <span class="token attr-name">width</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>16<span class="token punctuation">"</span></span> <span class="token attr-name">height</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>16<span class="token punctuation">"</span></span> <span class="token attr-name">fill</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>currentColor<span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>bi bi-arrow-right<span class="token punctuation">"</span></span> <span class="token attr-name">viewBox</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>0 0 16 16<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                                                                     <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>path</span>
                                                                        <span class="token attr-name">fill-rule</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>evenodd<span class="token punctuation">"</span></span>
                                                                        <span class="token attr-name">d</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>M1 8a.5.5 0 0 1 .5-.5h11.793l-3.147-3.146a.5.5 0 0 1 .708-.708l4 4a.5.5 0 0 1 0 .708l-4 4a.5.5 0 0 1-.708-.708L13.293 8.5H1.5A.5.5 0 0 1 1 8<span class="token punctuation">"</span></span> <span class="token punctuation">/&gt;</span></span>
                                                                  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>svg</span><span class="token punctuation">&gt;</span></span>
                                                               <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>a</span><span class="token punctuation">&gt;</span></span>
                                                            <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
                                                         <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
                                                      <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
                                                   <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
                                                   <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>col-lg-4 col-md-6 col-12<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                                                      <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>card shadow-sm card-lift h-100<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                                                         <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>card-body<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                                                            <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>mb-4<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                                                               <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>svg</span> <span class="token attr-name">width</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>48<span class="token punctuation">"</span></span> <span class="token attr-name">height</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>48<span class="token punctuation">"</span></span> <span class="token attr-name">viewBox</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>0 0 56 56<span class="token punctuation">"</span></span> <span class="token attr-name">fill</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>none<span class="token punctuation">"</span></span> <span class="token attr-name">xmlns</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>http://www.w3.org/2000/svg<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                                                                  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>path</span>
                                                                     <span class="token attr-name">d</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>M0 16C0 7.16344 7.16344 0 16 0H32C40.8366 0 48 7.16344 48 16V32C48 40.8366 40.8366 48 32 48H16C7.16344 48 0 40.8366 0 32V16Z<span class="token punctuation">"</span></span>
                                                                     <span class="token attr-name">fill</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>#FFC107<span class="token punctuation">"</span></span>
                                                                     <span class="token attr-name">fill-opacity</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>0.25<span class="token punctuation">"</span></span> <span class="token punctuation">/&gt;</span></span>
                                                                  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>path</span>
                                                                     <span class="token attr-name">opacity</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>0.2<span class="token punctuation">"</span></span>
                                                                     <span class="token attr-name">d</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>M48.5 34.125V39.75C48.5 41.3915 48.1767 43.017 47.5485 44.5335C46.9203 46.0501 45.9996 47.4281 44.8389 48.5888C43.6781 49.7496 42.3001 50.6703 40.7836 51.2985C39.267 51.9267 37.6415 52.25 36 52.25C29.0969 52.25 27.2797 48.5 21.4188 38.1875C21.0109 37.4701 20.9033 36.6206 21.1196 35.8242C21.3358 35.0278 21.8582 34.3493 22.5728 33.9366C23.2875 33.524 24.1363 33.4108 24.9341 33.6218C25.7319 33.8327 26.4139 34.3506 26.8313 35.0625L29.75 39.75V22.875C29.75 22.0462 30.0793 21.2513 30.6653 20.6653C31.2514 20.0792 32.0462 19.75 32.875 19.75C33.7038 19.75 34.4987 20.0792 35.0847 20.6653C35.6708 21.2513 36 22.0462 36 22.875V31.625C36 30.7962 36.3293 30.0013 36.9153 29.4153C37.5014 28.8292 38.2962 28.5 39.125 28.5C39.9538 28.5 40.7487 28.8292 41.3347 29.4153C41.9208 30.0013 42.25 30.7962 42.25 31.625V34.125C42.25 33.2962 42.5793 32.5013 43.1653 31.9153C43.7514 31.3292 44.5462 31 45.375 31C46.2038 31 46.9987 31.3292 47.5847 31.9153C48.1708 32.5013 48.5 33.2962 48.5 34.125Z<span class="token punctuation">"</span></span>
                                                                     <span class="token attr-name">fill</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>#8B3DFF<span class="token punctuation">"</span></span> <span class="token punctuation">/&gt;</span></span>
                                                                  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>path</span>
                                                                     <span class="token attr-name">d</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>M45.375 29.75C44.6463 29.7492 43.9291 29.9313 43.2891 30.2797C43.0977 29.6871 42.782 29.1423 42.3632 28.6815C41.9443 28.2207 41.4319 27.8547 40.8603 27.6078C40.2886 27.361 39.6708 27.239 39.0482 27.25C38.4256 27.2611 37.8126 27.4049 37.25 27.6719V22.875C37.25 21.7147 36.7891 20.6019 35.9686 19.7814C35.1481 18.9609 34.0353 18.5 32.875 18.5C31.7147 18.5 30.6019 18.9609 29.7814 19.7814C28.9609 20.6019 28.5 21.7147 28.5 22.875V35.375L27.9031 34.4172C27.6145 33.9201 27.2309 33.4848 26.774 33.136C26.3171 32.7872 25.7961 32.5318 25.2405 32.3844C24.1185 32.0866 22.9242 32.2468 21.9203 32.8297C20.9164 33.4125 20.1852 34.3703 19.8875 35.4923C19.5898 36.6143 19.75 37.8086 20.3328 38.8125L21.0625 40.0984C26.4391 49.5766 28.6641 53.5 36 53.5C39.6455 53.4959 43.1404 52.0459 45.7182 49.4682C48.2959 46.8904 49.7459 43.3955 49.75 39.75V34.125C49.75 32.9647 49.2891 31.8519 48.4686 31.0314C47.6481 30.2109 46.5353 29.75 45.375 29.75ZM47.25 39.75C47.2467 42.7327 46.0604 45.5922 43.9513 47.7013C41.8422 49.8104 38.9827 50.9967 36 51C30.1203 51 28.525 48.1875 23.2375 38.8625L22.5047 37.5703V37.5625C22.2559 37.1323 22.188 36.6209 22.3161 36.1407C22.4441 35.6605 22.7575 35.2508 23.1875 35.0016C23.4723 34.8366 23.7959 34.7503 24.125 34.7516C24.4546 34.7511 24.7785 34.8375 25.0641 35.0021C25.3496 35.1667 25.5868 35.4036 25.7516 35.6891C25.7579 35.7016 25.7653 35.7136 25.7735 35.725L28.6906 40.4125C28.8346 40.6424 29.0494 40.8192 29.3027 40.9162C29.556 41.0133 29.834 41.0253 30.0947 40.9505C30.3554 40.8757 30.5847 40.7182 30.7481 40.5016C30.9114 40.285 30.9998 40.0212 31 39.75V22.875C31 22.3777 31.1976 21.9008 31.5492 21.5492C31.9008 21.1975 32.3777 21 32.875 21C33.3723 21 33.8492 21.1975 34.2008 21.5492C34.5525 21.9008 34.75 22.3777 34.75 22.875V33.5C34.75 33.8315 34.8817 34.1495 35.1161 34.3839C35.3505 34.6183 35.6685 34.75 36 34.75C36.3315 34.75 36.6495 34.6183 36.8839 34.3839C37.1183 34.1495 37.25 33.8315 37.25 33.5V31.625C37.25 31.1277 37.4476 30.6508 37.7992 30.2992C38.1508 29.9475 38.6277 29.75 39.125 29.75C39.6223 29.75 40.0992 29.9475 40.4508 30.2992C40.8025 30.6508 41 31.1277 41 31.625V34.75C41 35.0815 41.1317 35.3995 41.3661 35.6339C41.6005 35.8683 41.9185 36 42.25 36C42.5815 36 42.8995 35.8683 43.1339 35.6339C43.3683 35.3995 43.5 35.0815 43.5 34.75V34.125C43.5 33.6277 43.6976 33.1508 44.0492 32.7992C44.4008 32.4475 44.8777 32.25 45.375 32.25C45.8723 32.25 46.3492 32.4475 46.7008 32.7992C47.0525 33.1508 47.25 33.6277 47.25 34.125V39.75Z<span class="token punctuation">"</span></span>
                                                                     <span class="token attr-name">fill</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>#8B3DFF<span class="token punctuation">"</span></span> <span class="token punctuation">/&gt;</span></span>
                                                               <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>svg</span><span class="token punctuation">&gt;</span></span>
                                                            <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
                                                            <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>h3</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>fs-4<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>On-Page SEO<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>h3</span><span class="token punctuation">&gt;</span></span>
                                                            <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>p</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>mb-0<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Our on-page SEO services optimize your website's content and HTML source code.<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>p</span><span class="token punctuation">&gt;</span></span>
                                                            <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>border-top mt-4<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                                                               <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>a</span> <span class="token attr-name">href</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>#<span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>text-reset mt-3 d-inline-flex gap-2 align-items-center icon-link icon-link-hover<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                                                                  Explore Social Media

                                                                  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>svg</span> <span class="token attr-name">xmlns</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>http://www.w3.org/2000/svg<span class="token punctuation">"</span></span> <span class="token attr-name">width</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>16<span class="token punctuation">"</span></span> <span class="token attr-name">height</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>16<span class="token punctuation">"</span></span> <span class="token attr-name">fill</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>currentColor<span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>bi bi-arrow-right<span class="token punctuation">"</span></span> <span class="token attr-name">viewBox</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>0 0 16 16<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                                                                     <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>path</span>
                                                                        <span class="token attr-name">fill-rule</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>evenodd<span class="token punctuation">"</span></span>
                                                                        <span class="token attr-name">d</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>M1 8a.5.5 0 0 1 .5-.5h11.793l-3.147-3.146a.5.5 0 0 1 .708-.708l4 4a.5.5 0 0 1 0 .708l-4 4a.5.5 0 0 1-.708-.708L13.293 8.5H1.5A.5.5 0 0 1 1 8<span class="token punctuation">"</span></span> <span class="token punctuation">/&gt;</span></span>
                                                                  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>svg</span><span class="token punctuation">&gt;</span></span>
                                                               <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>a</span><span class="token punctuation">&gt;</span></span>
                                                            <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
                                                         <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
                                                      <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
                                                   <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
                                                   <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>col-lg-4 col-md-6 col-12<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                                                      <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>card shadow-sm card-lift h-100<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                                                         <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>card-body<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                                                            <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>mb-4<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                                                               <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>svg</span> <span class="token attr-name">width</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>48<span class="token punctuation">"</span></span> <span class="token attr-name">height</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>48<span class="token punctuation">"</span></span> <span class="token attr-name">viewBox</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>0 0 56 56<span class="token punctuation">"</span></span> <span class="token attr-name">fill</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>none<span class="token punctuation">"</span></span> <span class="token attr-name">xmlns</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>http://www.w3.org/2000/svg<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                                                                  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>path</span>
                                                                     <span class="token attr-name">d</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>M0 16C0 7.16344 7.16344 0 16 0H32C40.8366 0 48 7.16344 48 16V32C48 40.8366 40.8366 48 32 48H16C7.16344 48 0 40.8366 0 32V16Z<span class="token punctuation">"</span></span>
                                                                     <span class="token attr-name">fill</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>#FFC107<span class="token punctuation">"</span></span>
                                                                     <span class="token attr-name">fill-opacity</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>0.25<span class="token punctuation">"</span></span> <span class="token punctuation">/&gt;</span></span>
                                                                  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>path</span>
                                                                     <span class="token attr-name">opacity</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>0.2<span class="token punctuation">"</span></span>
                                                                     <span class="token attr-name">d</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>M48.8031 33.8029L33.8031 48.8029C32.3928 50.1905 30.4912 50.9646 28.5127 50.9565C26.5341 50.9485 24.639 50.1589 23.2399 48.7599C21.8409 47.3608 21.0513 45.4657 21.0433 43.4871C21.0352 41.5086 21.8093 39.607 23.1969 38.1967L38.1969 23.1967C39.6073 21.8091 41.5088 21.035 43.4874 21.043C45.4659 21.0511 47.3611 21.8406 48.7601 23.2397C50.1592 24.6387 50.9487 26.5339 50.9568 28.5124C50.9648 30.491 50.1907 32.3925 48.8031 33.8029Z<span class="token punctuation">"</span></span>
                                                                     <span class="token attr-name">fill</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>#8B3DFF<span class="token punctuation">"</span></span> <span class="token punctuation">/&gt;</span></span>
                                                                  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>path</span>
                                                                     <span class="token attr-name">d</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>M41.8843 30.1153C42.0006 30.2314 42.0928 30.3692 42.1557 30.521C42.2186 30.6727 42.2509 30.8354 42.2509 30.9997C42.2509 31.1639 42.2186 31.3266 42.1557 31.4783C42.0928 31.6301 42.0006 31.7679 41.8843 31.884L31.8843 41.884C31.7682 42.0002 31.6303 42.0923 31.4786 42.1551C31.3268 42.218 31.1642 42.2503 31 42.2503C30.8357 42.2503 30.6731 42.218 30.5213 42.1551C30.3696 42.0923 30.2317 42.0002 30.1156 41.884C29.9994 41.7679 29.9073 41.63 29.8445 41.4783C29.7816 41.3265 29.7493 41.1639 29.7493 40.9997C29.7493 40.8354 29.7816 40.6728 29.8445 40.521C29.9073 40.3693 29.9994 40.2314 30.1156 40.1153L40.1156 30.1153C40.2317 29.9991 40.3695 29.9069 40.5213 29.844C40.673 29.781 40.8357 29.7487 41 29.7487C41.1642 29.7487 41.3269 29.781 41.4786 29.844C41.6304 29.9069 41.7682 29.9991 41.8843 30.1153ZM49.6875 22.3122C48.8749 21.4995 47.9103 20.8549 46.8486 20.4152C45.787 19.9754 44.6491 19.749 43.5 19.749C42.3508 19.749 41.2129 19.9754 40.1513 20.4152C39.0896 20.8549 38.125 21.4995 37.3125 22.3122L32.6156 27.0075C32.381 27.242 32.2493 27.5601 32.2493 27.8918C32.2493 28.2235 32.381 28.5417 32.6156 28.7762C32.8501 29.0108 33.1683 29.1425 33.5 29.1425C33.8317 29.1425 34.1498 29.0108 34.3843 28.7762L39.0812 24.0887C40.2576 22.9382 41.8402 22.298 43.4857 22.307C45.1312 22.3161 46.7067 22.9737 47.8703 24.1371C49.0339 25.3006 49.6918 26.8759 49.7012 28.5214C49.7105 30.1669 49.0706 31.7496 47.9203 32.9262L43.2218 37.6231C42.9873 37.8574 42.8554 38.1754 42.8553 38.5069C42.8551 38.8385 42.9867 39.1565 43.221 39.3911C43.4554 39.6256 43.7733 39.7575 44.1049 39.7576C44.4364 39.7578 44.7545 39.6262 44.989 39.3918L49.6875 34.6872C50.5001 33.8746 51.1447 32.91 51.5844 31.8483C52.0242 30.7867 52.2506 29.6488 52.2506 28.4997C52.2506 27.3505 52.0242 26.2126 51.5844 25.151C51.1447 24.0893 50.5001 23.1247 49.6875 22.3122ZM37.6156 43.2215L32.9187 47.9184C32.3407 48.5094 31.6512 48.9798 30.8901 49.3025C30.1291 49.6252 29.3116 49.7937 28.4849 49.7982C27.6583 49.8028 26.839 49.6433 26.0744 49.329C25.3098 49.0148 24.6152 48.5519 24.0307 47.9673C23.4462 47.3828 22.9835 46.6881 22.6694 45.9234C22.3552 45.1588 22.1959 44.3394 22.2006 43.5128C22.2053 42.6862 22.3739 41.8687 22.6967 41.1077C23.0196 40.3467 23.4901 39.6572 24.0812 39.0793L28.7765 34.384C29.0111 34.1495 29.1428 33.8314 29.1428 33.4997C29.1428 33.1679 29.0111 32.8498 28.7765 32.6153C28.542 32.3807 28.2238 32.249 27.8921 32.249C27.5604 32.249 27.2423 32.3807 27.0078 32.6153L22.3125 37.3122C20.6714 38.9532 19.7495 41.1789 19.7495 43.4997C19.7495 45.8204 20.6714 48.0461 22.3125 49.6872C23.9535 51.3282 26.1792 52.2501 28.5 52.2501C30.8207 52.2501 33.0464 51.3282 34.6875 49.6872L39.3843 44.9887C39.6187 44.7542 39.7502 44.4361 39.7501 44.1046C39.75 43.773 39.6181 43.4551 39.3835 43.2207C39.149 42.9864 38.831 42.8548 38.4994 42.855C38.1678 42.8551 37.8499 42.987 37.6156 43.2215Z<span class="token punctuation">"</span></span>
                                                                     <span class="token attr-name">fill</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>#8B3DFF<span class="token punctuation">"</span></span> <span class="token punctuation">/&gt;</span></span>
                                                               <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>svg</span><span class="token punctuation">&gt;</span></span>
                                                            <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
                                                            <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>h3</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>fs-4<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Link Building<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>h3</span><span class="token punctuation">&gt;</span></span>
                                                            <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>p</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>mb-0<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>We build a robust backlink profile through ethical and effective link-building strategies.<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>p</span><span class="token punctuation">&gt;</span></span>
                                                            <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>border-top mt-4<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                                                               <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>a</span> <span class="token attr-name">href</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>#<span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>text-reset mt-3 d-inline-flex gap-2 align-items-center icon-link icon-link-hover<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                                                                  Link Building Services

                                                                  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>svg</span> <span class="token attr-name">xmlns</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>http://www.w3.org/2000/svg<span class="token punctuation">"</span></span> <span class="token attr-name">width</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>16<span class="token punctuation">"</span></span> <span class="token attr-name">height</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>16<span class="token punctuation">"</span></span> <span class="token attr-name">fill</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>currentColor<span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>bi bi-arrow-right<span class="token punctuation">"</span></span> <span class="token attr-name">viewBox</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>0 0 16 16<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                                                                     <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>path</span>
                                                                        <span class="token attr-name">fill-rule</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>evenodd<span class="token punctuation">"</span></span>
                                                                        <span class="token attr-name">d</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>M1 8a.5.5 0 0 1 .5-.5h11.793l-3.147-3.146a.5.5 0 0 1 .708-.708l4 4a.5.5 0 0 1 0 .708l-4 4a.5.5 0 0 1-.708-.708L13.293 8.5H1.5A.5.5 0 0 1 1 8<span class="token punctuation">"</span></span> <span class="token punctuation">/&gt;</span></span>
                                                                  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>svg</span><span class="token punctuation">&gt;</span></span>
                                                               <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>a</span><span class="token punctuation">&gt;</span></span>
                                                            <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
                                                         <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
                                                      <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
                                                   <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
                                                   <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>col-lg-4 col-md-6 col-12<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                                                      <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>card shadow-sm card-lift h-100<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                                                         <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>card-body<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                                                            <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>mb-4<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                                                               <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>svg</span> <span class="token attr-name">width</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>48<span class="token punctuation">"</span></span> <span class="token attr-name">height</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>48<span class="token punctuation">"</span></span> <span class="token attr-name">viewBox</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>0 0 56 56<span class="token punctuation">"</span></span> <span class="token attr-name">fill</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>none<span class="token punctuation">"</span></span> <span class="token attr-name">xmlns</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>http://www.w3.org/2000/svg<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                                                                  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>path</span>
                                                                     <span class="token attr-name">d</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>M0 16C0 7.16344 7.16344 0 16 0H32C40.8366 0 48 7.16344 48 16V32C48 40.8366 40.8366 48 32 48H16C7.16344 48 0 40.8366 0 32V16Z<span class="token punctuation">"</span></span>
                                                                     <span class="token attr-name">fill</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>#FFC107<span class="token punctuation">"</span></span>
                                                                     <span class="token attr-name">fill-opacity</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>0.25<span class="token punctuation">"</span></span> <span class="token punctuation">/&gt;</span></span>
                                                                  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>path</span>
                                                                     <span class="token attr-name">opacity</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>0.2<span class="token punctuation">"</span></span>
                                                                     <span class="token attr-name">d</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>M36 19.75C32.6848 19.75 29.5054 21.067 27.1612 23.4112C24.817 25.7554 23.5 28.9348 23.5 32.25C23.5 43.5 36 52.25 36 52.25C36 52.25 48.5 43.5 48.5 32.25C48.5 28.9348 47.183 25.7554 44.8388 23.4112C42.4946 21.067 39.3152 19.75 36 19.75ZM36 37.25C35.0111 37.25 34.0444 36.9568 33.2221 36.4073C32.3999 35.8579 31.759 35.077 31.3806 34.1634C31.0022 33.2498 30.9031 32.2445 31.0961 31.2745C31.289 30.3046 31.7652 29.4137 32.4645 28.7145C33.1637 28.0152 34.0546 27.539 35.0245 27.3461C35.9945 27.1531 36.9998 27.2522 37.9134 27.6306C38.827 28.009 39.6079 28.6499 40.1573 29.4721C40.7068 30.2944 41 31.2611 41 32.25C41 33.5761 40.4732 34.8479 39.5355 35.7855C38.5979 36.7232 37.3261 37.25 36 37.25Z<span class="token punctuation">"</span></span>
                                                                     <span class="token attr-name">fill</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>#8B3DFF<span class="token punctuation">"</span></span> <span class="token punctuation">/&gt;</span></span>
                                                                  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>path</span>
                                                                     <span class="token attr-name">d</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>M36 26C34.7639 26 33.5555 26.3666 32.5277 27.0533C31.4999 27.7401 30.6988 28.7162 30.2258 29.8582C29.7527 31.0003 29.6289 32.2569 29.8701 33.4693C30.1112 34.6817 30.7065 35.7953 31.5806 36.6694C32.4547 37.5435 33.5683 38.1388 34.7807 38.3799C35.9931 38.6211 37.2497 38.4973 38.3918 38.0242C39.5338 37.5512 40.5099 36.7501 41.1967 35.7223C41.8834 34.6945 42.25 33.4861 42.25 32.25C42.25 30.5924 41.5915 29.0027 40.4194 27.8306C39.2473 26.6585 37.6576 26 36 26ZM36 36C35.2583 36 34.5333 35.7801 33.9166 35.368C33.2999 34.956 32.8193 34.3703 32.5355 33.6851C32.2516 32.9998 32.1774 32.2458 32.3221 31.5184C32.4667 30.791 32.8239 30.1228 33.3483 29.5983C33.8728 29.0739 34.541 28.7167 35.2684 28.5721C35.9958 28.4274 36.7498 28.5016 37.4351 28.7855C38.1203 29.0693 38.706 29.5499 39.118 30.1666C39.5301 30.7833 39.75 31.5083 39.75 32.25C39.75 33.2446 39.3549 34.1984 38.6517 34.9017C37.9484 35.6049 36.9946 36 36 36ZM36 18.5C32.3545 18.5041 28.8596 19.9541 26.2818 22.5318C23.7041 25.1096 22.2541 28.6045 22.25 32.25C22.25 37.1562 24.5172 42.3563 28.8125 47.2891C30.7425 49.5181 32.9148 51.5252 35.2891 53.2734C35.4992 53.4207 35.7496 53.4996 36.0062 53.4996C36.2629 53.4996 36.5133 53.4207 36.7234 53.2734C39.0934 51.5245 41.2614 49.5174 43.1875 47.2891C47.4766 42.3563 49.75 37.1562 49.75 32.25C49.7459 28.6045 48.2959 25.1096 45.7182 22.5318C43.1404 19.9541 39.6455 18.5041 36 18.5ZM36 50.6875C33.4172 48.6562 24.75 41.1953 24.75 32.25C24.75 29.2663 25.9353 26.4048 28.045 24.295C30.1548 22.1853 33.0163 21 36 21C38.9837 21 41.8452 22.1853 43.955 24.295C46.0647 26.4048 47.25 29.2663 47.25 32.25C47.25 41.1922 38.5828 48.6562 36 50.6875Z<span class="token punctuation">"</span></span>
                                                                     <span class="token attr-name">fill</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>#8B3DFF<span class="token punctuation">"</span></span> <span class="token punctuation">/&gt;</span></span>
                                                               <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>svg</span><span class="token punctuation">&gt;</span></span>
                                                            <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
                                                            <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>h3</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>fs-4<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Local SEO<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>h3</span><span class="token punctuation">&gt;</span></span>
                                                            <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>p</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>mb-0<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Our local SEO services focus on optimizing your online presence to attract more business.<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>p</span><span class="token punctuation">&gt;</span></span>
                                                            <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>border-top mt-4<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                                                               <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>a</span> <span class="token attr-name">href</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>#<span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>text-reset mt-3 d-inline-flex gap-2 align-items-center icon-link icon-link-hover<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                                                                  Local SEO Services

                                                                  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>svg</span> <span class="token attr-name">xmlns</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>http://www.w3.org/2000/svg<span class="token punctuation">"</span></span> <span class="token attr-name">width</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>16<span class="token punctuation">"</span></span> <span class="token attr-name">height</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>16<span class="token punctuation">"</span></span> <span class="token attr-name">fill</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>currentColor<span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>bi bi-arrow-right<span class="token punctuation">"</span></span> <span class="token attr-name">viewBox</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>0 0 16 16<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                                                                     <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>path</span>
                                                                        <span class="token attr-name">fill-rule</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>evenodd<span class="token punctuation">"</span></span>
                                                                        <span class="token attr-name">d</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>M1 8a.5.5 0 0 1 .5-.5h11.793l-3.147-3.146a.5.5 0 0 1 .708-.708l4 4a.5.5 0 0 1 0 .708l-4 4a.5.5 0 0 1-.708-.708L13.293 8.5H1.5A.5.5 0 0 1 1 8<span class="token punctuation">"</span></span> <span class="token punctuation">/&gt;</span></span>
                                                                  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>svg</span><span class="token punctuation">&gt;</span></span>
                                                               <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>a</span><span class="token punctuation">&gt;</span></span>
                                                            <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
                                                         <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
                                                      <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
                                                   <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
                                                   <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>col-lg-4 col-md-6 col-12<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                                                      <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>card shadow-sm card-lift h-100<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                                                         <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>card-body<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                                                            <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>mb-4<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                                                               <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>svg</span> <span class="token attr-name">width</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>48<span class="token punctuation">"</span></span> <span class="token attr-name">height</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>48<span class="token punctuation">"</span></span> <span class="token attr-name">viewBox</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>0 0 56 56<span class="token punctuation">"</span></span> <span class="token attr-name">fill</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>none<span class="token punctuation">"</span></span> <span class="token attr-name">xmlns</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>http://www.w3.org/2000/svg<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                                                                  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>path</span>
                                                                     <span class="token attr-name">d</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>M0 16C0 7.16344 7.16344 0 16 0H32C40.8366 0 48 7.16344 48 16V32C48 40.8366 40.8366 48 32 48H16C7.16344 48 0 40.8366 0 32V16Z<span class="token punctuation">"</span></span>
                                                                     <span class="token attr-name">fill</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>#FFC107<span class="token punctuation">"</span></span>
                                                                     <span class="token attr-name">fill-opacity</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>0.25<span class="token punctuation">"</span></span> <span class="token punctuation">/&gt;</span></span>
                                                                  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>path</span>
                                                                     <span class="token attr-name">opacity</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>0.2<span class="token punctuation">"</span></span>
                                                                     <span class="token attr-name">d</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>M49.75 26L47.85 36.4469C47.7453 37.0229 47.4418 37.544 46.9924 37.9192C46.543 38.2944 45.9761 38.4999 45.3906 38.5H25.7734L23.5 26H49.75Z<span class="token punctuation">"</span></span>
                                                                     <span class="token attr-name">fill</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>#8B3DFF<span class="token punctuation">"</span></span> <span class="token punctuation">/&gt;</span></span>
                                                                  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>path</span>
                                                                     <span class="token attr-name">d</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>M50.7094 25.1984C50.592 25.058 50.4453 24.9451 50.2795 24.8676C50.1137 24.7901 49.933 24.75 49.75 24.75H24.5437L23.7797 20.5531C23.675 19.9771 23.3715 19.456 22.9221 19.0808C22.4726 18.7056 21.9058 18.5001 21.3203 18.5H18.5C18.1685 18.5 17.8505 18.6317 17.6161 18.8661C17.3817 19.1005 17.25 19.4185 17.25 19.75C17.25 20.0815 17.3817 20.3995 17.6161 20.6339C17.8505 20.8683 18.1685 21 18.5 21H21.3125L25.3062 42.9203C25.4239 43.5704 25.7112 44.1779 26.1391 44.6812C25.5485 45.2328 25.1223 45.9372 24.9076 46.7163C24.6929 47.4953 24.6982 48.3186 24.9229 49.0948C25.1475 49.871 25.5828 50.5699 26.1804 51.1139C26.7779 51.6578 27.5145 52.0256 28.3084 52.1766C29.1022 52.3275 29.9224 52.2555 30.6779 51.9688C31.4334 51.682 32.0947 51.1916 32.5885 50.552C33.0823 49.9123 33.3893 49.1484 33.4755 48.3449C33.5616 47.5414 33.4235 46.7298 33.0766 46H40.1734C39.8938 46.5854 39.7491 47.2262 39.75 47.875C39.75 48.7403 40.0066 49.5862 40.4873 50.3056C40.9681 51.0251 41.6513 51.5858 42.4508 51.917C43.2502 52.2481 44.1298 52.3347 44.9785 52.1659C45.8272 51.9971 46.6067 51.5804 47.2186 50.9686C47.8304 50.3567 48.2471 49.5772 48.4159 48.7285C48.5847 47.8799 48.4981 47.0002 48.167 46.2008C47.8358 45.4013 47.2751 44.7181 46.5556 44.2373C45.8362 43.7566 44.9903 43.5 44.125 43.5H28.9953C28.7026 43.5 28.4191 43.3972 28.1944 43.2096C27.9697 43.022 27.818 42.7615 27.7656 42.4734L27.2703 39.75H45.3953C46.2735 39.7499 47.1238 39.4415 47.798 38.8787C48.4721 38.3159 48.9274 37.5344 49.0844 36.6703L50.9844 26.2234C51.0165 26.0429 51.0086 25.8574 50.961 25.6803C50.9135 25.5032 50.8276 25.3387 50.7094 25.1984ZM31 47.875C31 48.2458 30.89 48.6084 30.684 48.9167C30.478 49.225 30.1851 49.4654 29.8425 49.6073C29.4999 49.7492 29.1229 49.7863 28.7592 49.714C28.3955 49.6416 28.0614 49.463 27.7992 49.2008C27.537 48.9386 27.3584 48.6045 27.286 48.2408C27.2137 47.8771 27.2508 47.5001 27.3927 47.1575C27.5346 46.8149 27.775 46.522 28.0833 46.316C28.3916 46.11 28.7542 46 29.125 46C29.6223 46 30.0992 46.1975 30.4508 46.5492C30.8025 46.9008 31 47.3777 31 47.875ZM46 47.875C46 48.2458 45.89 48.6084 45.684 48.9167C45.478 49.225 45.1851 49.4654 44.8425 49.6073C44.4999 49.7492 44.1229 49.7863 43.7592 49.714C43.3955 49.6416 43.0614 49.463 42.7992 49.2008C42.5369 48.9386 42.3584 48.6045 42.286 48.2408C42.2137 47.8771 42.2508 47.5001 42.3927 47.1575C42.5346 46.8149 42.775 46.522 43.0833 46.316C43.3916 46.11 43.7542 46 44.125 46C44.6223 46 45.0992 46.1975 45.4508 46.5492C45.8025 46.9008 46 47.3777 46 47.875ZM46.625 36.2234C46.5725 36.5122 46.4201 36.7734 46.1944 36.9611C45.9687 37.1488 45.6842 37.2511 45.3906 37.25H26.8156L24.9984 27.25H48.2516L46.625 36.2234Z<span class="token punctuation">"</span></span>
                                                                     <span class="token attr-name">fill</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>#8B3DFF<span class="token punctuation">"</span></span> <span class="token punctuation">/&gt;</span></span>
                                                               <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>svg</span><span class="token punctuation">&gt;</span></span>
                                                            <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
                                                            <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>h3</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>fs-4<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>E-commerce SEO<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>h3</span><span class="token punctuation">&gt;</span></span>
                                                            <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>p</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>mb-0<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Our on-page SEO services optimize your website's content and HTML source code.<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>p</span><span class="token punctuation">&gt;</span></span>
                                                            <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>border-top mt-4<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                                                               <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>a</span> <span class="token attr-name">href</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>#<span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>text-reset mt-3 d-inline-flex gap-2 align-items-center icon-link icon-link-hover<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                                                                  E-commerce SEO

                                                                  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>svg</span> <span class="token attr-name">xmlns</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>http://www.w3.org/2000/svg<span class="token punctuation">"</span></span> <span class="token attr-name">width</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>16<span class="token punctuation">"</span></span> <span class="token attr-name">height</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>16<span class="token punctuation">"</span></span> <span class="token attr-name">fill</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>currentColor<span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>bi bi-arrow-right<span class="token punctuation">"</span></span> <span class="token attr-name">viewBox</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>0 0 16 16<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                                                                     <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>path</span>
                                                                        <span class="token attr-name">fill-rule</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>evenodd<span class="token punctuation">"</span></span>
                                                                        <span class="token attr-name">d</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>M1 8a.5.5 0 0 1 .5-.5h11.793l-3.147-3.146a.5.5 0 0 1 .708-.708l4 4a.5.5 0 0 1 0 .708l-4 4a.5.5 0 0 1-.708-.708L13.293 8.5H1.5A.5.5 0 0 1 1 8<span class="token punctuation">"</span></span> <span class="token punctuation">/&gt;</span></span>
                                                                  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>svg</span><span class="token punctuation">&gt;</span></span>
                                                               <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>a</span><span class="token punctuation">&gt;</span></span>
                                                            <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
                                                         <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
                                                      <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
                                                   <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
                                                   <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>col-lg-4 col-md-6 col-12<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                                                      <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>card shadow-sm card-lift h-100<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                                                         <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>card-body<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                                                            <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>mb-4<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                                                               <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>svg</span> <span class="token attr-name">width</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>48<span class="token punctuation">"</span></span> <span class="token attr-name">height</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>48<span class="token punctuation">"</span></span> <span class="token attr-name">viewBox</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>0 0 56 56<span class="token punctuation">"</span></span> <span class="token attr-name">fill</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>none<span class="token punctuation">"</span></span> <span class="token attr-name">xmlns</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>http://www.w3.org/2000/svg<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                                                                  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>path</span>
                                                                     <span class="token attr-name">d</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>M0 16C0 7.16344 7.16344 0 16 0H32C40.8366 0 48 7.16344 48 16V32C48 40.8366 40.8366 48 32 48H16C7.16344 48 0 40.8366 0 32V16Z<span class="token punctuation">"</span></span>
                                                                     <span class="token attr-name">fill</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>#FFC107<span class="token punctuation">"</span></span>
                                                                     <span class="token attr-name">fill-opacity</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>0.25<span class="token punctuation">"</span></span> <span class="token punctuation">/&gt;</span></span>
                                                                  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>path</span>
                                                                     <span class="token attr-name">opacity</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>0.2<span class="token punctuation">"</span></span>
                                                                     <span class="token attr-name">d</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>M50 18.5V47H14V17H48.5C48.8978 17 49.2794 17.158 49.5607 17.4393C49.842 17.7206 50 18.1022 50 18.5Z<span class="token punctuation">"</span></span>
                                                                     <span class="token attr-name">fill</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>#8B3DFF<span class="token punctuation">"</span></span> <span class="token punctuation">/&gt;</span></span>
                                                                  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>path</span>
                                                                     <span class="token attr-name">d</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>M51.5 47C51.5 47.3978 51.342 47.7794 51.0607 48.0607C50.7794 48.342 50.3978 48.5 50 48.5H14C13.6022 48.5 13.2206 48.342 12.9393 48.0607C12.658 47.7794 12.5 47.3978 12.5 47V17C12.5 16.6022 12.658 16.2206 12.9393 15.9393C13.2206 15.658 13.6022 15.5 14 15.5C14.3978 15.5 14.7794 15.658 15.0607 15.9393C15.342 16.2206 15.5 16.6022 15.5 17V37.3794L24.9387 27.9387C25.0781 27.7993 25.2435 27.6886 25.4256 27.6132C25.6077 27.5377 25.8029 27.4988 26 27.4988C26.1971 27.4988 26.3923 27.5377 26.5744 27.6132C26.7565 27.6886 26.9219 27.7993 27.0613 27.9387L32 32.8794L41.8794 23H38C37.6022 23 37.2206 22.842 36.9393 22.5607C36.658 22.2794 36.5 21.8978 36.5 21.5C36.5 21.1022 36.658 20.7206 36.9393 20.4393C37.2206 20.158 37.6022 20 38 20H45.5C45.8978 20 46.2794 20.158 46.5607 20.4393C46.842 20.7206 47 21.1022 47 21.5V29C47 29.3978 46.842 29.7794 46.5607 30.0607C46.2794 30.342 45.8978 30.5 45.5 30.5C45.1022 30.5 44.7206 30.342 44.4393 30.0607C44.158 29.7794 44 29.3978 44 29V25.1206L33.0613 36.0613C32.9219 36.2007 32.7565 36.3114 32.5744 36.3868C32.3923 36.4623 32.1971 36.5012 32 36.5012C31.8029 36.5012 31.6077 36.4623 31.4256 36.3868C31.2435 36.3114 31.0781 36.2007 30.9387 36.0613L26 31.1206L15.5 41.6206V45.5H50C50.3978 45.5 50.7794 45.658 51.0607 45.9393C51.342 46.2206 51.5 46.6022 51.5 47Z<span class="token punctuation">"</span></span>
                                                                     <span class="token attr-name">fill</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>#8B3DFF<span class="token punctuation">"</span></span> <span class="token punctuation">/&gt;</span></span>
                                                               <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>svg</span><span class="token punctuation">&gt;</span></span>
                                                            <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
                                                            <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>h3</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>fs-4 text-truncate<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>SEO Analytics &amp; Reporting<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>h3</span><span class="token punctuation">&gt;</span></span>
                                                            <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>p</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>mb-0<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>We build a robust backlink profile through ethical &amp; effective link-build strategies.<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>p</span><span class="token punctuation">&gt;</span></span>
                                                            <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>border-top mt-4<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                                                               <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>a</span> <span class="token attr-name">href</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>#<span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>text-reset mt-3 d-inline-flex gap-2 align-items-center icon-link icon-link-hover<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                                                                  SEO Analytics Service

                                                                  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>svg</span> <span class="token attr-name">xmlns</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>http://www.w3.org/2000/svg<span class="token punctuation">"</span></span> <span class="token attr-name">width</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>16<span class="token punctuation">"</span></span> <span class="token attr-name">height</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>16<span class="token punctuation">"</span></span> <span class="token attr-name">fill</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>currentColor<span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>bi bi-arrow-right<span class="token punctuation">"</span></span> <span class="token attr-name">viewBox</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>0 0 16 16<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
                                                                     <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>path</span>
                                                                        <span class="token attr-name">fill-rule</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>evenodd<span class="token punctuation">"</span></span>
                                                                        <span class="token attr-name">d</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>M1 8a.5.5 0 0 1 .5-.5h11.793l-3.147-3.146a.5.5 0 0 1 .708-.708l4 4a.5.5 0 0 1 0 .708l-4 4a.5.5 0 0 1-.708-.708L13.293 8.5H1.5A.5.5 0 0 1 1 8<span class="token punctuation">"</span></span> <span class="token punctuation">/&gt;</span></span>
                                                                  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>svg</span><span class="token punctuation">&gt;</span></span>
                                                               <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>a</span><span class="token punctuation">&gt;</span></span>
                                                            <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
                                                         <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
                                                      <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
                                                   <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
                                                <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
                                             <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
                                          <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
                                       <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
                                    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>section</span><span class="token punctuation">&gt;</span></span></code></pre>
                           </div>
                        </div>
                     </div>
                  </div>
               </div>
            </div>
         </section>
      </main>
      <footer class="pt-7">
   <div class="container">
      <!-- Footer 4 column -->
      <div class="row">
         <div class="col-lg-9 col-12">
            <div class="row" id="ft-links">
               <div class="col-lg-3 col-12">
                  <div class="position-relative">
                     <div class="mb-3 pb-2 d-flex justify-content-between border-bottom border-bottom-lg-0">
                        <h4>Service</h4>
                        <a class="d-block d-lg-none stretched-link text-body" data-bs-toggle="collapse" href="#collapseLanding" role="button" aria-expanded="true" aria-controls="collapseLanding">
                           <i class="bi bi-chevron-down"></i>
                        </a>
                     </div>
                     <div class="d-lg-block collapse show" id="collapseLanding" data-bs-parent="#ft-links" style="">
                        <ul class="list-unstyled mb-0 py-3 py-lg-0">
                           <li class="mb-2">
                              <a href="./index.html" class="text-decoration-none text-reset">Web App Development</a>
                           </li>
                           <li class="mb-2">
                              <a href="#!" class="text-decoration-none text-reset">Front End Development</a>
                           </li>
                           <li class="mb-2">
                              <a href="#!" class="text-decoration-none text-reset">MVP Development</a>
                           </li>
                           <li class="mb-2">
                              <a href="#!" class="text-decoration-none text-reset">Digital Marketing</a>
                           </li>
                           <li class="mb-2">
                              <a href="#!" class="text-decoration-none text-reset">Content Writing</a>
                           </li>
                        </ul>
                     </div>
                  </div>
               </div>
               <div class="col-lg-3 col-12">
                  <div>
                     <div class="mb-3 pb-2 d-flex justify-content-between border-bottom border-bottom-lg-0 position-relative">
                        <h4>About us</h4>
                        <a
                           class="d-block d-lg-none stretched-link text-body collapsed"
                           data-bs-toggle="collapse"
                           href="#collapseAccounts"
                           role="button"
                           aria-expanded="false"
                           aria-controls="collapseAccounts">
                           <i class="bi bi-chevron-down"></i>
                        </a>
                     </div>
                     <div class="collapse d-lg-block" id="collapseAccounts" data-bs-parent="#ft-links">
                        <ul class="list-unstyled mb-0 py-3 py-lg-0">
                           <li class="mb-2">
                              <a href="#!" class="text-decoration-none text-reset">Case Studies</a>
                           </li>
                           <li class="mb-2">
                              <a href="#!" class="text-decoration-none text-reset">Blog</a>
                           </li>
                           <li class="mb-2">
                              <a href="#!" class="text-decoration-none text-reset">Services</a>
                           </li>
                           <li class="mb-2">
                              <a href="#!" class="text-decoration-none text-reset">About</a>
                           </li>
                           <li class="mb-2">
                              <a href="#!" class="text-decoration-none text-reset">Career</a>
                           </li>
                        </ul>
                     </div>
                  </div>
               </div>
               <div class="col-lg-3 col-12">
                  <div class="mb-3 pb-2 d-flex justify-content-between border-bottom border-bottom-lg-0 position-relative">
                     <h4>Technology</h4>
                     <a
                        class="d-block d-lg-none stretched-link text-body collapsed"
                        data-bs-toggle="collapse"
                        href="#collapseResources"
                        role="button"
                        aria-expanded="false"
                        aria-controls="collapseResources">
                        <i class="bi bi-chevron-down"></i>
                     </a>
                  </div>
                  <div class="collapse d-lg-block" id="collapseResources" data-bs-parent="#ft-links">
                     <ul class="list-unstyled mb-0 py-3 py-lg-0">
                        <li class="mb-2">
                           <a href="./docs/index.html" class="text-decoration-none text-reset">Next.js</a>
                        </li>
                        <li class="mb-2">
                           <a href="#!" class="text-decoration-none text-reset">Sanity</a>
                        </li>
                        <li class="mb-2">
                           <a href="./changelog.html" class="text-decoration-none text-reset">Content ful</a>
                        </li>
                        <li class="mb-2">
                           <a href="#!" class="text-decoration-none text-reset">Vercel</a>
                        </li>
                        <li class="mb-2">
                           <a href="#!" class="text-decoration-none text-reset">Netlify</a>
                        </li>
                     </ul>
                  </div>
               </div>
               <div class="col-lg-3 col-12">
                  <div class="mb-3 pb-2 d-flex justify-content-between border-bottom border-bottom-lg-0 position-relative">
                     <h4>Locations</h4>
                     <a
                        class="d-block d-lg-none stretched-link text-body collapsed"
                        data-bs-toggle="collapse"
                        href="#collapseLocations"
                        role="button"
                        aria-expanded="false"
                        aria-controls="collapseLocations">
                        <i class="bi bi-chevron-down"></i>
                     </a>
                  </div>
                  <div class="collapse d-lg-block" id="collapseLocations" data-bs-parent="#ft-links">
                     <ul class="list-unstyled mb-0 py-3 py-lg-0">
                        <li class="mb-2">
                           <a href="./docs/index.html" class="text-decoration-none text-reset">India</a>
                        </li>
                        <li class="mb-2">
                           <a href="#!" class="text-decoration-none text-reset">Australia</a>
                        </li>
                        <li class="mb-2">
                           <a href="./changelog.html" class="text-decoration-none text-reset">Brazil</a>
                        </li>
                        <li class="mb-2">
                           <a href="#!" class="text-decoration-none text-reset">Canada</a>
                        </li>
                     </ul>
                  </div>
               </div>
            </div>
         </div>
         <div class="col-lg-3 col-12">
            <div class="me-7">
               <h4 class="mb-4">Headquarters</h4>
               <p class="text-body-secondary">Codescandy, 412, Residency Rd, Shanthala Nagar, Ashok Nagar, Bengaluru, Karnataka, India 560025</p>
            </div>
         </div>
      </div>
   </div>
   <div class="container mt-7 pt-lg-7 pb-4">
      <div class="row align-items-center">
         <div class="col-md-3">
            <a class="mb-4 mb-lg-0 d-block text-inverse" href="../index.html"><img src="./assets/images/logo/logo.svg" alt="" /></a>
         </div>
         <div class="col-md-9 col-lg-6">
            <div class="small mb-3 mb-lg-0 text-lg-center">
               Copyright © 2024

               <span class="text-primary"><a href="#">Block Bootstrap 5 Theme</a></span>
               | Designed by
               <span class="text-primary"><a href="#">CodesCandy</a></span>
            </div>
         </div>
         <div class="col-lg-3">
            <div class="text-lg-end d-flex align-items-center justify-content-lg-end">
               <div class="dropdown">
                  <button class="btn btn-light btn-icon rounded-circle d-flex align-items-center" type="button" aria-expanded="false" data-bs-toggle="dropdown" aria-label="Toggle theme (auto)">
                     <i class="bi theme-icon-active lh-1"><i class="bi theme-icon bi-sun-fill"></i></i>
                     <span class="visually-hidden bs-theme-text">Toggle theme</span>
                  </button>
                  <ul class="dropdown-menu dropdown-menu-end shadow" aria-labelledby="bs-theme-text">
                     <li>
                        <button type="button" class="dropdown-item d-flex align-items-center active" data-bs-theme-value="light" aria-pressed="true">
                           <i class="bi theme-icon bi-sun-fill"></i>
                           <span class="ms-2">Light</span>
                        </button>
                     </li>
                     <li>
                        <button type="button" class="dropdown-item d-flex align-items-center" data-bs-theme-value="dark" aria-pressed="false">
                           <i class="bi theme-icon bi-moon-stars-fill"></i>
                           <span class="ms-2">Dark</span>
                        </button>
                     </li>
                     <li>
                        <button type="button" class="dropdown-item d-flex align-items-center" data-bs-theme-value="auto" aria-pressed="false">
                           <i class="bi theme-icon bi-circle-half"></i>
                           <span class="ms-2">Auto</span>
                        </button>
                     </li>
                  </ul>
               </div>
               <div class="ms-3 d-flex gap-2">
                  <a href="#!" class="btn btn-instagram btn-light btn-icon">
                     <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-instagram" viewBox="0 0 16 16">
                        <path
                           d="M8 0C5.829 0 5.556.01 4.703.048 3.85.088 3.269.222 2.76.42a3.917 3.917 0 0 0-1.417.923A3.927 3.927 0 0 0 .42 2.76C.222 3.268.087 3.85.048 4.7.01 5.555 0 5.827 0 8.001c0 2.172.01 2.444.048 3.297.04.852.174 1.433.372 1.942.205.526.478.972.923 1.417.444.445.89.719 1.416.923.51.198 1.09.333 1.942.372C5.555 15.99 5.827 16 8 16s2.444-.01 3.298-.048c.851-.04 1.434-.174 1.943-.372a3.916 3.916 0 0 0 1.416-.923c.445-.445.718-.891.923-1.417.197-.509.332-1.09.372-1.942C15.99 10.445 16 10.173 16 8s-.01-2.445-.048-3.299c-.04-.851-.175-1.433-.372-1.941a3.926 3.926 0 0 0-.923-1.417A3.911 3.911 0 0 0 13.24.42c-.51-.198-1.092-.333-1.943-.372C10.443.01 10.172 0 7.998 0h.003zm-.717 1.442h.718c2.136 0 2.389.007 3.232.046.78.035 1.204.166 1.486.275.373.145.64.319.92.599.28.28.453.546.598.92.11.281.24.705.275 1.485.039.843.047 1.096.047 3.231s-.008 2.389-.047 3.232c-.035.78-.166 1.203-.275 1.485a2.47 2.47 0 0 1-.599.919c-.28.28-.546.453-.92.598-.28.11-.704.24-1.485.276-.843.038-1.096.047-3.232.047s-2.39-.009-3.233-.047c-.78-.036-1.203-.166-1.485-.276a2.478 2.478 0 0 1-.92-.598 2.48 2.48 0 0 1-.6-.92c-.109-.281-.24-.705-.275-1.485-.038-.843-.046-1.096-.046-3.233 0-2.136.008-2.388.046-3.231.036-.78.166-1.204.276-1.486.145-.373.319-.64.599-.92.28-.28.546-.453.92-.598.282-.11.705-.24 1.485-.276.738-.034 1.024-.044 2.515-.045v.002zm4.988 1.328a.96.96 0 1 0 0 1.92.96.96 0 0 0 0-1.92zm-4.27 1.122a4.109 4.109 0 1 0 0 8.217 4.109 4.109 0 0 0 0-8.217zm0 1.441a2.667 2.667 0 1 1 0 5.334 2.667 2.667 0 0 1 0-5.334z"></path>
                     </svg>
                  </a>
                  <a href="#!" class="btn btn-facebook btn-icon">
                     <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-facebook" viewBox="0 0 16 16">
                        <path
                           d="M16 8.049c0-4.446-3.582-8.05-8-8.05C3.58 0-.002 3.603-.002 8.05c0 4.017 2.926 7.347 6.75 7.951v-5.625h-2.03V8.05H6.75V6.275c0-2.017 1.195-3.131 3.022-3.131.876 0 1.791.157 1.791.157v1.98h-1.009c-.993 0-1.303.621-1.303 1.258v1.51h2.218l-.354 2.326H9.25V16c3.824-.604 6.75-3.934 6.75-7.951z"></path>
                     </svg>
                  </a>
                  <a href="#!" class="btn btn-twitter btn-icon">
                     <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-twitter" viewBox="0 0 16 16">
                        <path
                           d="M5.026 15c6.038 0 9.341-5.003 9.341-9.334 0-.14 0-.282-.006-.422A6.685 6.685 0 0 0 16 3.542a6.658 6.658 0 0 1-1.889.518 3.301 3.301 0 0 0 1.447-1.817 6.533 6.533 0 0 1-2.087.793A3.286 3.286 0 0 0 7.875 6.03a9.325 9.325 0 0 1-6.767-3.429 3.289 3.289 0 0 0 1.018 4.382A3.323 3.323 0 0 1 .64 6.575v.045a3.288 3.288 0 0 0 2.632 3.218 3.203 3.203 0 0 1-.865.115 3.23 3.23 0 0 1-.614-.057 3.283 3.283 0 0 0 3.067 2.277A6.588 6.588 0 0 1 .78 13.58a6.32 6.32 0 0 1-.78-.045A9.344 9.344 0 0 0 5.026 15z"></path>
                     </svg>
                  </a>
               </div>
            </div>
         </div>
      </div>
   </div>
</footer>
 <div class="btn-scroll-top">
   <svg class="progress-square svg-content" width="100%" height="100%" viewBox="0 0 40 40">
      <path d="M8 1H32C35.866 1 39 4.13401 39 8V32C39 35.866 35.866 39 32 39H8C4.13401 39 1 35.866 1 32V8C1 4.13401 4.13401 1 8 1Z" />
   </svg>
</div>
 <!-- Libs JS -->
<script src="../assets/libs/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
<script src="../assets/libs/simplebar/dist/simplebar.min.js"></script>
<script src="../assets/libs/headhesive/dist/headhesive.min.js"></script>

<!-- Theme JS -->
<script src="../assets/js/theme.min.js"></script>

      <script src="../assets/libs/prismjs/prism.js"></script>
      <script src="../assets/libs/prismjs/components/prism-scss.min.js"></script>
      <script src="../assets/libs/prismjs/plugins/toolbar/prism-toolbar.min.js"></script>
      <script src="../assets/libs/prismjs/plugins/copy-to-clipboard/prism-copy-to-clipboard.min.js"></script>
      <script src="../assets/libs/swiper/swiper-bundle.min.js"></script>
      <script src="../assets/js/vendors/swiper.js"></script>
   </body>
</html>
