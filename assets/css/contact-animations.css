/* Enhanced Contact Page Animations */

/* Base animation states - hidden by default */
[data-cue] {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    will-change: transform, opacity;
}

/* Animation states when elements come into view */
[data-cue].cue-fade-in {
    opacity: 1;
    transform: translateY(0);
}

[data-cue="zoomIn"] {
    opacity: 0;
    transform: scale(0.8) translateY(20px);
    transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

[data-cue="zoomIn"].cue-fade-in {
    opacity: 1;
    transform: scale(1) translateY(0);
}

[data-cue="slideInUp"] {
    opacity: 0;
    transform: translateY(50px);
    transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

[data-cue="slideInUp"].cue-fade-in {
    opacity: 1;
    transform: translateY(0);
}

[data-cue="slideInLeft"] {
    opacity: 0;
    transform: translateX(-50px);
    transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

[data-cue="slideInLeft"].cue-fade-in {
    opacity: 1;
    transform: translateX(0);
}

[data-cue="fadeIn"] {
    opacity: 0;
    transition: all 0.6s ease-out;
}

[data-cue="fadeIn"].cue-fade-in {
    opacity: 1;
}

/* Smooth hover transitions for contact cards */
.card {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    transform: translateY(0);
}

.card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(139, 61, 255, 0.15) !important;
    border-color: rgba(139, 61, 255, 0.2) !important;
}

/* Enhanced icon animations on hover */
.card:hover .text-primary {
    color: #8b3dff !important;
    transform: scale(1.1);
    transition: all 0.3s ease;
}

.card .text-primary {
    transition: all 0.3s ease;
}

/* Smooth link hover effects */
.card a {
    transition: all 0.3s ease;
    position: relative;
}

.card a:hover {
    color: #6f31cc !important;
    text-decoration: none;
}

/* Add subtle pulse animation to primary support card */
.bg-opacity-10 {
    animation: subtle-pulse 3s ease-in-out infinite;
}

@keyframes subtle-pulse {
    0%, 100% {
        box-shadow: 0 0 0 0 rgba(139, 61, 255, 0.1);
    }
    50% {
        box-shadow: 0 0 0 10px rgba(139, 61, 255, 0.05);
    }
}

/* Enhanced FAQ accordion animations */
.accordion-item {
    transition: all 0.3s ease;
    border: 1px solid rgba(139, 61, 255, 0.1) !important;
    margin-bottom: 0.5rem;
    border-radius: 0.5rem !important;
    overflow: hidden;
}

.accordion-item:hover {
    border-color: rgba(139, 61, 255, 0.3) !important;
    box-shadow: 0 4px 12px rgba(139, 61, 255, 0.1);
}

.accordion-button {
    transition: all 0.3s ease;
    border: none !important;
    background: transparent !important;
}

.accordion-button:not(.collapsed) {
    background: linear-gradient(135deg, rgba(139, 61, 255, 0.05), rgba(139, 61, 255, 0.1)) !important;
    color: #8b3dff !important;
}

.accordion-button:focus {
    box-shadow: 0 0 0 0.25rem rgba(139, 61, 255, 0.25) !important;
}

/* Smooth section transitions */
section {
    transition: all 0.3s ease;
}

/* Enhanced button hover effects */
.btn {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn:hover {
    transform: translateY(-2px);
}

/* Improved scroll animations for better performance */
[data-cue] {
    will-change: transform, opacity;
}

/* Custom stagger delays for contact cards */
.row.g-3 .col-lg-4:nth-child(1) [data-cue] {
    transition-delay: 0.1s;
}

.row.g-3 .col-lg-4:nth-child(2) [data-cue] {
    transition-delay: 0.2s;
}

.row.g-3 .col-lg-4:nth-child(3) [data-cue] {
    transition-delay: 0.3s;
}

.row.g-3 .col-lg-4:nth-child(4) [data-cue] {
    transition-delay: 0.4s;
}

.row.g-3 .col-lg-4:nth-child(5) [data-cue] {
    transition-delay: 0.5s;
}

.row.g-3 .col-lg-4:nth-child(6) [data-cue] {
    transition-delay: 0.6s;
}

/* Stagger delays for FAQ items */
.accordion-item:nth-child(1) {
    transition-delay: 0.1s;
}

.accordion-item:nth-child(2) {
    transition-delay: 0.2s;
}

.accordion-item:nth-child(3) {
    transition-delay: 0.3s;
}

.accordion-item:nth-child(4) {
    transition-delay: 0.4s;
}

/* Enhanced mobile responsiveness for animations */
@media (max-width: 768px) {
    .card:hover {
        transform: translateY(-4px);
    }
    
    .bg-opacity-10 {
        animation: none; /* Disable pulse on mobile for better performance */
    }
    
    /* Reduce motion for mobile users who prefer it */
    [data-cue] {
        transition: all 0.4s ease-out;
    }
    
    /* Faster animations on mobile */
    [data-cue="slideInUp"] {
        transform: translateY(30px);
    }
    
    [data-cue="slideInLeft"] {
        transform: translateX(-30px);
    }
}

/* Respect user's motion preferences */
@media (prefers-reduced-motion: reduce) {
    [data-cue],
    .card,
    .btn,
    .accordion-item {
        transition: none !important;
        animation: none !important;
    }
    
    [data-cue] {
        opacity: 1 !important;
        transform: none !important;
    }
}

/* Improve performance with GPU acceleration */
[data-cue],
.card,
.btn {
    transform-style: preserve-3d;
    backface-visibility: hidden;
}

/* Smooth page load animation */
body {
    animation: pageLoad 0.8s ease-out;
}

@keyframes pageLoad {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

/* Loading state for elements before ScrollCue initializes */
.scroll-loading [data-cue] {
    opacity: 0;
    transform: translateY(20px);
}
