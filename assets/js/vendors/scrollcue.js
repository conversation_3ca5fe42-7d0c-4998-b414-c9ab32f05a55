// Initialize ScrollCue with optimized settings for better scroll animations
scrollCue.init({
    interval: -100, // Trigger animations slightly before element comes into view
    duration: 600, // Faster animation duration for better UX
    percentage: 0.8, // Trigger when 80% of element is visible
    enable: true,
    docSlider: false,
    pageChangeReset: true // Reset animations on page changes
});

// Update ScrollCue on page load and resize
scrollCue.update();

// Re-trigger animations on window resize for responsive behavior
window.addEventListener('resize', function() {
    setTimeout(() => {
        scrollCue.update();
    }, 100);
});

// Additional trigger for dynamic content
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(() => {
        scrollCue.update();
    }, 100);
});

// Handle scroll events for better performance
let scrollTimeout;
window.addEventListener('scroll', function() {
    if (scrollTimeout) {
        clearTimeout(scrollTimeout);
    }
    scrollTimeout = setTimeout(() => {
        scrollCue.update();
    }, 10);
});

// Intersection Observer fallback for better browser support
if ('IntersectionObserver' in window) {
    const animatedElements = document.querySelectorAll('[data-cue]');
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('cue-fade-in');
                observer.unobserve(entry.target);
            }
        });
    }, {
        threshold: 0.1,
        rootMargin: '50px'
    });

    animatedElements.forEach(element => {
        observer.observe(element);
    });
}